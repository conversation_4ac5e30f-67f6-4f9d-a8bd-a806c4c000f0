<div class="Polaris-Page">
  <div class="Polaris-Page__Content">
    <div
      class="Polaris-BlockStack"
      style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400)"
    >
      <div
        class="Polaris-BlockStack"
        style="
          --pc-block-stack-align: center;
          --pc-block-stack-order: column;
          --pc-block-stack-gap-xs: var(--p-space-200);
        "
      >
        <h1 class="Polaris-Text--root Polaris-Text--heading2xl">Welcome to StoreSEO</h1>
        <p class="Polaris-Text--root Polaris-Text--bodyLg">
          Drive sales and traffic with the power of StoreSEO and improve your search engine ranking
        </p>
      </div>
      <div
        class="Polaris-BlockStack"
        style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400)"
      >
        <div
          class="Polaris-ShadowBevel"
          style="
            --pc-shadow-bevel-z-index: 32;
            --pc-shadow-bevel-content-xs: &quot;&quot;;
            --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100);
            --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);
          "
        >
          <div
            class="Polaris-Box"
            style="
              --pc-box-background: var(--p-color-bg-surface);
              --pc-box-min-height: 100%;
              --pc-box-overflow-x: clip;
              --pc-box-overflow-y: clip;
              --pc-box-padding-block-start-xs: var(--p-space-400);
              --pc-box-padding-block-end-xs: var(--p-space-400);
              --pc-box-padding-inline-start-xs: var(--p-space-400);
              --pc-box-padding-inline-end-xs: var(--p-space-400);
            "
          >
            <div
              class="Polaris-BlockStack"
              style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400)"
            >
              <div
                class="Polaris-InlineStack"
                style="
                  --pc-inline-stack-align: space-between;
                  --pc-inline-stack-block-align: center;
                  --pc-inline-stack-wrap: wrap;
                  --pc-inline-stack-gap-xs: var(--p-space-400);
                  --pc-inline-stack-flex-direction-xs: row;
                "
              >
                <div
                  class="Polaris-InlineStack"
                  style="
                    --pc-inline-stack-wrap: wrap;
                    --pc-inline-stack-gap-xs: var(--p-space-200);
                    --pc-inline-stack-flex-direction-xs: row;
                  "
                >
                  <h3 class="Polaris-Text--root Polaris-Text--headingLg">Store Details</h3>
                </div>
                <button
                  class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--disabled"
                  type="button"
                  aria-disabled="true"
                >
                  <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">View reports</span>
                </button>
              </div>
              <div
                class="Polaris-Grid"
                style="
                  --pc-grid-columns-xs: 1;
                  --pc-grid-columns-sm: 2;
                  --pc-grid-columns-md: 2;
                  --pc-grid-columns-lg: 4;
                  --pc-grid-columns-xl: 4;
                "
              >
                <div class="Polaris-Grid-Cell">
                  <div
                    class="Polaris-ShadowBevel"
                    style="
                      --pc-shadow-bevel-z-index: 32;
                      --pc-shadow-bevel-content-xs: &quot;&quot;;
                      --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100);
                      --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);
                    "
                  >
                    <div
                      class="Polaris-Box"
                      style="
                        --pc-box-background: var(--p-color-bg-surface);
                        --pc-box-min-height: 100%;
                        --pc-box-overflow-x: clip;
                        --pc-box-overflow-y: clip;
                        --pc-box-padding-block-start-xs: var(--p-space-400);
                        --pc-box-padding-block-end-xs: var(--p-space-400);
                        --pc-box-padding-inline-start-xs: var(--p-space-400);
                        --pc-box-padding-inline-end-xs: var(--p-space-400);
                      "
                    >
                      <div
                        class="Polaris-InlineStack"
                        style="
                          --pc-inline-stack-align: space-between;
                          --pc-inline-stack-block-align: center;
                          --pc-inline-stack-wrap: wrap;
                          --pc-inline-stack-gap-xs: var(--p-space-200);
                          --pc-inline-stack-flex-direction-xs: row;
                        "
                      >
                        <div style="flex: 1 1 0%">
                          <div
                            class="Polaris-BlockStack"
                            style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400)"
                          >
                            <div
                              class="Polaris-InlineStack"
                              style="
                                --pc-inline-stack-align: start;
                                --pc-inline-stack-block-align: center;
                                --pc-inline-stack-wrap: wrap;
                                --pc-inline-stack-gap-xs: var(--p-space-100);
                                --pc-inline-stack-flex-direction-xs: row;
                              "
                            >
                              <p class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--semibold">Products</p>
                              <div
                                tabindex="-1"
                                aria-controls=":r1j:"
                                aria-owns=":r1j:"
                                aria-expanded="false"
                                data-state="closed"
                              >
                                <span style="cursor: pointer"
                                  ><span class="Polaris-Icon Polaris-Icon--toneBase"
                                    ><svg
                                      viewBox="0 0 20 20"
                                      class="Polaris-Icon__Svg"
                                      focusable="false"
                                      aria-hidden="true"
                                    >
                                      <path
                                        d="M10 14a.75.75 0 0 1-.75-.75v-3.5a.75.75 0 0 1 1.5 0v3.5a.75.75 0 0 1-.75.75Z"
                                      ></path>
                                      <path d="M9 7a1 1 0 1 1 2 0 1 1 0 0 1-2 0Z"></path>
                                      <path
                                        fill-rule="evenodd"
                                        d="M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Zm-1.5 0a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0Z"
                                      ></path></svg></span
                                ></span>
                              </div>
                            </div>
                            <div
                              class="Polaris-BlockStack"
                              style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-025)"
                            >
                              <div class="skeleton-wrapper">
                                <div
                                  class="skeleton-box"
                                  style="width: 40px; height: 22px"
                                ></div>
                              </div>
                              <p class="Polaris-Text--root Polaris-Text--bodySm">Total products</p>
                            </div>
                          </div>
                        </div>
                        <div class="skeleton-wrapper">
                          <div
                            class="skeleton-box"
                            style="width: 75px; height: 75px; border-radius: 50px"
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="Polaris-Grid-Cell">
                  <div
                    class="Polaris-ShadowBevel"
                    style="
                      --pc-shadow-bevel-z-index: 32;
                      --pc-shadow-bevel-content-xs: &quot;&quot;;
                      --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100);
                      --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);
                    "
                  >
                    <div
                      class="Polaris-Box"
                      style="
                        --pc-box-background: var(--p-color-bg-surface);
                        --pc-box-min-height: 100%;
                        --pc-box-overflow-x: clip;
                        --pc-box-overflow-y: clip;
                        --pc-box-padding-block-start-xs: var(--p-space-400);
                        --pc-box-padding-block-end-xs: var(--p-space-400);
                        --pc-box-padding-inline-start-xs: var(--p-space-400);
                        --pc-box-padding-inline-end-xs: var(--p-space-400);
                      "
                    >
                      <div
                        class="Polaris-InlineStack"
                        style="
                          --pc-inline-stack-align: space-between;
                          --pc-inline-stack-block-align: center;
                          --pc-inline-stack-wrap: wrap;
                          --pc-inline-stack-gap-xs: var(--p-space-200);
                          --pc-inline-stack-flex-direction-xs: row;
                        "
                      >
                        <div style="flex: 1 1 0%">
                          <div
                            class="Polaris-BlockStack"
                            style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400)"
                          >
                            <div
                              class="Polaris-InlineStack"
                              style="
                                --pc-inline-stack-align: start;
                                --pc-inline-stack-block-align: center;
                                --pc-inline-stack-wrap: wrap;
                                --pc-inline-stack-gap-xs: var(--p-space-100);
                                --pc-inline-stack-flex-direction-xs: row;
                              "
                            >
                              <p class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--semibold">Collections</p>
                              <div
                                tabindex="-1"
                                aria-controls=":r1j:"
                                aria-owns=":r1j:"
                                aria-expanded="false"
                                data-state="closed"
                              >
                                <span style="cursor: pointer"
                                  ><span class="Polaris-Icon Polaris-Icon--toneBase"
                                    ><svg
                                      viewBox="0 0 20 20"
                                      class="Polaris-Icon__Svg"
                                      focusable="false"
                                      aria-hidden="true"
                                    >
                                      <path
                                        d="M10 14a.75.75 0 0 1-.75-.75v-3.5a.75.75 0 0 1 1.5 0v3.5a.75.75 0 0 1-.75.75Z"
                                      ></path>
                                      <path d="M9 7a1 1 0 1 1 2 0 1 1 0 0 1-2 0Z"></path>
                                      <path
                                        fill-rule="evenodd"
                                        d="M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Zm-1.5 0a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0Z"
                                      ></path></svg></span
                                ></span>
                              </div>
                            </div>
                            <div
                              class="Polaris-BlockStack"
                              style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-025)"
                            >
                              <div class="skeleton-wrapper">
                                <div
                                  class="skeleton-box"
                                  style="width: 40px; height: 22px"
                                ></div>
                              </div>
                              <p class="Polaris-Text--root Polaris-Text--bodySm">Total collections</p>
                            </div>
                          </div>
                        </div>
                        <div class="skeleton-wrapper">
                          <div
                            class="skeleton-box"
                            style="width: 75px; height: 75px; border-radius: 50px"
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="Polaris-Grid-Cell">
                  <div
                    class="Polaris-ShadowBevel"
                    style="
                      --pc-shadow-bevel-z-index: 32;
                      --pc-shadow-bevel-content-xs: &quot;&quot;;
                      --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100);
                      --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);
                    "
                  >
                    <div
                      class="Polaris-Box"
                      style="
                        --pc-box-background: var(--p-color-bg-surface);
                        --pc-box-min-height: 100%;
                        --pc-box-overflow-x: clip;
                        --pc-box-overflow-y: clip;
                        --pc-box-padding-block-start-xs: var(--p-space-400);
                        --pc-box-padding-block-end-xs: var(--p-space-400);
                        --pc-box-padding-inline-start-xs: var(--p-space-400);
                        --pc-box-padding-inline-end-xs: var(--p-space-400);
                      "
                    >
                      <div
                        class="Polaris-InlineStack"
                        style="
                          --pc-inline-stack-align: space-between;
                          --pc-inline-stack-block-align: center;
                          --pc-inline-stack-wrap: wrap;
                          --pc-inline-stack-gap-xs: var(--p-space-200);
                          --pc-inline-stack-flex-direction-xs: row;
                        "
                      >
                        <div style="flex: 1 1 0%">
                          <div
                            class="Polaris-BlockStack"
                            style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400)"
                          >
                            <div
                              class="Polaris-InlineStack"
                              style="
                                --pc-inline-stack-align: start;
                                --pc-inline-stack-block-align: center;
                                --pc-inline-stack-wrap: wrap;
                                --pc-inline-stack-gap-xs: var(--p-space-100);
                                --pc-inline-stack-flex-direction-xs: row;
                              "
                            >
                              <p class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--semibold">Pages</p>
                              <div
                                tabindex="-1"
                                aria-controls=":r1j:"
                                aria-owns=":r1j:"
                                aria-expanded="false"
                                data-state="closed"
                              >
                                <span style="cursor: pointer"
                                  ><span class="Polaris-Icon Polaris-Icon--toneBase"
                                    ><svg
                                      viewBox="0 0 20 20"
                                      class="Polaris-Icon__Svg"
                                      focusable="false"
                                      aria-hidden="true"
                                    >
                                      <path
                                        d="M10 14a.75.75 0 0 1-.75-.75v-3.5a.75.75 0 0 1 1.5 0v3.5a.75.75 0 0 1-.75.75Z"
                                      ></path>
                                      <path d="M9 7a1 1 0 1 1 2 0 1 1 0 0 1-2 0Z"></path>
                                      <path
                                        fill-rule="evenodd"
                                        d="M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Zm-1.5 0a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0Z"
                                      ></path></svg></span
                                ></span>
                              </div>
                            </div>
                            <div
                              class="Polaris-BlockStack"
                              style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-025)"
                            >
                              <div class="skeleton-wrapper">
                                <div
                                  class="skeleton-box"
                                  style="width: 40px; height: 22px"
                                ></div>
                              </div>
                              <p class="Polaris-Text--root Polaris-Text--bodySm">Total pages</p>
                            </div>
                          </div>
                        </div>
                        <div class="skeleton-wrapper">
                          <div
                            class="skeleton-box"
                            style="width: 75px; height: 75px; border-radius: 50px"
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="Polaris-Grid-Cell">
                  <div
                    class="Polaris-ShadowBevel"
                    style="
                      --pc-shadow-bevel-z-index: 32;
                      --pc-shadow-bevel-content-xs: &quot;&quot;;
                      --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100);
                      --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);
                    "
                  >
                    <div
                      class="Polaris-Box"
                      style="
                        --pc-box-background: var(--p-color-bg-surface);
                        --pc-box-min-height: 100%;
                        --pc-box-overflow-x: clip;
                        --pc-box-overflow-y: clip;
                        --pc-box-padding-block-start-xs: var(--p-space-400);
                        --pc-box-padding-block-end-xs: var(--p-space-400);
                        --pc-box-padding-inline-start-xs: var(--p-space-400);
                        --pc-box-padding-inline-end-xs: var(--p-space-400);
                      "
                    >
                      <div
                        class="Polaris-InlineStack"
                        style="
                          --pc-inline-stack-align: space-between;
                          --pc-inline-stack-block-align: center;
                          --pc-inline-stack-wrap: wrap;
                          --pc-inline-stack-gap-xs: var(--p-space-200);
                          --pc-inline-stack-flex-direction-xs: row;
                        "
                      >
                        <div style="flex: 1 1 0%">
                          <div
                            class="Polaris-BlockStack"
                            style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400)"
                          >
                            <div
                              class="Polaris-InlineStack"
                              style="
                                --pc-inline-stack-align: start;
                                --pc-inline-stack-block-align: center;
                                --pc-inline-stack-wrap: wrap;
                                --pc-inline-stack-gap-xs: var(--p-space-100);
                                --pc-inline-stack-flex-direction-xs: row;
                              "
                            >
                              <p class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--semibold">Blog Posts</p>
                              <div
                                tabindex="-1"
                                aria-controls=":r1j:"
                                aria-owns=":r1j:"
                                aria-expanded="false"
                                data-state="closed"
                              >
                                <span style="cursor: pointer"
                                  ><span class="Polaris-Icon Polaris-Icon--toneBase"
                                    ><svg
                                      viewBox="0 0 20 20"
                                      class="Polaris-Icon__Svg"
                                      focusable="false"
                                      aria-hidden="true"
                                    >
                                      <path
                                        d="M10 14a.75.75 0 0 1-.75-.75v-3.5a.75.75 0 0 1 1.5 0v3.5a.75.75 0 0 1-.75.75Z"
                                      ></path>
                                      <path d="M9 7a1 1 0 1 1 2 0 1 1 0 0 1-2 0Z"></path>
                                      <path
                                        fill-rule="evenodd"
                                        d="M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Zm-1.5 0a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0Z"
                                      ></path></svg></span
                                ></span>
                              </div>
                            </div>
                            <div
                              class="Polaris-BlockStack"
                              style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-025)"
                            >
                              <div class="skeleton-wrapper">
                                <div
                                  class="skeleton-box"
                                  style="width: 40px; height: 22px"
                                ></div>
                              </div>
                              <p class="Polaris-Text--root Polaris-Text--bodySm">Total blog posts</p>
                            </div>
                          </div>
                        </div>
                        <div class="skeleton-wrapper">
                          <div
                            class="skeleton-box"
                            style="width: 75px; height: 75px; border-radius: 50px"
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="Polaris-ShadowBevel"
        style="
          --pc-shadow-bevel-z-index: 32;
          --pc-shadow-bevel-content-xs: &quot;&quot;;
          --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100);
          --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);
        "
      >
        <div
          class="Polaris-Box"
          style="
            --pc-box-background: var(--p-color-bg-surface);
            --pc-box-min-height: 100%;
            --pc-box-overflow-x: clip;
            --pc-box-overflow-y: clip;
            --pc-box-padding-block-start-xs: var(--p-space-400);
            --pc-box-padding-block-end-xs: var(--p-space-400);
            --pc-box-padding-inline-start-xs: var(--p-space-400);
            --pc-box-padding-inline-end-xs: var(--p-space-400);
          "
        >
          <div
            class="Polaris-BlockStack"
            style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-300)"
          >
            <h3 class="Polaris-Text--root Polaris-Text--headingSm">Get started</h3>
            <p class="Polaris-Text--root Polaris-Text--subdued">
              Optimize your product for search engines instantly and rank them on top of the search results
            </p>
            <div>
              <button
                class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantPrimary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--disabled"
                aria-disabled="true"
                type="button"
                tabindex="-1"
              >
                <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Optimize your products</span>
              </button>
            </div>
          </div>
        </div>
      </div>

            <div
        class="Polaris-BlockStack"
        style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400)"
      >
        <div class="Polaris-LegacyCard">
          <div class="Polaris-MediaCard">
            <div class="Polaris-MediaCard__MediaContainer Polaris-MediaCard--sizeSmall">
              <div style="height: 180px; min-width: 30%">
                <div class="skeleton-wrapper">
                  <div
                    class="skeleton-box"
                    style="height: 180px; width: 100%"
                  ></div>
                </div>
              </div>
            </div>
            <div class="Polaris-MediaCard__InfoContainer Polaris-MediaCard--sizeSmall">
              <div
                class="Polaris-Box"
                style="
                  --pc-box-padding-block-start-xs: var(--p-space-500);
                  --pc-box-padding-block-end-xs: var(--p-space-500);
                  --pc-box-padding-inline-start-xs: var(--p-space-500);
                  --pc-box-padding-inline-end-xs: var(--p-space-500);
                "
              >
                <div
                  class="Polaris-BlockStack"
                  style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200)"
                >
                  <div
                    class="Polaris-InlineStack"
                    style="
                      --pc-inline-stack-align: space-between;
                      --pc-inline-stack-wrap: nowrap;
                      --pc-inline-stack-gap-xs: var(--p-space-200);
                      --pc-inline-stack-flex-direction-xs: row;
                    "
                  >
                    <div>
                      <h2 class="Polaris-Text--root Polaris-Text--headingSm">Write Well Optimized Blog with AI</h2>
                    </div>
                    <div
                      class="Polaris-Box"
                      style="
                        position: absolute;
                        --pc-box-inset-inline-end: var(--p-space-500);
                        z-index: var(--p-z-index-2);
                      "
                    >
                      <div
                        class="Polaris-InlineStack"
                        style="
                          --pc-inline-stack-wrap: nowrap;
                          --pc-inline-stack-gap-xs: var(--p-space-100);
                          --pc-inline-stack-flex-direction-xs: row;
                        "
                      >
                        <div>
                          <div
                            class="Polaris-InlineStack"
                            style="
                              --pc-inline-stack-block-align: center;
                              --pc-inline-stack-wrap: wrap;
                              --pc-inline-stack-flex-direction-xs: row;
                            "
                          >
                            <button
                              class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantTertiary Polaris-Button--sizeSlim Polaris-Button--textAlignCenter Polaris-Button--iconOnly"
                              aria-label="Actions"
                              type="button"
                              tabindex="0"
                              aria-controls=":r53:"
                              aria-owns=":r53:"
                              aria-expanded="false"
                              data-state="closed"
                            >
                              <span class="Polaris-Button__Icon"
                                ><span class="Polaris-Icon"
                                  ><svg
                                    viewBox="0 0 20 20"
                                    class="Polaris-Icon__Svg"
                                    focusable="false"
                                    aria-hidden="true"
                                  >
                                    <path d="M6 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"></path>
                                    <path d="M11.5 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"></path>
                                    <path d="M17 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"></path></svg></span
                              ></span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <p class="Polaris-Text--root Polaris-Text--bodySm">
                    Grow your online store with AI blog generation. Easily create clear, SEO-friendly content that helps
                    your products pop up in AI-driven search results and recommendations
                  </p>
                  <div class="Polaris-MediaCard__ActionContainer">
                    <div class="Polaris-ButtonGroup">
                      <div class="Polaris-ButtonGroup__Item">
                        <button
                          class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--disabled"
                          aria-disabled="true"
                          type="button"
                          tabindex="-1"
                        >
                          <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium"
                            >Wirte with AI</span
                          >
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="Polaris-ShadowBevel"
        style="
          --pc-shadow-bevel-z-index: 32;
          --pc-shadow-bevel-content-xs: &quot;&quot;;
          --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100);
          --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);
        "
      >
        <div
          class="Polaris-Box"
          style="
            --pc-box-background: var(--p-color-bg-surface);
            --pc-box-min-height: 100%;
            --pc-box-overflow-x: clip;
            --pc-box-overflow-y: clip;
            --pc-box-padding-block-start-xs: var(--p-space-400);
            --pc-box-padding-block-end-xs: var(--p-space-400);
            --pc-box-padding-inline-start-xs: var(--p-space-400);
            --pc-box-padding-inline-end-xs: var(--p-space-400);
          "
        >
          <div
            class="Polaris-BlockStack"
            style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-300)"
          >
            <p class="Polaris-Text--root Polaris-Text--subdued">
              To enable some advanced features (i.e., SEO Schema, Google verification, Noindex/Nofollow), you need to
              enable the App Embed for StoreSEO from your Shopify settings. Go to the settings page and follow the
              instructions to enable it.
            </p>
            <div>
              <button
                class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--disabled"
                aria-disabled="true"
                type="button"
                tabindex="-1"
              >
                <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">App Embed Settings</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div
        class="Polaris-ShadowBevel"
        style="
          --pc-shadow-bevel-z-index: 32;
          --pc-shadow-bevel-content-xs: &quot;&quot;;
          --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100);
          --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);
        "
      >
        <div
          class="Polaris-Box"
          style="
            --pc-box-background: var(--p-color-bg-surface);
            --pc-box-min-height: 100%;
            --pc-box-overflow-x: clip;
            --pc-box-overflow-y: clip;
            --pc-box-padding-block-start-xs: var(--p-space-400);
            --pc-box-padding-block-end-xs: var(--p-space-400);
            --pc-box-padding-inline-start-xs: var(--p-space-400);
            --pc-box-padding-inline-end-xs: var(--p-space-400);
          "
        >
          <div
            class="Polaris-Box"
            style="--pc-box-padding-block-end-xs: var(--p-space-400)"
          >
            🎉 What's New
          </div>
          <div
            class="Polaris-Bleed"
            style="
              --pc-bleed-margin-inline-start-xs: var(--p-space-400);
              --pc-bleed-margin-inline-end-xs: var(--p-space-400);
            "
          >
            <hr
              class="Polaris-Divider"
              style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary)"
            />
          </div>
          <div
            class="Polaris-BlockStack"
            style="--pc-block-stack-order: column"
          >
            <div
              class="Polaris-Box"
              height="160px"
              style="
                --pc-box-padding-block-start-xs: var(--p-space-400);
                --pc-box-width: 70%;
                --pc-box-padding-block-end-xs: var(--p-space-400);
              "
            >
              <div class="Polaris-LegacyCard">
                <div class="Polaris-MediaCard">
                  <div class="Polaris-MediaCard__MediaContainer">
                    <div style="height: 160px; min-width: 30%">
                      <div class="skeleton-wrapper">
                        <div
                          class="skeleton-box"
                          style="height: 180px; width: 100%"
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div class="Polaris-MediaCard__InfoContainer">
                    <div
                      class="Polaris-Box"
                      style="
                        --pc-box-padding-block-start-xs: var(--p-space-500);
                        --pc-box-padding-block-end-xs: var(--p-space-500);
                        --pc-box-padding-inline-start-xs: var(--p-space-500);
                        --pc-box-padding-inline-end-xs: var(--p-space-500);
                      "
                    >
                      <div
                        class="Polaris-BlockStack"
                        style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200)"
                      >
                        <div
                          class="Polaris-InlineStack"
                          style="
                            --pc-inline-stack-align: space-between;
                            --pc-inline-stack-wrap: nowrap;
                            --pc-inline-stack-gap-xs: var(--p-space-200);
                            --pc-inline-stack-flex-direction-xs: row;
                          "
                        >
                          <div>
                            <h2 class="Polaris-Text--root Polaris-Text--headingSm">
                              Boost the Visibility of Your Business in Local Search
                            </h2>
                          </div>
                        </div>
                        <p class="Polaris-Text--root Polaris-Text--bodySm">
                          Optimize your Google Business Profile with accurate information and engaging photos to enhance
                          your local search presence.
                        </p>
                        <div class="Polaris-MediaCard__ActionContainer">
                          <div class="Polaris-ButtonGroup">
                            <div class="Polaris-ButtonGroup__Item">
                              <div>
                                <a
                                  data-polaris-unstyled="true"
                                  class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter"
                                  url="/local-seo"
                                  href="/local-seo"
                                  ><span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium"
                                    >Check it out</span
                                  ></a
                                >
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="Polaris-Bleed"
              style="
                --pc-bleed-margin-block-end-xs: var(--p-space-400);
                --pc-bleed-margin-inline-start-xs: var(--p-space-400);
                --pc-bleed-margin-inline-end-xs: var(--p-space-400);
              "
            >
              <div
                class="Polaris-Box"
                style="
                  --pc-box-background: var(--p-color-bg-surface-secondary);
                  --pc-box-padding-block-start-xs: var(--p-space-100);
                  --pc-box-padding-block-end-xs: var(--p-space-100);
                  --pc-box-padding-inline-start-xs: var(--p-space-100);
                  --pc-box-padding-inline-end-xs: var(--p-space-300);
                "
              >
                <div
                  class="Polaris-InlineStack"
                  style="
                    --pc-inline-stack-align: end;
                    --pc-inline-stack-wrap: wrap;
                    --pc-inline-stack-flex-direction-xs: row;
                  "
                >
                  <nav
                    aria-label="Pagination"
                    class="Polaris-Pagination"
                  >
                    <div
                      class="Polaris-ButtonGroup Polaris-ButtonGroup--variantSegmented"
                      data-buttongroup-variant="segmented"
                    >
                      <div class="Polaris-ButtonGroup__Item">
                        <button
                          id="previousURL"
                          class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--iconOnly"
                          aria-label="Previous"
                          type="button"
                        >
                          <span class="Polaris-Button__Icon"
                            ><span class="Polaris-Icon"
                              ><svg
                                viewBox="0 0 20 20"
                                class="Polaris-Icon__Svg"
                                focusable="false"
                                aria-hidden="true"
                              >
                                <path
                                  fill-rule="evenodd"
                                  d="M11.764 5.204a.75.75 0 0 1 .032 1.06l-3.516 3.736 3.516 3.736a.75.75 0 1 1-1.092 1.028l-4-4.25a.75.75 0 0 1 0-1.028l4-4.25a.75.75 0 0 1 1.06-.032Z"
                                ></path></svg></span
                          ></span>
                        </button>
                      </div>
                      <div class="Polaris-ButtonGroup__Item">
                        <button
                          id="nextURL"
                          class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--iconOnly"
                          aria-label="Next"
                          type="button"
                        >
                          <span class="Polaris-Button__Icon"
                            ><span class="Polaris-Icon"
                              ><svg
                                viewBox="0 0 20 20"
                                class="Polaris-Icon__Svg"
                                focusable="false"
                                aria-hidden="true"
                              >
                                <path
                                  fill-rule="evenodd"
                                  d="M7.72 14.53a.75.75 0 0 1 0-1.06l3.47-3.47-3.47-3.47a.75.75 0 0 1 1.06-1.06l4 4a.75.75 0 0 1 0 1.06l-4 4a.75.75 0 0 1-1.06 0Z"
                                ></path></svg></span
                          ></span>
                        </button>
                      </div>
                    </div>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="Polaris-ShadowBevel"
        style="
          --pc-shadow-bevel-z-index: 32;
          --pc-shadow-bevel-content-xs: &quot;&quot;;
          --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100);
          --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);
        "
      >
        <div
          class="Polaris-Box"
          style="
            --pc-box-background: var(--p-color-bg-surface);
            --pc-box-min-height: 100%;
            --pc-box-overflow-x: clip;
            --pc-box-overflow-y: clip;
            --pc-box-padding-block-start-xs: var(--p-space-0);
            --pc-box-padding-block-end-xs: var(--p-space-0);
            --pc-box-padding-inline-start-xs: var(--p-space-0);
            --pc-box-padding-inline-end-xs: var(--p-space-0);
          "
        >
          <div
            class="Polaris-InlineStack"
            style="--pc-inline-stack-wrap: nowrap; --pc-inline-stack-flex-direction-xs: row"
          >
            <div style="height: 180px; min-width: 30%">
              <div class="skeleton-wrapper">
                <div
                  class="skeleton-box"
                  style="height: 180px; width: 100%"
                ></div>
              </div>
            </div>
            <div
              class="Polaris-Box"
              style="
                --pc-box-padding-block-start-xs: var(--p-space-400);
                --pc-box-padding-block-end-xs: var(--p-space-400);
                --pc-box-padding-inline-start-xs: var(--p-space-400);
                --pc-box-padding-inline-end-xs: var(--p-space-400);
              "
            >
              <div
                class="Polaris-BlockStack"
                style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200)"
              >
                <div
                  class="Polaris-InlineStack"
                  style="
                    --pc-inline-stack-block-align: center;
                    --pc-inline-stack-wrap: wrap;
                    --pc-inline-stack-gap-xs: var(--p-space-100);
                    --pc-inline-stack-flex-direction-xs: row;
                  "
                >
                  <div class="Polaris-Box">
                    <span class="Polaris-Icon Polaris-Icon--toneMagic"
                      ><svg
                        viewBox="0 0 20 20"
                        class="Polaris-Icon__Svg"
                        focusable="false"
                        aria-hidden="true"
                      >
                        <path
                          d="M5.702 4.253a.625.625 0 0 1 1.096 0l.196.358c.207.378.517.688.895.895l.358.196a.625.625 0 0 1 0 1.097l-.358.196a2.25 2.25 0 0 0-.895.894l-.196.359a.625.625 0 0 1-1.096 0l-.196-.359a2.25 2.25 0 0 0-.895-.894l-.358-.196a.625.625 0 0 1 0-1.097l.358-.196a2.25 2.25 0 0 0 .895-.895l.196-.358Z"
                        ></path>
                        <path
                          fill-rule="evenodd"
                          d="M12.948 7.89c-.18-1.167-1.852-1.19-2.064-.029l-.03.164a3.756 3.756 0 0 1-3.088 3.031c-1.15.189-1.173 1.833-.03 2.054l.105.02a3.824 3.824 0 0 1 3.029 3.029l.032.165c.233 1.208 1.963 1.208 2.196 0l.025-.129a3.836 3.836 0 0 1 3.077-3.045c1.184-.216 1.12-1.928-.071-2.107a3.789 3.789 0 0 1-3.18-3.154Zm-.944 6.887a5.34 5.34 0 0 1 2.542-2.647 5.305 5.305 0 0 1-2.628-2.548 5.262 5.262 0 0 1-2.488 2.508 5.329 5.329 0 0 1 2.574 2.687Z"
                        ></path></svg
                    ></span>
                  </div>
                  <h3 class="Polaris-Text--root Polaris-Text--headingSm Polaris-Text--magic">
                    Introducing Our Latest AI SEO Optimization Feature 🎉
                  </h3>
                </div>
                <p class="Polaris-Text--root">
                  Don't know how to optimize your products for SEO? Or want to speed up your workflow? Try our AI
                  Content Optimizer to make your Meta Titles, Meta Descriptions, and Tags SEO-friendly within the blink
                  of an eye.
                </p>
                <div class="Polaris-Box">
                  <button
                    class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--disabled"
                    aria-disabled="true"
                    type="button"
                    tabindex="-1"
                  >
                    <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Check it out</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
