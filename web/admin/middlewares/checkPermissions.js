const httpStatus = require("http-status");
const AdminScopes = require("storeseo-enums/admin/AdminScopes");
const {
  STORE_READ,
  STORE_WRITE,
  PRODUCT_READ,
  STORE_DELETE,
  ADMIN_USER_READ,
  ADMIN_USER_WRITE,
  ADMIN_USER_DELETE,
  COUPON_READ,
  COUPON_WRITE,
  COUPON_DELETE,
  SUBSCRIPTION_PLAN_READ,
  SUBSCRIPTION_PLAN_WRITE,
  SUBSCRIPTION_PLAN_DELETE,
  SUBSCRIPTION_ADDON_READ,
  SUBSCRIPTION_ADDON_WRITE,
  SUBSCRIPTION_ADDON_DELETE,
  ACTIVITY_LOG_READ,
  REPORT_DAILY_STATUS,
  DASHBOARD_READ,
  PAYMENT_HISTORY_READ,
  PARTNER_READ,
  PARTNER_WRITE,
  PARTNER_DELETE,
  COLLECTION_READ,
  ARTICLE_READ,
  PAGE_READ,
} = require("storeseo-enums/admin/ApiActionList");
const { normalizePath, matchRoute } = require("../utils/helpers");

const routeToActionMap = {
  // dashboard related routes
  "/dashboard/counter/": {
    get: STORE_READ,
  },

  "/dashboard/customer/": {
    get: DASHBOARD_READ,
  },

  "/dashboard/chart/": {
    get: DASHBOARD_READ,
  },

  "/dashboard/resources-stats": {
    get: STORE_READ,
  },

  // stores related routes
  "/stores/counter": {
    get: STORE_READ,
  },

  "/stores": {
    get: STORE_READ,
  },

  "/stores/details/:id": {
    get: STORE_READ,
  },

  "/stores/plans/:id": {
    get: STORE_READ,
  },

  "/stores/names": {
    get: STORE_READ,
  },

  "/stores/plan-options": {
    get: STORE_READ,
  },

  "/stores/store-verified/:id": {
    put: STORE_WRITE,
  },

  "/stores/remove-branding/:id": {
    put: STORE_WRITE,
  },

  "/stores/cache-data/:id": {
    get: STORE_READ,
  },

  "/stores/update-cache/:id": {
    put: STORE_WRITE,
  },

  "/stores/reset-cache/:id": {
    put: STORE_WRITE,
  },

  "/stores/reset-product-sync/:id": {
    put: STORE_WRITE,
  },

  "/stores/cancel-subscription/:id": {
    put: STORE_WRITE,
  },

  "/stores/remove-data/:id": {
    delete: STORE_DELETE,
  },

  "/stores/shop-data/:id": {
    get: STORE_READ,
  },

  "/stores/update-plan/:id": {
    put: STORE_WRITE,
  },

  "/stores/webhooks/:id": {
    get: STORE_READ,
  },

  "/stores/register-webhooks/:id": {
    put: STORE_WRITE,
  },

  "/stores/addons/:id": {
    get: STORE_READ,
  },

  "/stores/remove-deleted-products/:id": {
    delete: STORE_DELETE,
  },

  "/stores/change-shop-status/:id": {
    put: STORE_WRITE,
  },

  "/stores/gift-ai-credits/:id": {
    put: STORE_WRITE,
  },

  // products related routes
  "/products": {
    get: PRODUCT_READ,
  },

  "/products/details/:id": {
    get: PRODUCT_READ,
  },

  "/products/meta/:id": {
    get: PRODUCT_READ,
  },

  "/products/image/:id": {
    get: PRODUCT_READ,
  },

  "/products/:shopId/analytics/:id": {
    get: PRODUCT_READ,
  },

  // collections related routes
  "/collections": {
    get: COLLECTION_READ,
  },
  "/collections/:id": {
    get: COLLECTION_READ,
  },

  "/collections/meta/:id": {
    get: COLLECTION_READ,
  },

  "/collections/image/:id": {
    get: COLLECTION_READ,
  },

  // articles related routes
  "/articles": {
    get: ARTICLE_READ,
  },
  "/articles/:id": {
    get: ARTICLE_READ,
  },
  "/articles/image/:id": {
    get: ARTICLE_READ,
  },
  "/articles/meta/:id": {
    get: ARTICLE_READ,
  },

  // pages related routes
  "/pages": {
    get: PAGE_READ,
  },
  "/pages/:id": {
    get: PAGE_READ,
  },
  "/pages/meta/:id": {
    get: PAGE_READ,
  },

  // payments related routes
  "/payments": {
    get: PAYMENT_HISTORY_READ,
  },
  "/payments/:id": {
    get: PAYMENT_HISTORY_READ,
  },

  // daily reports related routes
  "/reports/daily-status": {
    get: REPORT_DAILY_STATUS,
  },

  // plans related routes
  "/settings/subscription/plans": {
    get: SUBSCRIPTION_PLAN_READ,
    post: SUBSCRIPTION_PLAN_WRITE,
  },
  "/settings/subscription/plans/:id": {
    get: SUBSCRIPTION_PLAN_READ,
    put: SUBSCRIPTION_PLAN_WRITE,
    delete: SUBSCRIPTION_PLAN_DELETE,
  },
  "/settings/subscription/plan-features": {
    get: SUBSCRIPTION_PLAN_READ,
  },

  // addons related routes
  "/settings/subscription/addons": {
    get: SUBSCRIPTION_ADDON_READ,
    post: SUBSCRIPTION_ADDON_WRITE,
  },
  "/settings/subscription/addons/:id": {
    get: SUBSCRIPTION_ADDON_READ,
    put: SUBSCRIPTION_ADDON_WRITE,
    delete: SUBSCRIPTION_ADDON_DELETE,
  },

  // coupons related routes
  "/settings/coupons": {
    get: COUPON_READ,
    post: COUPON_WRITE,
  },
  "/settings/coupons/:id": {
    get: COUPON_READ,
    put: COUPON_WRITE,
    delete: COUPON_DELETE,
  },
  "/settings/coupon/status": {
    get: COUPON_READ,
  },

  // users related routes
  "/settings/users": {
    get: ADMIN_USER_READ,
    post: ADMIN_USER_WRITE,
  },
  "/settings/users/:id": {
    get: ADMIN_USER_READ,
    put: ADMIN_USER_WRITE,
    delete: ADMIN_USER_DELETE,
  },
  "/settings/users/password/:id": {
    put: ADMIN_USER_WRITE,
  },

  // partners related routes
  "/settings/partners": {
    get: PARTNER_READ,
    post: PARTNER_WRITE,
  },

  "/settings/partners/:id": {
    get: PARTNER_READ,
    put: PARTNER_WRITE,
    delete: PARTNER_DELETE,
  },

  // activity logs related routes
  "/activityLog": {
    get: ACTIVITY_LOG_READ,
  },

  "/activityLog/delete-logs": {
    delete: ACTIVITY_LOG_READ,
  },
};

const checkPermissions = (req, res, next) => {
  let actions = new Set();

  if (req?.user?.scopes) {
    const { scopes, id, type } = req.user;

    scopes.forEach((scope) => {
      if (AdminScopes[scope]?.code) {
        AdminScopes[scope]?.actions.forEach((action) => actions.add(action));
      }
    });

    actions = Array.from(actions);

    const method = req.method.toLowerCase();
    const normalizedPath = normalizePath(req.originalUrl);
    const matchedRoute = matchRoute(normalizedPath, routeToActionMap);

    const routePermissions = matchedRoute ? routeToActionMap[matchedRoute] : null;

    if (routePermissions) {
      const requiredAction = routePermissions[method];
      if (req.params?.id === String(req.user?.id)) {
        if (!scopes.includes("ADMIN_USER:WRITE")) {
          actions.push("ADMIN_USER_WRITE");
        }
      }

      if (scopes.includes("ADMIN_USER:READ") && type !== 1 && type !== 2) {
        req.hasOwnProfile = true;
      }

      if (!scopes?.includes("ADMIN_USER:READ")) {
        actions.push("ADMIN_USER_READ");
        req.hasOwnProfile = true;
      }

      if (!actions.includes(requiredAction)) {
        return res.status(httpStatus.UNAUTHORIZED).send({ message: "Unauthorized Access" });
      }
    } else {
      return res.status(httpStatus.UNAUTHORIZED).send({ message: "Unauthorized Access" });
    }
  } else {
    throw new Error("Scopes not found in the request");
  }
  next();
};

module.exports = checkPermissions;
