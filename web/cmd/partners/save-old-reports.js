const moment = require("moment");
const { sleep } = require("../../api/utils/helper");
const AdminReportService = require("../../api/services/admin/AdminReportService");
const AdminReportTypes = require("storeseo-enums/admin/AdminReportTypes");
const SlackService = require("../../api/services/SlackService");

(async () => {
  const fromDate = moment(process?.argv[2] || "2021-01-01").add(1, "d");
  const today = moment();

  while (+fromDate < +today) {
    const { startDate, endDate } = AdminReportService.prepareDates(fromDate.format("YYYY-MM-DD"));
    const formattedDate = moment(endDate).format("Do MMM, YYYY (dddd)");
    console.log("Saving Daily Stats of Date =", formattedDate);
    const report = await AdminReportService.dailyStatusReport(startDate, endDate);

    await AdminReportService.createOrUpdateReportData({
      date: moment(endDate).format("YYYY-MM-DD"),
      type: AdminReportTypes.DAILY_STATUS_REPORT,
      stats: report,
    });

    const startTime = moment(endDate).tz("Asia/Dhaka").startOf("D").toISOString();
    const endTime = moment(endDate).tz("Asia/Dhaka").endOf("D").toISOString();

    const subscription = await SlackService.getIOSubscriptionData(startTime, endTime);

    const ioData = await SlackService.imageOptimizerData(moment(endDate).format("YYYY-MM-DD"));

    await AdminReportService.createOrUpdateReportData({
      date: moment(endDate).format("YYYY-MM-DD"),
      type: AdminReportTypes.DAILY_APP_USAGE_REPORT,
      stats: { subscriptions: subscription, imageOptimizerData: ioData },
    });

    fromDate.add(1, "d");
    sleep(1000);
  }

  setTimeout(process.exit, 5000);
})();
