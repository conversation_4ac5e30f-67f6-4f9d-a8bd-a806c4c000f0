const createVisionaryRecurringCharges = require("./tasks/create-visionary-recurring-charges");
const deleteDeliveredEmails = require("./tasks/delete-delivered-emails");
// const resetImageOptimizerAddonUsageOfRelevantShops = require("./tasks/addons/resetImageOptimizerUsageCount");
const syncAddonUsageCacheIntoDb = require("./tasks/addons/syncAddonUsageCacheIntoDb");
const dailyStoreDataCheck = require("./tasks/events/daily-store-data-check");
const trialExpirationCheck = require("./tasks/events/trial-expiration-check");
const storeAppEvents = require("./tasks/partners/store-app-events");
const storeTransactions = require("./tasks/partners/store-transactions");
const sendDailyStats = require("./tasks/slack/send-daily-stats");
const sendAppUsageReport = require("./tasks/slack/send-app-usage-report");
const resetRecurringAddonsUsage = require("./tasks/addons/resetRecurringAddonsUsage");
const processPendingWebhookRegistrations = require("./tasks/webhooks/processPendingWebhookRegistrations");

module.exports = [
  {
    name: "Process Pending Webhook Registrations",
    schedule: "*/5 * * * * *", // Run every 5 seconds
    callbackFn: processPendingWebhookRegistrations,
    env: "",
  },
  {
    name: "Sync addons usage cache to database",
    schedule: "0 0 */6 * * *",
    callbackFn: syncAddonUsageCacheIntoDb,
    env: "",
  },
  {
    name: "Create Visionary Charges",
    schedule: "30 0 * * *",
    callbackFn: createVisionaryRecurringCharges,
    env: "production",
  },
  // {
  //   name: "Reset Image optimizer addon usages",
  //   schedule: "0 1 0 * * *",
  //   callbackFn: resetImageOptimizerAddonUsageOfRelevantShops,
  //   env: "",
  // },
  {
    name: "Reset recurring addons usages",
    schedule: "0 1 0 * * *",
    callbackFn: resetRecurringAddonsUsage,
    env: "",
  },
  {
    name: "Store shopify app event data to database",
    schedule: "0 1 * * *",
    callbackFn: storeAppEvents,
    env: "production",
  },
  {
    name: "Store shopify transaction data to database",
    schedule: "30 1 * * *",
    callbackFn: storeTransactions,
    env: "production",
  },
  {
    name: "Daily Status Report",
    schedule: "0 4 * * *",
    callbackFn: sendDailyStats,
    env: "production",
  },
  {
    name: "Daily Store Data Check",
    schedule: "0 2 * * *",
    // schedule: "1-59 * * * *",
    callbackFn: dailyStoreDataCheck,
    // env: "",
    env: "production",
  },
  {
    name: "Trial Expiration Check",
    schedule: "0 0 * * *", // Run daily at 00:00 (midnight)
    callbackFn: trialExpirationCheck,
    env: "production",
  },
  {
    name: "Daily App Usage Report",
    schedule: "30 16 * * *",
    callbackFn: sendAppUsageReport,
    env: "production",
  },
  {
    name: "Delete Delivered Emails",
    schedule: "0 8 * * 6",
    callbackFn: deleteDeliveredEmails,
    env: "production",
  },
];
