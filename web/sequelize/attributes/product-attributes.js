//@ts-check
const { DataTypes } = require("sequelize");
const { timestampFields } = require("../config/common-options");
const AiOptimiationStatus = require("storeseo-enums/aiOptimization");
const focusKeywordGenerationStatus = require("storeseo-enums/focusKeywordGenerationStatus");
const { omit } = require("lodash");

module.exports = {
  shop_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
  },
  product_id: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
  },
  handle: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  cursor: {
    type: DataTypes.STRING,
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  description: {
    type: DataTypes.TEXT,
  },
  featured_image_id: {
    type: DataTypes.STRING,
  },
  featured_media_id: {
    type: DataTypes.STRING,
  },
  issues: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
  },
  score: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
  },
  passed: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
  },

  focus_keyword: {
    type: DataTypes.STRING,
  },
  focus_keyword_suggestions: {
    type: DataTypes.JSON,
    defaultValue: [],
  },
  focus_keyword_generation_status: {
    type: DataTypes.STRING(100),
    defaultValue: focusKeywordGenerationStatus.NOT_GENERATED,
  },

  tags: {
    type: DataTypes.JSON,
  },
  is_analysed: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
  },
  is_optimized: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
  },
  optimized_at: {
    type: DataTypes.DATE,
  },
  product_type: {
    type: DataTypes.STRING,
  },
  vendor: {
    type: DataTypes.STRING,
  },
  online_store_url: {
    type: DataTypes.TEXT,
  },
  online_store_preview_url: {
    type: DataTypes.TEXT,
  },
  status: {
    type: DataTypes.INTEGER,
  },
  indexing_status: {
    type: DataTypes.STRING,
  },
  indexing_last_update: {
    type: DataTypes.DATE,
  },
  ai_optimization_status: {
    type: DataTypes.ENUM(...Object.keys(omit(AiOptimiationStatus, ["labels"]))),
    defaultValue: AiOptimiationStatus.NOT_OPTIMIZED,
  },
  ai_optimized_at: { type: DataTypes.DATE, allowNull: true },
  is_synced: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    allowNull: false,
  },
  ...timestampFields,
};
