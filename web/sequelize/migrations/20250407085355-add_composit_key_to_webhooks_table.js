"use strict";

const tableNames = require("../config/table-names");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addConstraint(tableNames.WEBHOOK, {
      fields: ["shop_id", "topic"],
      type: "unique",
      name: "webhook_shop_id_topic_unique",
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(tableNames.WEBHOOK, "webhook_shop_id_topic_unique");
  },
};
