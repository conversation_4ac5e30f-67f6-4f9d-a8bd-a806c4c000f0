const logger = require("storeseo-logger");
const ArticleService = require("../services/ArticleService");
const BlogService = require("../services/BlogService");
const cache = require("../cache");
const toastMessages = require("storeseo-enums/toastMessages");
const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");
const activityLogService = require("../../admin/services/activityLog.service");
const LogOriginTypes = require("storeseo-enums/logOriginTypes");

class BlogController {
  /**
   * Get all blogs for a shop with pagination and filtering
   * GET /api/blogs
   */
  getAllBlogs = async (req, res) => {
    try {
      const { shopId } = req.user;
      const {
        synced_only = true,
        search = "",
        sort_by = "title",
        sort_order = "asc",
        page = 1,
        limit = 50,
      } = req.query;

      // Prepare options for BlogService
      const options = {
        syncedOnly: synced_only === "true" || synced_only === true,
        search,
        sortBy: sort_by,
        sortOrder: sort_order,
        page: parseInt(page),
        limit: parseInt(limit),
      };

      const result = await BlogService.getBlogsWithPagination(shopId, options);
      return res.success(result);
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: `Error getting all blogs.` });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  /**
   * Get blog statistics for a shop
   * GET /api/blogs/stats
   */
  getBlogStats = async (req, res) => {
    try {
      const { shopId } = req.user;
      const stats = await BlogService.getBlogStats(shopId);
      return res.success(stats);
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: `Error getting blog stats.` });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getAllArticles = async (req, res) => {
    try {
      const { shopId } = req.user;
      const result = await ArticleService.getAllArticlesWithPagination(shopId, req.query, [
        "id",
        "article_id",
        "title",
        "focus_keyword",
        "issues",
        "score",
        "created_at",
        "ai_optimization_status",
        "published_at",
      ]);

      const articleCount = await ArticleService.count(shopId);
      return res.success({ ...result, articleCount });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: `Error gettting all blog articles.` });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  syncBlogsFromShopify = async (req, res) => {
    try {
      const { shopId, shop, url } = req.user;

      activityLogService.addActivityLog({
        req,
        prevData: null,
        updatedData: null,
        subject: "Blogs Sync Started",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      await cache.blogSyncOngoing(req.user.shop, true);

      await BlogService.markBlogsAsNotSynced(shopId);
      await ArticleService.markArticlesAsNotSynced(shopId);

      dispatchQueue({
        queueName: QUEUE_NAMES.ARTICLE_SYNC,
        message: {
          shopId,
          shopDomain: shop,
          shopUrl: url,
          session: req.user,
          cursor: null,
        },
      });
      res.success({ message: toastMessages.ARTICLES_SYNC_STARTED });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: `Error processing req to sync blogs from shopify.` });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG, err });
    }
  };
}

module.exports = new BlogController();
