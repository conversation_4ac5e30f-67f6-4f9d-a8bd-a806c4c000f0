const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const { RABBIT_MQ_CONNECTION } = require("../index");
const MailchimpService = require("../../services/MailchimpService");
const ShopService = require("../../services/ShopService");
const MailchimpStaticTags = require("storeseo-enums/mailchimp/staticTags");

/**
 * @typedef {Object} Message
 * @property {string} domain
 * @property {Array} tags
 * @property {Array | []} tagsToRemove
 */

class AddToMailchimpQueue extends BaseQueue {
  /**
   *
   * @param {string} shopId
   * @param {string} email
   */
  async updateMailchimpDataInDb(shopId, email) {
    const member = await MailchimpService.getMember(email);
    delete member["_links"];

    await ShopService.upsertMailchimpData(shopId, member);
  }

  /**
   * @param {Message} message
   * @param channel
   * @param decodeToJSON
   * @returns {Promise<boolean>}
   */
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { domain, tags = [], tagsToRemove = [] } = decodedMessage;

    try {
      // Add/Update member
      const { email_address: email } = await MailchimpService.addMember(domain);
      const shop = await ShopService.getShop(domain, ["id", "trial_data"]);
      const trialTag =
        shop.trial_data?.isTrialUser &&
        Object.values(MailchimpStaticTags)
          .find((t) => t.includes(`TRIAL_${shop.trial_data.status}`))
          ?.replace("_", " ");

      trialTag && tags.push(trialTag);

      // Update member's tags
      await MailchimpService.updateMemberTags(email, tags, tagsToRemove);

      await this.updateMailchimpDataInDb(shop.id, email);

      channel.ack(message);
      console.info(`[${this.config.queueName}](${domain}) processed successfully`);
      return true;
    } catch (e) {
      channel.ack(message);
      console.warn(`Failed to update member tags`, e.message);
      return false;
    }
  }
}

module.exports = new AddToMailchimpQueue(
  {
    queueName: QUEUE_NAMES.MAILCHIMP_ADD_MEMBER,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.MAILCHIMP_ADD_MEMBER],
  },
  RABBIT_MQ_CONNECTION
);
