const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const ProductService = require("../../services/ProductService");
const ProductAnalysisService = require("../../services/ProductAnalysisService");
const ShopService = require("../../services/ShopService");
const SitemapService = require("../../services/SitemapService");
const { isNull } = require("lodash/lang");
const { RABBIT_MQ_CONNECTION } = require("../index");
const { execTimeTracker } = require("../../utils/helper");
const { compareShopifyProductWithDbProduct } = require("../../utils/comparer");
const { dispatchQueue } = require("../queueDispatcher");
const { serializeWebhookPayloadToShopifyProduct } = require("../../serializers/ProductSerializer");
const { isEmpty, uniq } = require("lodash");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const ProductImageService = require("../../services/ProductImageService");
const { Op } = require("../../../sequelize");
const resourceType = require("storeseo-enums/resourceType");
const { isExcludedProductType } = require("storeseo-enums/excludedProductTypes");

class WebhookProductUpdateQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const execTime = execTimeTracker();
    const decodedMessage = decodeToJSON(message);

    const { headers, body: webhookPayload } = decodedMessage;
    const shopDomain = headers["X-Shopify-Shop-Domain"] || headers["x-shopify-shop-domain"];
    const { admin_graphql_api_id: productGqlId, status } = webhookPayload;

    try {
      // disptach product delete if product status is not 'active'
      if (status !== "active") {
        console.info(
          `[${this.config.queueName}](${shopDomain}) product : ${productGqlId} status is not active! Dispatching product delete queue for it.`
        );
        dispatchQueue({
          queueName: QUEUE_NAMES.WEBHOOK_PRODUCT_DELETE,
          message: decodedMessage,
        });
        channel.ack(message);
        return;
      }

      const shop = await ShopService.getShop(shopDomain);

      if (isEmpty(shop)) {
        channel.ack(message);
        console.log("Shop not found for domain = " + shopDomain);
        return;
      }

      const oldProduct = await ProductService.getProductByCondition(shop.id, { product_id: productGqlId });

      if (!oldProduct || !webhookPayload) {
        const limit = !isNull(shop?.plan_rules?.products) ? shop.plan_rules.products : Number.POSITIVE_INFINITY;

        // Stop product insert if more than limit
        const productCount = await ProductService.countProducts(shop.id);

        if (productCount >= limit) {
          console.info(`[${this.config.queueName}](${shopDomain}) - Limit exceeded. Product cannot be saved.`, {
            productCount,
            productLimit: limit,
          });
        } else {
          console.info(
            `Product doesn't exist in DB! Dispatching product create queue for (${shopDomain}), product id ${productGqlId}`
          );
          dispatchQueue({
            queueName: QUEUE_NAMES.WEBHOOK_PRODUCT_CREATE,
            message: decodedMessage,
          });
        }

        channel.ack(message);
        return;
      }

      const shopifyProduct = serializeWebhookPayloadToShopifyProduct(webhookPayload);

      // Skip products from EasyFlow app and other excluded product types
      if (isExcludedProductType(shopifyProduct.productType)) {
        console.info(
          `[${this.config.queueName}](${shopDomain}) Skipping excluded product type: ${shopifyProduct.productType} for product: ${productGqlId}`
        );
        channel.ack(message);
        return;
      }

      let product;
      if (!compareShopifyProductWithDbProduct(shopifyProduct, oldProduct)) {
        console.info(`[${this.config.queueName}](${shopDomain}) product : ${productGqlId} has changed...`);
        product = await ProductService.saveOrUpdateProduct(shop.id, shopifyProduct);
        console.info(`[${this.config.queueName}](${shopDomain}) updated successfully : ${productGqlId}`);

        if (!isEmpty(product)) {
          await SitemapService.storeSitemapData(product, analysisEntityTypes.PRODUCT);
          await ProductAnalysisService.analyseEachProduct({
            shopId: shop.id,
            product,
            oldProduct,
          });

          dispatchQueue({
            queueName: QUEUE_NAMES.AUTO_IMAGE_OPTIMIZER_QUEUE,
            message: {
              shop: shopDomain,
              itemId: product.id,
              type: analysisEntityTypes.PRODUCT,
            },
          });

          dispatchQueue({
            queueName: QUEUE_NAMES.AUTO_IMAGE_ALT_TEXT_OPTIMIZATION_QUEUE,
            message: {
              shop: shopDomain,
              resourceId: product.id,
              resourceType: resourceType.PRODUCT,
            },
          });

          await this.#reanalseProductsThatShareSameImages(shop.id, product);
        } else {
          console.info(`[${this.config.queueName}](${shopDomain}) product not found : ${productGqlId}`);
        }
      } else {
        console.info(`[${this.config.queueName}](${shopDomain}) product : ${productGqlId} not changed!`);
      }

      console.info(
        `[${this.config.queueName}](${shopDomain}) processed successfully : ${productGqlId} -- ${execTime.get()}`
      );

      channel.ack(message);
      // return true;
    } catch (e) {
      channel.ack(message);
      if (this.isThrottled(e)) {
        // re-dispatch
        // await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: this.throttledDelay,
        });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(
          `[${shopDomain}] ${this.config.queueName} failed. Product ID: ${productGqlId}, Message: ${e.message}`
        );
        await this.handleError(e, message);
      }
      return false;
    }
  }

  async #reanalseProductsThatShareSameImages(shopId, product) {
    const imageIds = product.images?.map((img) => img.id) || [];

    const dbImages = await ProductImageService.getImagesByConditions({
      id: {
        [Op.in]: imageIds,
      },
    });

    const productIds = dbImages
      .map((img) => img.products)
      .flat()
      .map((p) => p.id);
    const uniqueIds = uniq(productIds).filter((id) => id !== product.id);

    for (let id of uniqueIds) {
      dispatchQueue({
        queueName: QUEUE_NAMES.RE_ANALYSE_PRODUCT,
        message: {
          shopId,
          productId: id,
        },
      });
    }
  }
}

module.exports = new WebhookProductUpdateQueue(
  {
    queueName: QUEUE_NAMES.WEBHOOK_PRODUCT_UPDATE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_PRODUCT_UPDATE],
  },
  RABBIT_MQ_CONNECTION
);
