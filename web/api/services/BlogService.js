const logger = require("storeseo-logger");
const ShopifyService = require("./ShopifyService");
const { Blog, Article, Op } = require("../../sequelize");
const { serializeShopifyBlogData } = require("../serializers/BlogSerializer");
const cache = require("../cache");
const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");
const EventService = require("./EventService");
const SitemapService = require("./SitemapService");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");

/**
 * @typedef {import('../../jsDocTypes').BlogDetails} BlogDetails
 * @typedef {import("../../jsDocTypes").Pagination} Pagination
 * @typedef {import("../../jsDocTypes").Metafield} Metafield
 */

class BlogService {
  /**
   * Create a new blog in DB
   * @param {BlogDetails} data blog details object
   * @returns {Promise<BlogDetails>} saved blog details from DB
   */
  saveBlog = async (data) => {
    try {
      const savedBlog = await Blog.create(data);
      return savedBlog.toJSON();
    } catch (err) {
      console.error(`Error saving blog into database. Data: ${JSON.stringify(data)}. Error: ${err}}`);
      return null;
    }
  };

  /**
   * Update existing blog data in DB
   * @param {number} id database id of the blog
   * @param {BlogDetails} data blog attributes to update
   * @returns {Promise<BlogDetails>} updated blog from DB
   */
  updateBlog = async (id, data) => {
    try {
      const [affectedRows, blogs] = await Blog.update(data, { returning: true, where: { id } });

      if (affectedRows === 1) return blogs[0].toJSON();

      return blogs.map((b) => b.toJSON());
    } catch (err) {
      console.error(`Error updating blog ${id}. Data: ${JSON.stringify(data, null, 2)}. Error: ${err}`);
      return null;
    }
  };

  /**
   * Get blog by DB id & shop id
   * @param {number} shopId database id of the shop
   * @param {number} id database id of the blog
   * @returns {Promise<BlogDetails>} blog details retrieved from DB
   */
  getBlog = async (shopId, id) => {
    try {
      const blog = await Blog.findOne({ where: { id, shop_id: shopId } });
      return blog.toJSON();
    } catch (err) {
      // console.error(`Error retrieving blog by id. Id: ${id}, shop id: ${shopId}. Error: ${err}`);
      return null;
    }
  };

  /**
   * Get blog by shop id & shopify blog id
   * @param {number} shopId database id of the shop
   * @param {number} blogId shopify blog id
   * @returns {Promise<BlogDetails>} blog details retrieved from DB
   */
  getBlogByBlogId = async (shopId, blogId) => {
    try {
      const blog = await Blog.findOne({ where: { shop_id: shopId, blog_id: blogId } });
      return blog?.toJSON();
    } catch (err) {
      // console.error(`Error retrieving blog by shopify blog id. Blog id: ${blogId}, shopId: ${shopId}. Error: ${err}`);
      return null;
    }
  };

  /**
   * Insert or update existing blog in DB
   * @param {BlogDetails} data blog details object
   * @returns {Promise<BlogDetails>} saved/updated blog from DB
   */
  saveOrUpdateBlog = async (data) => {
    const [blog] = await Blog.upsert(data, { returning: true });
    return blog.toJSON();
  };

  /**
   * Fetch list of all blogs from Shopify and insert/update them in DB
   * @param {User} user
   * @param {number} shopId
   * @returns {Promise<BlogDetails[]>}
   */
  saveOrUpdateBlogsFromShopify = async (user, shopId) => {
    try {
      const ArticleService = require("./ArticleService");
      await ArticleService.temproarilyMarkAllArticlesAsDeleted(shopId);
      const blogs = await ShopifyService.getShopifyBlogs(user);
      const results = await Promise.allSettled(
        blogs?.map((b) => {
          const data = serializeShopifyBlogData(shopId, b);
          return this.saveOrUpdateBlog(data);
        })
      );

      const savedBlogs = results.filter((r) => r.status === "fulfilled").map((r) => r.value);

      if (savedBlogs.length === 0) {
        await EventService.handleBlogArticleSyncUpdate({
          shop: user.shop,
          id: null,
          articles: 0,
        });
        return;
      }

      // Delete all articles related sitemaps
      await SitemapService.deleteAllSitemaps(shopId, analysisEntityTypes.ARTICLE);

      for (const b of savedBlogs) {
        await cache.blogsPendingInQueue(user.shop, b.id);
        dispatchQueue({
          queueName: QUEUE_NAMES.BLOG_ARTICLES_SYNC,
          message: { user, blog: b },
          ttl: 1000,
        });
      }

      return savedBlogs;
    } catch (err) {
      return null;
    }
  };

  /**
   * Delete all blogs for a shop from DB
   * @param {number} shopId
   */
  deleteBlogOfShop = async (shopId) => {
    try {
      await Blog.destroy({ where: { shop_id: shopId } });
    } catch (e) {
      console.log(e);
    }
  };

  deleteNotSyncedBlogs = async (shopId) => {
    let blogsToDelete = await Blog.findAll({ where: { shop_id: shopId, is_synced: false } });

    for (let blog of blogsToDelete) {
      await blog.destroy();
    }
  };

  markBlogsAsNotSynced = async (shopId) => {
    await Blog.update(
      {
        is_synced: false,
      },
      {
        where: { shop_id: shopId },
      }
    );
  };

  /**
   * Get the default blog for a shop (first available blog)
   * @param {number} shopId - Shop ID
   * @returns {Promise<BlogDetails|null>} Default blog or null if no blogs exist
   */
  getDefaultBlog = async (shopId) => {
    try {
      const blog = await Blog.findOne({
        where: { shop_id: shopId },
        order: [["id", "ASC"]], // Get the first blog (usually the default)
      });
      return blog ? blog.toJSON() : null;
    } catch (err) {
      console.error(`Error retrieving default blog for shop ${shopId}. Error: ${err}`);
      return null;
    }
  };

  /**
   * Get paginated list of blogs with filtering and article counts
   * @param {number} shopId - Shop database ID
   * @param {Object} options - Query options
   * @param {boolean} [options.syncedOnly=true] - Only return synced blogs
   * @param {string} [options.search=""] - Search term for blog titles
   * @param {string} [options.sortBy="title"] - Sort field
   * @param {string} [options.sortOrder="asc"] - Sort order
   * @param {number} [options.page=1] - Page number
   * @param {number} [options.limit=50] - Items per page
   * @returns {Promise<{blogs: Array, pagination: Object}>}
   */
  getBlogsWithPagination = async (shopId, options = {}) => {
    const { syncedOnly = true, search = "", sortBy = "title", sortOrder = "asc", page = 1, limit = 50 } = options;

    try {
      // Build where clause
      const whereClause = { shop_id: shopId };

      if (syncedOnly) {
        whereClause.is_synced = true;
      }

      if (search) {
        whereClause.title = { [Op.iLike]: `%${search}%` };
      }

      // Build order clause
      const orderField = sortBy === "created_at" ? "created_at" : "title";
      const orderDirection = sortOrder.toUpperCase() === "DESC" ? "DESC" : "ASC";

      // Calculate pagination
      const offset = (parseInt(page) - 1) * parseInt(limit);

      // Fetch blogs with pagination
      const { rows: blogs, count: total } = await Blog.findAndCountAll({
        where: whereClause,
        order: [[orderField, orderDirection]],
        limit: parseInt(limit),
        offset: offset,
        attributes: [
          "id",
          "blog_id",
          "title",
          "handle",
          "is_synced",
          "template_suffix",
          "tags",
          "created_at",
          "updated_at",
        ],
      });

      // Add article count for each blog
      const blogsWithCounts = await this._addArticleCountsToBlogs(shopId, blogs);

      const pagination = {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        hasNext: offset + parseInt(limit) < total,
        hasPrev: parseInt(page) > 1,
      };

      return {
        blogs: blogsWithCounts,
        pagination,
      };
    } catch (error) {
      logger.error(error, { shopId, message: "Error fetching blogs with pagination" });
      throw error;
    }
  };

  /**
   * Get blog statistics for a shop
   * @param {number} shopId - Shop database ID
   * @returns {Promise<{total: number, synced: number, unsynced: number}>}
   */
  getBlogStats = async (shopId) => {
    try {
      const total = await Blog.count({
        where: { shop_id: shopId },
      });

      const synced = await Blog.count({
        where: { shop_id: shopId, is_synced: true },
      });

      const unsynced = total - synced;

      return { total, synced, unsynced };
    } catch (error) {
      logger.error(error, { shopId, message: "Error fetching blog stats" });
      throw error;
    }
  };

  /**
   * Private method to add article counts to blogs
   * @param {number} shopId - Shop database ID
   * @param {Array} blogs - Array of blog instances
   * @returns {Promise<Array>} Blogs with article counts
   * @private
   */
  _addArticleCountsToBlogs = async (shopId, blogs) => {
    return Promise.all(
      blogs.map(async (blog) => {
        const articleCount = await Article.count({
          where: {
            shop_id: shopId,
            blog_id: blog.id,
            is_deleted: false,
          },
        });
        return {
          ...blog.toJSON(),
          article_count: articleCount || 0,
        };
      })
    );
  };
}

module.exports = new BlogService();
