const moment = require("moment");
const { IncomingWebhook } = require("@slack/webhook");
const { sequelize, Sequelize, QueueStats, BulkOperationTracker, BlogAutoWriteJob } = require("../../sequelize");
const ResourceType = require("storeseo-enums/resourceType");
const ResourceOPType = require("storeseo-enums/resourceOPType");
const logger = require("storeseo-logger");
const AdminReportService = require("./admin/AdminReportService");
const {
  serializeDailyStatsData,
  prepareDailyStatusReportBlock,
} = require("../serializers/admin/DailyStatusDataSerializer");
const { QUEUE_BASE_NAME } = require("../queue/imageOptimizer/config");
const AdminReportTypes = require("storeseo-enums/admin/AdminReportTypes");
const BlogAutoWriteJobStatus = require("storeseo-enums/blogAutoWrite/jobStatus");
const { Op } = require("sequelize");

class SlackService {
  #config = {
    test: "*******************************************************************************",
    live: "*******************************************************************************",
  };

  #channels = {
    ALERT_CHANNEL: "ALERT_CHANNEL",
  };

  #incomingWebhookUrls = {
    [this.#channels.ALERT_CHANNEL]: {
      test: "*******************************************************************************",
      live: "*******************************************************************************",
    },
  };

  #client = () => {
    // Read a url from the environment variables
    const url = this.#config[process.env.SLACK_ENV || "test"];

    // Initialize
    const webhook = new IncomingWebhook(url);

    return webhook;
  };

  /**
   *
   * @param {string} channelName channel key defined in **#channels** config
   * @returns {string} incoming webhook url of the channel based on `.env -> SLACK_ENV` (default env: "test")
   */
  #getIncomingWebhookUrl = (channelName) => {
    return this.#incomingWebhookUrls[this.#channels.ALERT_CHANNEL][process.env.SLACK_ENV || "test"];
  };

  /**
   *
   * @param {string} channelName channel key defined in **#channels** config
   */
  #prepareClient = (channelName) => {
    const url = this.#getIncomingWebhookUrl(channelName);

    const webhook = new IncomingWebhook(url);
    return webhook;
  };

  sendImageOptimizerMissingWebhooksAlert = async ({ date, imagesQueued, webhooksReceived, imagesMissing }) => {
    const client = this.#prepareClient(this.#channels.ALERT_CHANNEL);

    const data = {
      blocks: [
        {
          type: "divider",
        },
        {
          type: "rich_text",
          elements: [
            {
              type: "rich_text_section",
              elements: [
                {
                  type: "emoji",
                  name: "x",
                },
                {
                  type: "text",
                  text: " ",
                },
                {
                  type: "text",
                  text: "Images are not returning via webhooks",
                  style: {
                    bold: true,
                  },
                },
                {
                  type: "text",
                  text: " ",
                },
                {
                  type: "emoji",
                  name: "cry",
                },
              ],
            },
          ],
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `---\n*Date: ${date}*\n--- \n • :alphabet-white-question:Images queued: _${imagesQueued}_ \n • :alphabet-yellow-question:Webhooks received: _${webhooksReceived}_ \n • :warning:Images not back yet: _${imagesMissing}_`,
          },
          accessory: {
            type: "image",
            image_url: "https://api.slack.com/img/blocks/bkb_template_images/notifications.png",
            alt_text: "calendar thumbnail",
          },
        },
        {
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `:alert: *\`${
                (imagesMissing * 100) / imagesQueued
              }%\`* images did not return from the image processing server.`,
            },
            {
              type: "mrkdwn",
              text: "*CC:* @channel",
            },
          ],
        },
      ],
    };

    client.send(data);
  };

  sendTestMessage = async () => {
    const res = await this.#client().send({ text: "this is a test message" });
    console.log("res", res);
  };

  sendAppUsageReport = async (date) => {
    const formattedDate = moment(date).format("MMMM D, YYYY");

    const ioData = await this.imageOptimizerData(date);
    const aiContentData = await this.getAiContentOptimizationStats(date);
    const blogAutoWriteData = await this.getBlogAutoWriteStats(date);

    const blocks = [
      {
        type: "header",
        text: {
          type: "plain_text",
          text: ":earth_asia: App Usage Overview :basket:",
        },
      },
      {
        type: "context",
        elements: [
          {
            text: `Report for: ${formattedDate}`,
            type: "mrkdwn",
          },
        ],
      },
      { type: "divider" },
      {
        type: "section",
        text: {
          type: "mrkdwn",
          text: " :maple_leaf: *Image Optimization*",
        },
      },
      {
        type: "section",
        text: {
          type: "mrkdwn",
          text: `_*Queued for optimization*_\n${ioData?.imagesQueued || 0}\n_*Successful*_\n${ioData?.imagesProcessed || 0}\n_*Webhooks received*_\n${ioData?.webhooksCount || 0}\n_*Failed*_\n - ${ioData?.invalidImages || 0} invalid images,\n - ${ioData?.imagesFailed || 0} images failed during optimization,\n - ${ioData?.imagesFailedToProcess || 0} images failed while processing.`,
        },
      },
      {
        type: "context",
        elements: [
          {
            type: "mrkdwn",
            text: `:volcano: *Parallel queue usage*: maximum - ${ioData?.maxQueueUsage || 0}, minimum - ${ioData?.minQueueUsage || 0}`,
          },
          {
            type: "mrkdwn",
            text: `:hut: *Parallel stores served*: maximum - ${ioData?.maxStoreCount || 0}, minimum - ${ioData?.minStoreCount || 0}`,
          },
        ],
      },
      { type: "divider" },
      {
        type: "section",
        text: {
          type: "mrkdwn",
          text: " :feather: *AI Optimization*",
        },
      },
      {
        type: "section",
        fields: [
          {
            type: "mrkdwn",
            text: `_*Content optimizations*_\n${aiContentData.aiContentGenerated}\n_*Alt-text generations*_\n${aiContentData.altTextGenerated}`,
          },
        ],
      },
      {
        type: "context",
        elements: [
          {
            type: "mrkdwn",
            text: `:comet: *Auto optimization*: content generation - ${aiContentData.autoOptimization.contentAutoOptimizations}, alt-text generation - ${aiContentData.autoOptimization.altTextAutoOptimizations}`,
          },
          {
            type: "mrkdwn",
            text: `:hut: *Stores served*: content generation - ${aiContentData.storesServed.contentStoresServed}, alt-text generation - ${aiContentData.storesServed.altTextStoresServed}, total stores - ${aiContentData.storesServed.totalStoresServed}`,
          },
        ],
      },
      { type: "divider" },
      {
        type: "section",
        text: {
          type: "mrkdwn",
          text: " :writing_hand: *Blog auto-write*",
        },
      },
      {
        type: "section",
        text: {
          type: "mrkdwn",
          text: `_*Jobs initiated*_\n${blogAutoWriteData.jobsInitiated}\n_*Completed jobs*_\n${blogAutoWriteData.completedJobs}\n_*Regenerated jobs*_\n${blogAutoWriteData.cancelledJobs}\n_*Failed jobs*_\n${blogAutoWriteData.failedJobs}`,
        },
      },
      {
        type: "context",
        elements: [
          {
            type: "mrkdwn",
            text: `:hut: *Stores served*: ${blogAutoWriteData.storesServed}`,
          },
        ],
      },
    ];

    const res = await this.#client().send({ blocks });

    if (res.text === "ok") {
      logger.debug(`IO Stats send to slack`, res);
    } else {
      logger.error(`IO Stats faild send to slack`, res);
    }

    await AdminReportService.createOrUpdateReportData({
      date: moment(date).format("YYYY-MM-DD"),
      type: AdminReportTypes.DAILY_APP_USAGE_REPORT,
      stats: { imageOptimizerData: ioData, aiContentData, blogAutoWriteData },
    });

    return res;
  };

  sendDailyStatusReport = async (date = undefined) => {
    const { startDate, endDate, compareStartDate, compareEndDate, interval } = AdminReportService.prepareDates(date);
    const formattedDate = moment(endDate).format("Do MMM, YYYY (dddd)");
    console.log(
      "Sending Daily Stats of",
      {
        startDate,
        endDate,
        compareStartDate,
        compareEndDate,
        interval,
        formattedDate,
      },
      "\n",
      "=".repeat(60),
      "\n"
    );
    const report = await AdminReportService.dailyStatusReportV2(startDate, endDate);
    const compareReport = await AdminReportService.dailyStatusReportV2(compareStartDate, compareEndDate);

    const data = serializeDailyStatsData(report, compareReport);

    const blocks = prepareDailyStatusReportBlock(formattedDate, data);

    const res = await this.#client().send({ blocks });

    if (res.text !== "ok") {
      logger.error(`Daily status report of ${formattedDate} faild send to slack`, res);
    }

    await AdminReportService.createOrUpdateReportData({
      date: moment(endDate).format("YYYY-MM-DD"),
      type: AdminReportTypes.DAILY_STATUS_REPORT,
      stats: report,
    });

    return res;
  };

  prepareIOSubsBlocks = (subscription) => {
    const freeIo = subscription.find((row) => Number(row.images) === 50)?.count || 0;
    const proIOs = subscription.filter((row) => Number(row.images) > 50);

    const formattedData = [
      { images: 50, count: Number(freeIo) },
      ...proIOs.map((row) => ({
        images: Number(row.images),
        count: Number(row.count),
      })),
    ];

    const total = formattedData.reduce((totalCount, row) => totalCount + row.count, 0);
    const proStores = formattedData.filter((r) => Number(r.images) > 50);
    const totalPro = proStores.reduce((totalCount, row) => totalCount + row.count, 0);

    const proStoresElements = proStores.map((row) => ({
      type: "rich_text_section",
      elements: [
        {
          type: "text",
          text: `${row.images} images - `,
        },
        {
          type: "text",
          text: `${row.count}`,
        },
      ],
    }));

    return [
      {
        type: "rich_text_section",
        elements: [
          {
            type: "text",
            text: "Subscribed to Image Optimizer - ",
            style: {
              bold: true,
            },
          },
          {
            type: "text",
            text: `${total}`,
          },
          {
            type: "text",
            text: "\n",
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 0,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Free User (50 images) - ",
              },
              {
                type: "text",
                text: `${freeIo}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Pro User - ",
              },
              {
                type: "text",
                text: `${totalPro}`,
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 1,
        elements: proStoresElements,
      },
    ];
  };

  prepareIODataBlock = (ioData) => ({
    type: "rich_text",
    elements: [
      {
        type: "rich_text_section",
        elements: [
          {
            type: "text",
            text: "Queue Status",
            style: {
              bold: true,
            },
          },
          {
            type: "text",
            text: "\n",
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 0,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Queue usage:",
                style: {
                  bold: true,
                },
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 1,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Max - ${ioData?.maxQueueUsage || 0}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Min - ${ioData?.minQueueUsage || 0}`,
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 0,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Stores queued:",
                style: {
                  bold: true,
                },
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 1,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Max - ${ioData?.maxStoreCount || 0}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Min - 3",
                text: `Min - ${ioData?.minStoreCount || 0}`,
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 0,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Images:",
                style: {
                  bold: true,
                },
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 1,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Queued for optimization - ${ioData?.imagesQueued || 0}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Successfully optimized - ${ioData?.imagesProcessed || 0}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Failed to optimize - ${ioData?.imagesFailed || 0}`,
              },
            ],
          },

          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Failed to process - ${ioData?.imagesFailedToProcess || 0}`,
              },
            ],
          },

          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Webhooks received - ${ioData?.webhooksCount || 0}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Invalid images found - ${ioData?.invalidImages || 0}`,
              },
            ],
          },
        ],
      },
    ],
  });

  prepareAiDataBlocks = (aiData) => ({
    type: "rich_text",
    elements: [
      {
        type: "rich_text_section",
        elements: [
          {
            type: "text",
            text: "AI Content Optimizer Stats",
            style: { bold: true },
          },
          { type: "text", text: "\n" },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 0,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Total Stores Tried - ",
                style: { bold: true },
              },
              {
                type: "text",
                text: `${aiData.aiContentSettingStats.total}`,
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 1,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Currently Inactive - `,
              },
              {
                type: "text",
                text: `${aiData.aiContentSettingStats.inactive}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Currently Active - `,
              },
              {
                type: "text",
                text: `${aiData.aiContentSettingStats.active}`,
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 0,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Product AI Content Generated - ",
                style: { bold: true },
              },
              {
                type: "text",
                text: `${aiData.aiContentGenerated}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Image alt-text Generated - ",
                style: { bold: true },
              },
              {
                type: "text",
                text: `${aiData.altTextGenerated}`,
              },
            ],
          },
        ],
      },
    ],
  });

  getIOSubscriptionData = async (startTime, endTime) => {
    const query = `
      WITH io_stats AS (
        SELECT
          id,
          DOMAIN,
          plan_id,
          plan_info ->> 'name' AS plan_name,
          (plan_rules ->> 'products')::DECIMAL AS products,
          (plan_rules ->> 'image_optimizer')::DECIMAL AS images,
          subscribed_at
        FROM
          shops
        WHERE
          subscribed_at BETWEEN '${startTime}' AND '${endTime}'
          AND plan_rules ->> 'image_optimizer' IS NOT NULL
          AND email NOT LIKE '%@wpdeveloper%'
        ORDER BY
          subscribed_at DESC
      )
      SELECT images, count(*) AS count FROM io_stats GROUP BY images ORDER BY images
    `;

    const [results] = await sequelize.query(query, { type: Sequelize.QueryTypes.RAW });

    return results;
  };

  imageOptimizerData = async (date) => {
    // Get stats for all resource types
    const resourceTypes = [ResourceType.PRODUCT, ResourceType.COLLECTION, ResourceType.ARTICLE];
    const resourceQueueNames = resourceTypes.map((resourceType) => `${QUEUE_BASE_NAME}.${resourceType.toLowerCase()}`);

    const allStats = await QueueStats.findAll({
      where: {
        date,
        queue_name: {
          [Op.in]: resourceQueueNames,
        },
      },
      rejectOnEmpty: false,
    });

    // Initialize aggregated data
    let aggregatedData = {
      maxQueueUsage: 0,
      minQueueUsage: Number.MAX_SAFE_INTEGER,
      maxStoreCount: 0,
      minStoreCount: Number.MAX_SAFE_INTEGER,
      imagesQueued: 0,
      imagesProcessed: 0,
      imagesFailedToProcess: 0,
      imagesFailed: 0,
      webhooksCount: 0,
      invalidImages: 0,
    };

    // Aggregate data from all resource types
    for (const stat of allStats) {
      const statistics = stat.statistics || {};

      // For max values, take the maximum across all resource types
      aggregatedData.maxQueueUsage = Math.max(aggregatedData.maxQueueUsage, statistics.maxQueueUsage || 0);
      aggregatedData.maxStoreCount = Math.max(aggregatedData.maxStoreCount, statistics.maxStoreCount || 0);

      // For min values, take the minimum across all resource types (but ignore 0 values)
      if (statistics.minQueueUsage > 0) {
        aggregatedData.minQueueUsage = Math.min(aggregatedData.minQueueUsage, statistics.minQueueUsage);
      }
      if (statistics.minStoreCount > 0) {
        aggregatedData.minStoreCount = Math.min(aggregatedData.minStoreCount, statistics.minStoreCount);
      }

      // For count values, sum across all resource types
      aggregatedData.imagesQueued += statistics.imagesQueued || 0;
      aggregatedData.imagesProcessed += statistics.imagesProcessed || 0;
      aggregatedData.imagesFailedToProcess += statistics.imagesFailedToProcess || 0;
      aggregatedData.imagesFailed += statistics.imagesFailed || 0;
      aggregatedData.webhooksCount += statistics.webhooksCount || 0;
      aggregatedData.invalidImages += statistics.invalidImages || 0;
    }

    // Handle edge cases for min values
    if (aggregatedData.minQueueUsage === Number.MAX_SAFE_INTEGER) {
      aggregatedData.minQueueUsage = 0;
    }
    if (aggregatedData.minStoreCount === Number.MAX_SAFE_INTEGER) {
      aggregatedData.minStoreCount = 0;
    }

    return aggregatedData;
  };

  /**
   * Get comprehensive AI content optimization statistics
   * Includes both bulk (manual) and auto optimization data from bulk_operation_tracker
   * @param {string} date - Date in YYYY-MM-DD format
   * @returns {Promise<Object>} Complete AI optimization statistics
   */
  getAiContentOptimizationStats = async (date) => {
    try {
      // Filter by end_date to capture operations that completed on the target date
      const targetDate = moment(date).format("YYYY-MM-DD");

      // Get bulk (manual) content optimizations - no AUTO prefix
      const bulkContentOptimizations = await BulkOperationTracker.findAll({
        where: {
          op_type: ResourceOPType.AI_OPTIMIZATION,
          resource_type: {
            [Op.in]: [ResourceType.PRODUCT, ResourceType.COLLECTION, ResourceType.ARTICLE],
          },
          end_date: {
            [Op.between]: [moment(targetDate).startOf("day").toDate(), moment(targetDate).endOf("day").toDate()],
          },
        },
        attributes: ["shop_id", [sequelize.fn("SUM", sequelize.col("success_count")), "total_success_count"]],
        group: ["shop_id"],
      });

      // Get bulk (manual) alt-text optimizations - no AUTO prefix
      const bulkAltTextOptimizations = await BulkOperationTracker.findAll({
        where: {
          op_type: ResourceOPType.AI_OPTIMIZATION,
          resource_type: ResourceType.PRODUCT_IMAGE,
          end_date: {
            [Op.between]: [moment(targetDate).startOf("day").toDate(), moment(targetDate).endOf("day").toDate()],
          },
        },
        attributes: ["shop_id", [sequelize.fn("SUM", sequelize.col("success_count")), "total_success_count"]],
        group: ["shop_id"],
      });

      // Get auto content optimizations - with AUTO prefix
      const autoContentOptimizations = await BulkOperationTracker.findAll({
        where: {
          op_type: `AUTO_${ResourceOPType.AI_OPTIMIZATION}`,
          resource_type: {
            [Op.in]: [ResourceType.PRODUCT, ResourceType.COLLECTION, ResourceType.ARTICLE],
          },
          end_date: {
            [Op.between]: [moment(targetDate).startOf("day").toDate(), moment(targetDate).endOf("day").toDate()],
          },
        },
        attributes: ["shop_id", [sequelize.fn("SUM", sequelize.col("success_count")), "total_success_count"]],
        group: ["shop_id"],
      });

      // Get auto alt-text optimizations - with AUTO prefix
      const autoAltTextOptimizations = await BulkOperationTracker.findAll({
        where: {
          op_type: `AUTO_${ResourceOPType.AI_OPTIMIZATION}`,
          resource_type: ResourceType.PRODUCT_IMAGE,
          end_date: {
            [Op.between]: [moment(targetDate).startOf("day").toDate(), moment(targetDate).endOf("day").toDate()],
          },
        },
        attributes: ["shop_id", [sequelize.fn("SUM", sequelize.col("success_count")), "total_success_count"]],
        group: ["shop_id"],
      });

      // Calculate totals
      const bulkContentTotal = bulkContentOptimizations.reduce(
        (sum, item) => sum + parseInt(item.get("total_success_count") || 0),
        0
      );

      const bulkAltTextTotal = bulkAltTextOptimizations.reduce(
        (sum, item) => sum + parseInt(item.get("total_success_count") || 0),
        0
      );

      const autoContentTotal = autoContentOptimizations.reduce(
        (sum, item) => sum + parseInt(item.get("total_success_count") || 0),
        0
      );

      const autoAltTextTotal = autoAltTextOptimizations.reduce(
        (sum, item) => sum + parseInt(item.get("total_success_count") || 0),
        0
      );

      // Calculate stores served - combining both bulk and auto
      const bulkContentStoreIds = new Set(bulkContentOptimizations.map((item) => item.shop_id));
      const bulkAltTextStoreIds = new Set(bulkAltTextOptimizations.map((item) => item.shop_id));
      const autoContentStoreIds = new Set(autoContentOptimizations.map((item) => item.shop_id));
      const autoAltTextStoreIds = new Set(autoAltTextOptimizations.map((item) => item.shop_id));

      // Content stores served (bulk + auto)
      const contentStoresServed = new Set([...bulkContentStoreIds, ...autoContentStoreIds]).size;

      // Alt-text stores served (bulk + auto)
      const altTextStoresServed = new Set([...bulkAltTextStoreIds, ...autoAltTextStoreIds]).size;

      // Total unique stores served (all operations combined)
      const totalStoresServed = new Set([
        ...bulkContentStoreIds,
        ...bulkAltTextStoreIds,
        ...autoContentStoreIds,
        ...autoAltTextStoreIds,
      ]).size;

      // Auto-only stores served (for auto optimization breakdown)
      const autoContentStoresServed = autoContentOptimizations.length;
      const autoAltTextStoresServed = autoAltTextOptimizations.length;
      const autoTotalStoresServed = new Set([...autoContentStoreIds, ...autoAltTextStoreIds]).size;

      return {
        // Total optimizations (bulk + auto)
        aiContentGenerated: bulkContentTotal + autoContentTotal,
        altTextGenerated: bulkAltTextTotal + autoAltTextTotal,

        // Stores served (bulk + auto combined)
        storesServed: {
          contentStoresServed,
          altTextStoresServed,
          totalStoresServed,
        },

        // Auto optimization breakdown
        autoOptimization: {
          contentAutoOptimizations: autoContentTotal,
          altTextAutoOptimizations: autoAltTextTotal,
          contentStoresServed: autoContentStoresServed,
          altTextStoresServed: autoAltTextStoresServed,
          totalStoresServed: autoTotalStoresServed,
        },
      };
    } catch (error) {
      logger.error(`Error collecting AI content optimization stats for ${date}:`, error);

      // Return default values on error
      return {
        aiContentGenerated: 0,
        altTextGenerated: 0,
        storesServed: {
          contentStoresServed: 0,
          altTextStoresServed: 0,
          totalStoresServed: 0,
        },
        autoOptimization: {
          contentAutoOptimizations: 0,
          altTextAutoOptimizations: 0,
          contentStoresServed: 0,
          altTextStoresServed: 0,
          totalStoresServed: 0,
        },
      };
    }
  };

  /**
   * Get blog auto-write statistics
   * @param {string} date - Date in YYYY-MM-DD format
   * @returns {Promise<Object>} Blog auto-write statistics
   */
  getBlogAutoWriteStats = async (date) => {
    try {
      const targetDate = moment(date).format("YYYY-MM-DD");

      // Jobs initiated - filter by created_at
      const jobsInitiated = await BlogAutoWriteJob.count({
        where: {
          created_at: {
            [Op.between]: [moment(targetDate).startOf("day").toDate(), moment(targetDate).endOf("day").toDate()],
          },
        },
      });

      // Completed jobs - filter by updated_at and status
      const completedJobs = await BlogAutoWriteJob.count({
        where: {
          status: BlogAutoWriteJobStatus.COMPLETED,
          updated_at: {
            [Op.between]: [moment(targetDate).startOf("day").toDate(), moment(targetDate).endOf("day").toDate()],
          },
        },
      });

      // Cancelled jobs - filter by updated_at and status
      const cancelledJobs = await BlogAutoWriteJob.count({
        where: {
          status: BlogAutoWriteJobStatus.CANCELLED,
          updated_at: {
            [Op.between]: [moment(targetDate).startOf("day").toDate(), moment(targetDate).endOf("day").toDate()],
          },
        },
      });

      // Failed jobs - filter by updated_at and status
      const failedJobs = await BlogAutoWriteJob.count({
        where: {
          status: BlogAutoWriteJobStatus.FAILED,
          updated_at: {
            [Op.between]: [moment(targetDate).startOf("day").toDate(), moment(targetDate).endOf("day").toDate()],
          },
        },
      });

      // Stores served - unique shop_ids from jobs initiated on target date
      const storesServedResult = await BlogAutoWriteJob.findAll({
        where: {
          created_at: {
            [Op.between]: [moment(targetDate).startOf("day").toDate(), moment(targetDate).endOf("day").toDate()],
          },
        },
        attributes: [[sequelize.fn("COUNT", sequelize.fn("DISTINCT", sequelize.col("shop_id"))), "unique_stores"]],
      });

      const storesServed = parseInt(storesServedResult[0]?.get("unique_stores") || 0);

      return {
        jobsInitiated,
        completedJobs,
        cancelledJobs,
        failedJobs,
        storesServed,
      };
    } catch (error) {
      logger.error(`Error collecting blog auto-write stats for ${date}:`, error);

      // Return default values on error
      return {
        jobsInitiated: 0,
        completedJobs: 0,
        cancelledJobs: 0,
        failedJobs: 0,
        storesServed: 0,
      };
    }
  };
}

module.exports = new SlackService();
