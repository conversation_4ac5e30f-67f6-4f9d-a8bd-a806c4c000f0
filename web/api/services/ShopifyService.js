// @ts-check

// Node built-in modules
const fs = require("fs");
const fsPromise = require("fs/promises");

// Third party modules
const { isEmpty, first } = require("lodash");
const axios = require("axios");
const FormData = require("form-data");

// Local utils & clients
const { graphQLClient, prepareQueryString, graphQLClientWithDBSession } = require("../utils/shopify.clients");

// GraphQL queries & fragments
// Shop
const shopQuery = require("../queries/query.shop.gql");

// Subscriptions
const appSubscriptionFragment = require("../queries/subscriptions/fragment.app-subscription.gql");
const activeSubscriptionQuery = require("../queries/subscriptions/query.active-subscription.gql");
const subscriptionQuery = require("../queries/subscriptions/query.subscription.gql");
const subscriptionCreateMutation = require("../queries/subscriptions/mutation.subscription-create.gql");
const subscriptionCancelMutation = require("../queries/subscriptions/mutation.subscription-cancel.gql");
const onetimePurchaseCreateMutation = require("../queries/subscriptions/mutation.onetime-purchase-create.gql");
const usageChargeCreateMutation = require("../queries/subscriptions/mutation.usage-charge-create.gql");

// Products
const productQuery = require("../queries/products/query.product.gql");
const productsQuery = require("../queries/products/query.products.gql");
const productFragment = require("../queries/products/fragment.product.gql");
const updateProductMetaMutation = require("../queries/products/mutation.update-product-meta.gql");
const updateProductImageMutation = require("../queries/products/mutation.update-product-image.gql");
const updateProductSitemapMutation = require("../queries/products/mutation.update-product-sitemap.gql");

// Common fragments
const metaFragment = require("../queries/fragment.metafield.gql");
const pageInfoFragment = require("../queries/fragment.page-info.gql");
const mediaImageFragment = require("../queries/fragment.media-image.gql");

// Files
const filesQuery = require("../queries/files/query.files.gql");
const filesQueryWithBeforePagination = require("../queries/files/query.files-before-pagination.gql");
const filesQueryWithMediaIdWithUrl = require("../queries/files/query.files-media-id-with-url.gql");
const updateImageFilesMutation = require("../queries/files/mutation.update-files.gql");

// Bulk operations
const stagedUploadCreateMutation = require("../queries/mutations/mutation.staged-upload-create.gql");
const bulkOperationRunQuery = require("../queries/bulk-operation/mutation.bulk-operation-run-query.gql");
const productsGetQuery = require("../queries/bulk-operation/query.products-get.gql");
const bulkOperationRunMutation = require("../queries/bulk-operation/mutation.bulk-operation-run-mutation.gql");

// Collections
const collectionFragment = require("../queries/collections/fragment.collection.gql");
const collectionQuery = require("../queries/collections/query.collection.gql");
const collectionsQuery = require("../queries/collections/query.collections.gql");
const collectionProductsQuery = require("../queries/collections/query.collection-products.gql");
const updateCollectionMutation = require("../queries/collections/mutation.update-collection.gql");
const metafieldsSet = require("../queries/mutations/mutation.set-metafields.gql");
const mediaImagesQuery = require("../queries/query.media-images.gql");

// Serializers & enums
const {
  serializeVariablesWithAddons,
  serializeOnetimePurchaseVariables,
} = require("../serializers/AppSubscriptionSerializer");
const { METAFIELD_DEFINITIONS, METAFIELD_TYPES } = require("storeseo-enums/metafields");

// Error types
const { InactiveShopError } = require("../../errors");
const LocalaizationService = require("./shopify/LocalaizationService");
const { currency } = require("../config/app");
const OnlineStoreService = require("./shopify/OnlineStoreService");

class ShopifyService {
  localaizations = new LocalaizationService();
  onlineStore = new OnlineStoreService();

  /**
   * Get shop details from Shopify.
   * @param {string} shop - The shop myshopify domain.
   * @returns {Promise<Object>}
   */
  getShopDetailsFromShopify = async (shop) => {
    const { data } = await graphQLClient(shop, { query: shopQuery });
    return data.shop;
  };

  /**
   * Set a single metafield for a shop.
   * @param {string} shop - The shop myshopify domain.
   * @param {{ ownerId: string, key: string, value: string }} param0
   * @returns {Promise<Object>}
   */
  setMetafield = async (shop, { ownerId, key, value }) => {
    const metafieldDefinition = METAFIELD_DEFINITIONS[key];
    let processedValue = value;

    // Replace newline characters with spaces for single line text fields
    if (metafieldDefinition?.type === METAFIELD_TYPES.SINGLE_LINE_TEXT) {
      processedValue = value?.replace(/[\r\n\u2028\u2029]+/g, " ").trim() || "";
    }

    const metafield = { ...metafieldDefinition, ownerId, value: processedValue };

    const input = {
      metafields: [metafield],
    };

    const { data } = await graphQLClient(shop, {
      query: metafieldsSet,
      variables: input,
      fragments: [metaFragment],
    });
    return data;
  };

  /**
   * Set multiple metafields for a shop.
   * @param {string} shop - The shop myshopify domain.
   * @param {Array<{ ownerId: string, key: string, value: string, namespace: string, type: string }>} metas
   * @returns {Promise<Object>}
   */
  setMetafields = async (shop, metas) => {
    const metafields = metas.map((meta) => {
      const metafieldDefinition = METAFIELD_DEFINITIONS[meta.key];
      let processedValue = meta.value;

      // Replace newline characters with spaces for single line text fields
      if (metafieldDefinition?.type === METAFIELD_TYPES.SINGLE_LINE_TEXT) {
        processedValue = meta.value?.replace(/[\r\n\u2028\u2029]+/g, " ").trim() || "";
      }

      return {
        ...metafieldDefinition,
        ownerId: meta.ownerId,
        value: processedValue,
      };
    });

    const input = {
      metafields,
    };

    const { data } = await graphQLClient(shop, {
      query: metafieldsSet,
      variables: input,
      fragments: [metaFragment],
    });

    return data;
  };

  /**
   * Retrieve image files from Shopify.
   * @param {string} shop - The shop myshopify domain.
   * @param {{ limit?: number, after?: string|null, before?: string|null, filename?: string|null, productId?: string|null }} [options={ limit: 20, filename: "", productId: "" }]
   * @returns {Promise<Object>}
   */
  getImageFilesFromShopify = async (shop, options = { limit: 20, filename: "", productId: "" }) => {
    const { limit, after = null, before = null, filename, productId } = options;

    const input = {
      first: limit,
      after,
      query: `(media_type:IMAGE)`,
    };

    if (before) {
      input.last = limit;
      input.before = before;
    }

    if (filename) {
      input["query"] += ` AND (filename:*${filename})`;
    }
    if (productId) {
      input["query"] += ` AND (product_id:${productId})`;
    }

    const query = before ? filesQueryWithBeforePagination : filesQuery;

    const {
      data: { files },
      extensions: { cost },
    } = await graphQLClient(shop, {
      query,
      fragments: [mediaImageFragment, pageInfoFragment],
      variables: input,
    });

    return files;
  };

  /**
   * Update image files.
   * @param {string} shop - The shop myshopify domain.
   * @param {Array<{ id: string, originalSource: string, alt: string }>} files
   * @returns {Promise<Object>}
   */
  updateImageFiles = async (shop, files) => {
    const { data } = await graphQLClient(shop, {
      query: updateImageFilesMutation,
      fragments: [mediaImageFragment],
      variables: { files },
    });
    return data;
  };

  /**
   * Retrieve products from Shopify.
   * @param {string} shop - The shop myshopify domain.
   * @param {number} [first=1]
   * @param {string|null} [after=null]
   * @returns {Promise<Object>}
   */
  getProductsFromShopify = async (shop, first = 1, after = null) => {
    const {
      data: { products },
    } = await graphQLClient(shop, {
      query: productsQuery,
      fragments: [productFragment, metaFragment, mediaImageFragment, pageInfoFragment],
      variables: { first, after },
    });
    return products;
  };

  /**
   * Get a product from Shopify by its GraphQL id.
   * @param {string} shop - The shop myshopify domain.
   * @param {string} id
   * @returns {Promise<Object>}
   */
  getProductFromShopify = async (shop, id) => {
    const {
      data: { product },
    } = await graphQLClient(shop, {
      query: productQuery,
      fragments: [productFragment, metaFragment, mediaImageFragment],
      variables: { id },
    });

    return product;
  };

  /**
   * Save product metadata to Shopify.
   * @param {string} shop - The shop myshopify domain.
   * @param {{ productId: number, metadata: Array<any>, tags: Array<any>, handle?: string|null }} params
   * @returns {Promise<Object>}
   */
  saveShopifyProductMeta = async (shop, { productId, metadata, tags, handle = null }) => {
    // Process metafields to replace newlines with spaces for single line text fields
    const processedMetadata = metadata.map((meta) => {
      if (meta.type === METAFIELD_TYPES.SINGLE_LINE_TEXT) {
        return {
          ...meta,
          value: meta.value?.replace(/[\r\n\u2028\u2029]+/g, " ").trim() || "",
        };
      }
      return meta;
    });

    const variables = {
      product: {
        id: productId,
        metafields: processedMetadata,
        tags: !isEmpty(tags) ? tags : null,
        handle: handle || undefined,
      },
    };

    console.log("\n***\ngraphql variables in product update: ", JSON.stringify(variables, null, 2));

    const { data } = await graphQLClient(shop, {
      variables,
      query: updateProductMetaMutation,
      fragments: [metaFragment],
    });

    return data;
  };

  /**
   * Update product sitemap metafields.
   * @param {string} shop - The shop myshopify domain.
   * @param {{ productId: number, metadata: Array<any> }} params
   * @returns {Promise<Object>}
   */
  updateProductSitemapMeta = async (shop, { productId, metadata }) => {
    const variables = {
      product: {
        id: productId,
        metafields: metadata,
      },
    };

    const { data } = await graphQLClient(shop, {
      variables,
      query: updateProductSitemapMutation,
      fragments: [metaFragment],
    });

    return data;
  };

  /**
   * Update product metafields.
   * @param {string} shop - The shop myshopify domain.
   * @param {{ productId: number, metaFieldDefinitions: Array<any> }} params
   * @returns {Promise<Object>}
   */
  updateProductMetaFields = async (shop, { productId, metaFieldDefinitions }) => {
    // Process metafields to replace newlines with spaces for single line text fields
    const processedMetaFieldDefinitions = metaFieldDefinitions.map((meta) => {
      if (meta.type === METAFIELD_TYPES.SINGLE_LINE_TEXT) {
        return {
          ...meta,
          value: meta.value?.replace(/[\r\n\u2028\u2029]+/g, " ").trim() || "",
        };
      }
      return meta;
    });

    const variables = {
      product: {
        id: productId,
        metafields: processedMetaFieldDefinitions,
      },
    };

    const { data } = await graphQLClient(shop, {
      variables,
      query: updateProductMetaMutation,
      fragments: [metaFragment],
    });
    return data;
  };

  /**
   * Update product images.
   * @param {string} shop - The shop myshopify domain.
   * @param {{ productId: number, images: Array<any> }} params
   * @returns {Promise<Object>}
   */
  updateProductImages = async (shop, { productId, images }) => {
    const variables = {
      product: {
        id: productId,
        images: images,
      },
    };

    const { data } = await graphQLClient(shop, {
      query: updateProductImageMutation,
      variables,
      fragments: [mediaImageFragment],
    });

    return data;
  };

  /**
   * Create an app subscription.
   * @param {string} shop - The shop myshopify domain.
   * @param {{ shopDetails: Object, plan: Object, coupon: Object, addons?: Array<any>, withTrial?: boolean }} params
   * @returns {Promise<{ appSubscription: Object, confirmationUrl: string }>}
   */
  appSubscriptionCreate = async (shop, { shopDetails, plan, coupon, addons = [], withTrial = true }) => {
    const url = await this.getAppURL(shop);
    const variables = serializeVariablesWithAddons(plan, url, coupon, addons, withTrial);

    variables.test = shopDetails?.email?.includes("@wpdeveloper.com") || false;

    console.log("Subscription Create variables =", JSON.stringify(variables, null, 2));

    const {
      data: {
        appSubscriptionCreate: { appSubscription, confirmationUrl, userErrors },
      },
    } = await graphQLClient(shop, {
      query: subscriptionCreateMutation,
      variables,
      fragments: [appSubscriptionFragment],
    });

    if (userErrors.length > 0) {
      throw new Error(userErrors[0].message);
    }

    return { appSubscription, confirmationUrl };
  };

  /**
   * Cancel an app subscription.
   * @param {string} shop - The shop myshopify domain.
   * @param {string} appSubscriptionId
   * @returns {Promise<Object>}
   */
  appSubscriptionCancel = async (shop, appSubscriptionId) => {
    const variables = {
      id: appSubscriptionId,
    };

    const { data } = await graphQLClient(shop, {
      query: subscriptionCancelMutation,
      variables,
      fragments: [appSubscriptionFragment],
    });

    return data;
  };

  /**
   * Create a one-time purchase.
   * @param {string} shop - The shop myshopify domain.
   * @param {{ shopDetails: Object, addons?: Array<any>, coupon?: Object|null }} params
   * @returns {Promise<{ appPurchaseOneTime: Object, confirmationUrl: string }>}
   */
  appPurchaseOneTimeCreate = async (shop, { shopDetails, addons = [], coupon = null }) => {
    const url = await this.getAppURL(shop);
    const variables = serializeOnetimePurchaseVariables(url, addons, coupon);

    if (shopDetails.email && shopDetails.email.includes("@wpdeveloper.com")) {
      variables.test = true;
    }

    console.log(shopDetails.email, JSON.stringify(variables, null, 2));

    const {
      data: {
        appPurchaseOneTimeCreate: { appPurchaseOneTime, confirmationUrl, userErrors },
      },
    } = await graphQLClient(shop, {
      query: onetimePurchaseCreateMutation,
      variables,
    });

    if (userErrors.length > 0) {
      throw new Error(userErrors[0].message);
    }

    return { appPurchaseOneTime, confirmationUrl };
  };

  /**
   * Execute a bulk product request.
   * @param {string} shop - The shop myshopify domain.
   * @param {number|null} [limit=null]
   * @returns {Promise<Object>}
   */
  bulkProductRequest = async (shop, limit = null) => {
    let bulkProductsGetQuery = prepareQueryString(productsGetQuery, [
      productFragment,
      metaFragment,
      mediaImageFragment,
    ]);
    let productsFilter = `query: "status:ACTIVE"`;

    if (limit) {
      bulkProductsGetQuery = bulkProductsGetQuery.replace(productsFilter, `${productsFilter}, first: ${limit}`);
    }

    const {
      data: {
        bulkOperationRunQuery: { bulkOperation, userErrors },
      },
    } = await graphQLClient(shop, {
      query: bulkOperationRunQuery,
      variables: { bulkQuery: bulkProductsGetQuery, productsFilter },
    });

    if (userErrors.length > 0) {
      throw new Error(userErrors[0]?.message);
    }
    return bulkOperation;
  };

  /**
   * Create a staged upload process in Shopify.
   * @param {string} shop - The shop myshopify domain.
   * @param {string} fileName
   * @param {string|number} fileSize
   * @returns {Promise<Array<Object>>}
   */
  createStagedUploadProcessInShopify = async (shop, fileName, fileSize) => {
    const input = {
      input: {
        resource: "BULK_MUTATION_VARIABLES",
        filename: fileName,
        fileSize: String(fileSize),
        mimeType: "text/jsonl",
        httpMethod: "POST",
      },
    };

    const {
      data: {
        stagedUploadsCreate: { stagedTargets, userErrors },
      },
    } = await graphQLClient(shop, {
      query: stagedUploadCreateMutation,
      variables: input,
    });

    if (userErrors.length > 0) {
      throw new Error(userErrors[0]?.message);
    }

    return stagedTargets;
  };

  /**
   * Upload a bulk operation input file into Shopify.
   * @param {{ shop: string, fileName: string, filePath: string }} param0
   * @returns {Promise<{ status: number, stagedTarget: Object }>}
   */
  uploadBulkOperationInputFileIntoShopify = async ({ shop, fileName, filePath }) => {
    const fileSize = (await fsPromise.stat(filePath)).size;
    const [stagedTarget] = await this.createStagedUploadProcessInShopify(shop, fileName, fileSize);

    const status = await new Promise(async (resolve, reject) => {
      const { url, parameters } = stagedTarget;

      const formData = new FormData();

      for (let p of parameters) {
        formData.append(p.name, p.value);
      }

      formData.append("file", fs.createReadStream(filePath));

      formData.getLength(async (err, length) => {
        // console.table(["request body size", length]);
        try {
          const { status } = await axios.post(url, formData, {
            headers: formData.getHeaders(),
            maxBodyLength: length,
            maxContentLength: length,
          });

          console.log("\n---");
          console.log("status: ", status);
          console.log("---\n");
          return resolve(status);
        } catch (err) {
          reject(err);
        }
      });
    });

    return { status, stagedTarget };
  };

  /**
   * Start a bulk operation mutation.
   * @param {string} shop - The shop myshopify domain.
   * @param {string} stagedUploadPath
   * @param {string} mutationQuery
   * @returns {Promise<Object>}
   */
  startBulkOperationMutation = async (shop, stagedUploadPath, mutationQuery) => {
    const mutation = prepareQueryString(mutationQuery);
    const {
      data: {
        bulkOperationRunMutation: { bulkOperation, userErrors },
      },
    } = await graphQLClient(shop, {
      query: bulkOperationRunMutation,
      variables: { mutation, stagedUploadPath },
    });
    console.log("userErrors =", userErrors);

    if (userErrors.length > 0) {
      throw new Error(userErrors[0].message);
    }

    return bulkOperation;
  };

  /**
   * Get bulk operation details.
   * @param {string} shop - The shop myshopify domain.
   * @param {string} id
   * @returns {Promise<Object>}
   */
  getBulkOperationDetails = async (shop, id) => {
    const query = `
      query ($id: ID!) {
        node(id: $id) {
          ... on BulkOperation {
            id
            type
            status
            url
            partialDataUrl
            completedAt
          }
        }
      }
    `;
    const {
      data: { node: bulkOperation },
    } = await graphQLClient(shop, {
      query,
      variables: { id },
    });

    return bulkOperation;
  };

  /**
   * Get the active subscription.
   * @param {string} shop - The shop myshopify domain.
   * @returns {Promise<Object|null>}
   */
  getActiveSubscription = async (shop) => {
    try {
      const {
        data: {
          appInstallation: { activeSubscriptions },
        },
      } = await graphQLClient(shop, { query: activeSubscriptionQuery, fragments: [appSubscriptionFragment] });

      return activeSubscriptions.length > 0 ? first(activeSubscriptions) : null;
    } catch (err) {
      console.error("err =", err);
      return null;
    }
  };

  /**
   * Get the active subscription.
   * @param {string} shop - The shop myshopify domain.
   * @param {string} appSubscriptionId - The app subscription graphql ID.
   * @returns {Promise<Object|null>}
   */
  getSubscriptionByID = async (shop, appSubscriptionId) => {
    try {
      const {
        data: { node: activeSubscription },
      } = await graphQLClient(shop, {
        query: subscriptionQuery,
        fragments: [appSubscriptionFragment],
        variables: { id: appSubscriptionId },
      });

      return activeSubscription;
    } catch (err) {
      console.error("err =", err);
      return null;
    }
  };

  /**
   * Retrieve the app handle.
   * @param {string} shop - The shop myshopify domain.
   * @returns {Promise<string>}
   */
  getAppHandle = async (shop) => {
    const query = `
        {
          app {
            id
            title
            handle
          }
        }
      `;
    const {
      data: {
        app: { handle },
      },
    } = await graphQLClient(shop, { query });
    return handle;
  };

  /**
   * Get the app launch URL.
   * @param {string} shop - The shop myshopify domain.
   * @returns {Promise<string>}
   */
  getAppURL = async (shop) => {
    try {
      const query = `
        {
          app {
            id
            title
            handle
            installation {
              launchUrl
            }
          }
        }
      `;
      const {
        data: {
          app: { installation },
        },
      } = await graphQLClient(shop, { query });
      return installation.launchUrl;
    } catch (err) {
      console.log(err);
      throw Error(err.response.errors);
    }
  };

  /**
   * Calculate REST API rate limit.
   * @param {Object} headers
   * @returns {number}
   */
  calculateRestApiRateLimit = (headers) => {
    const apiCallLimit = headers["X-Shopify-Shop-Api-Call-Limit"];
    const apiCallLimitParts = apiCallLimit[0].split("/");
    return apiCallLimitParts[1] - apiCallLimitParts[0];
  };

  /**
   * Retrieve product IDs from Shopify.
   * @param {string} shop - The shop myshopify domain.
   * @param {{ first?: number, after?: string|null }} param0
   * @returns {Promise<Object>}
   */
  getProductIdsFromShopify = async (shop, { first = 250, after = null }) => {
    const query = `query ($first: Int, $after: String) {
      products(first: $first, after: $after, query: "status:ACTIVE", sortKey: CREATED_AT) {
        edges {
          node {
            id
          }
        }
        pageInfo {
          hasNextPage
          hasPreviousPage
          endCursor
          startCursor
        }
      }
    }`;
    const {
      data: { products },
    } = await graphQLClient(shop, {
      query,
      variables: { first, after },
    });
    return products;
  };

  /**
   * Create a usage charge.
   * @param {string} shop - The shop myshopify domain.
   * @param {Object} data
   * @returns {Promise<Object>}
   */
  createUsageCharge = async (shop, data) => {
    const input = {
      description: data.description,
      idempotencyKey: data.key,
      price: {
        amount: data.price.toFixed(2),
        currencyCode: currency,
      },
      subscriptionLineItemId: data.subscription_li_id,
    };

    const {
      data: {
        appUsageRecordCreate: { appUsageRecord, userErrors },
      },
    } = await graphQLClient(shop, { query: usageChargeCreateMutation, variables: input });

    if (userErrors.length > 0) {
      throw new Error(userErrors[0].message);
    }

    return appUsageRecord;
  };

  /**
   * Create a usage charge.
   * @param {object} session  - The shop session in database
   * @param {string} session.shop  - The shop myshopify domain
   * @param {string} session.accessToken  - The shop myshopify access token
   * @param {Object} data
   * @returns {Promise<Object>}
   */
  createUsageChargeUsingDBSession = async (session, data) => {
    const input = {
      description: data.description,
      idempotencyKey: data.key,
      price: {
        amount: data.price.toFixed(2),
        currencyCode: currency,
      },
      subscriptionLineItemId: data.subscription_li_id,
    };

    const {
      data: {
        appUsageRecordCreate: { appUsageRecord, userErrors },
      },
    } = await graphQLClientWithDBSession(session, { query: usageChargeCreateMutation, variables: input });

    if (userErrors.length > 0) {
      throw new Error(userErrors[0].message);
    }

    return appUsageRecord;
  };

  /**
   * Retrieve image media IDs with URLs.
   * @param {string} shop - The shop myshopify domain.
   * @param {{ limit?: number, fileName?: string, productId?: string }} [options={ limit: 20, fileName: "", productId: "" }]
   * @returns {Promise<Object>}
   */
  getImageMediaIdWithUrl = async (shop, options = { limit: 20, fileName: "", productId: "" }) => {
    const { limit, fileName, productId } = options;

    let input = {
      first: limit,
      query: `(media_type:IMAGE)`,
    };

    if (fileName) {
      input["query"] += ` AND (filename:*${fileName})`;
    }
    if (productId) {
      input["query"] += ` AND (product_id:${productId})`;
    }

    const { data } = await graphQLClient(shop, {
      query: filesQueryWithMediaIdWithUrl,
      fragments: [pageInfoFragment],
      variables: input,
    });
    // console.log("input: ", input, "graphql res: ", JSON.stringify(data, null, 2));
    return data;
  };

  /**
   * Check if a redirect URL exists for a specific path.
   * @param {string} shop - The shop myshopify domain.
   * @param {string} path - The path to check for existing redirects
   * @returns {Promise<Object|null>} Existing redirect or null if not found
   */
  getRedirectURL = async (shop, path) => {
    const query = `query ($query: String!) {
      urlRedirects(first: 1, query: $query) {
        edges {
          node {
            id
            path
            target
          }
        }
      }
    }`;

    try {
      const {
        data: { urlRedirects },
      } = await graphQLClient(shop, {
        query,
        variables: { query: `path:${path}` },
      });

      return urlRedirects.edges.length > 0 ? urlRedirects.edges[0].node : null;
    } catch (error) {
      console.warn(`Error checking redirect for path ${path}:`, error.message);
      return null;
    }
  };

  /**
   * Create a redirect URL.
   * @param {string} shop - The shop myshopify domain.
   * @param {{ oldPath: string, newPath: string }} param0
   * @returns {Promise<Object|undefined>}
   */
  createRedirectURL = async (shop, { oldPath, newPath }) => {
    const mutation = `mutation urlRedirectCreate($urlRedirect: UrlRedirectInput!) {
      urlRedirectCreate(urlRedirect: $urlRedirect) {
        urlRedirect {
          id
          path
          target
        }
        userErrors {
          field
          message
        }
      }
    }`;

    const input = {
      urlRedirect: {
        path: oldPath,
        target: newPath,
      },
    };
    const {
      data: {
        urlRedirectCreate: { urlRedirect, userErrors },
      },
    } = await graphQLClient(shop, {
      query: mutation,
      variables: input,
    });

    if (userErrors.length > 0) {
      // console.error(userErrors[0]?.message, { userErrors, input, shopDomain: shop.shop });
      // throw new Error(userErrors[0]?.message);
      console.warn(userErrors[0]?.message);
      return;
    }

    return urlRedirect;
  };

  /**
   * Delete a redirect URL by ID.
   * @param {string} shop - The shop myshopify domain.
   * @param {string} redirectId - The redirect ID to delete
   * @returns {Promise<boolean>} True if deleted successfully
   */
  deleteRedirectURL = async (shop, redirectId) => {
    const mutation = `mutation urlRedirectDelete($id: ID!) {
      urlRedirectDelete(id: $id) {
        deletedUrlRedirectId
        userErrors {
          field
          message
        }
      }
    }`;

    try {
      const {
        data: {
          urlRedirectDelete: { deletedUrlRedirectId, userErrors },
        },
      } = await graphQLClient(shop, {
        query: mutation,
        variables: { id: redirectId },
      });

      if (userErrors.length > 0) {
        console.warn(`Error deleting redirect ${redirectId}:`, userErrors[0]?.message);
        return false;
      }

      return !!deletedUrlRedirectId;
    } catch (error) {
      console.warn(`Error deleting redirect ${redirectId}:`, error.message);
      return false;
    }
  };

  // Collection related methods

  /**
   * Get count of all collections.
   * @param {string} shop - The shop myshopify domain.
   * @returns {Promise<number>}
   */
  getShopifyCollectionCount = async (shop) => {
    const collectionProductsQuery = `
      query CollectionsCount {
        collectionsCount {
          count
        }
      }
    `;
    const {
      data: { collectionsCount },
    } = await graphQLClient(shop, {
      query: collectionProductsQuery,
    });
    return collectionsCount.count;
  };

  /**
   * Get count of products in a collection.
   * @param {string} shop - The shop myshopify domain.
   * @param {string} collectionGqlId
   * @returns {Promise<number>}
   */
  getShopifyCollectionProductsCount = async (shop, collectionGqlId) => {
    const {
      data: { collection },
    } = await graphQLClient(shop, {
      query: collectionProductsQuery,
      fragments: [pageInfoFragment],
      variables: { collectionId: collectionGqlId, first: 1 },
    });
    const { productsCount } = collection;
    return productsCount;
  };

  /**
   * Retrieve a collection by its GraphQL ID.
   * @param {string} shop - The shop myshopify domain.
   * @param {string} id - Collection GraphQL ID.
   * @returns {Promise<Object>}
   */
  getCollectionById = async (shop, id) => {
    const {
      data: { collection },
    } = await graphQLClient(shop, {
      query: collectionQuery,
      fragments: [collectionFragment, metaFragment],
      variables: { id },
    });
    return collection;
  };

  /**
   * Get collections.
   * @param {string} shop - The shop myshopify domain.
   * @param {number} [first=1]
   * @param {string|null} [after=null]
   * @returns {Promise<Object>}
   */
  getCollections = async (shop, first = 1, after = null) => {
    const {
      data: { collections },
    } = await graphQLClient(shop, {
      query: collectionsQuery,
      fragments: [collectionFragment, metaFragment, pageInfoFragment],
      variables: { first, after },
    });
    return collections;
  };

  /**
   * Get products for a collection.
   * @param {string} shop - The shop myshopify domain.
   * @param {string} collectionGqlId
   * @param {number} [first=1]
   * @param {string|null} [after=null]
   * @returns {Promise<Object>}
   */
  getCollectionProducts = async (shop, collectionGqlId, first = 1, after = null) => {
    const {
      data: { collection },
    } = await graphQLClient(shop, {
      query: collectionProductsQuery,
      fragments: [pageInfoFragment],
      variables: { collectionId: collectionGqlId, first, after },
    });
    return collection;
  };

  /**
   * Update a collection's metafields.
   * @param {string} shop - The shop myshopify domain.
   * @param {{ collectionId: string, metadata: Array<any>, handle?: string }} param0
   * @returns {Promise<Object>}
   */
  updateShopifyCollectionMeta = async (shop, { collectionId, metadata, handle }) => {
    // Process metafields to replace newlines with spaces for single line text fields
    const processedMetadata = metadata.map((meta) => {
      if (meta.type === METAFIELD_TYPES.SINGLE_LINE_TEXT) {
        return {
          ...meta,
          value: meta.value?.replace(/[\r\n\u2028\u2029]+/g, " ").trim() || "",
        };
      }
      return meta;
    });

    const input = {
      input: {
        id: collectionId,
        metafields: processedMetadata,
        handle: handle || undefined,
      },
    };

    const { data } = await graphQLClient(shop, {
      query: updateCollectionMutation,
      fragments: [collectionFragment, metaFragment],
      variables: input,
    });
    return data;
  };

  /**
   * Update a collection in Shopify.
   * @param {string} shop
   * @param {{handle: string}} input
   * @returns
   */
  updateCollection = async (shop, input) => {
    const variables = {
      input,
    };

    const { data } = await graphQLClient(shop, {
      query: updateCollectionMutation,
      fragments: [collectionFragment, metaFragment],
      variables,
    });
    return data;
  };

  /**
   * Get media images using media image IDs.
   * @param {string} shop - The shop myshopify domain.
   * @param {Array<string>} mediaImageIds
   * @returns {Promise<Object>}
   */
  getMediaImages = async (shop, mediaImageIds) => {
    const {
      data: { nodes },
    } = await graphQLClient(shop, { query: mediaImagesQuery, variables: { ids: mediaImageIds } });
    return nodes;
  };

  /**
   * @param {string} shop - The shop myshopify domain
   * @returns {Promise<boolean>}
   */
  hasValidSession = async (shop) => {
    try {
      await this.getAppHandle(shop);
      return true;
    } catch (err) {
      throw new InactiveShopError(shop, { cause: err });
    }
  };
}

module.exports = new ShopifyService();
