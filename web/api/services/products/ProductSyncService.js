const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const { NAMESPACE, METAFIELD_KEYS } = require("storeseo-enums/metafields");
const { isExcludedProductType } = require("storeseo-enums/excludedProductTypes");
const AppDataMigrateService = require("../AppDataMigrateService");
const ProductService = require("../ProductService");
const ShopService = require("../ShopService");
const SitemapService = require("../SitemapService");
const ProductReadService = require("./ProductReadService");
const { omit } = require("lodash");
const ProductAnalysisService = require("../ProductAnalysisService");
const EventEmitter = require("node:events");
const { dispatchQueue } = require("../../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../../queue/config");
const resourceType = require("storeseo-enums/resourceType");

class ProductSyncService {
  /** @type {string} */
  #shopDomain;
  /** @type {*} */
  #shop;
  /** @type {Session} */
  #session;
  /** @type {string} */
  #migrateDataFromApp;
  /** @type {number} */
  #dbProductCount;

  /** @type {import("./ProductReadService")} */
  #productReadService;
  #eventEmitter = new EventEmitter();

  #readyForOperation = false;

  constructor(shopDomain, migrateDataFromApp) {
    this.#shopDomain = shopDomain;
    this.#migrateDataFromApp = migrateDataFromApp;

    this.#loadStoreDetails();
    this.#productReadService = new ProductReadService(shopDomain);
  }

  /** @returns {Promise<{ syncedProducts: *[], dbProductCount: number, planLimit: number | null }>} */
  async syncNextProducts() {
    await this.#readyState();

    const productSyncLimit = this.#calculateNumberOfProductsToSync();

    const shopifyProducts = await this.#productReadService.readNextShopifyProducts(productSyncLimit);
    const syncedProducts = await this.#saveProducts(shopifyProducts);

    this.#dbProductCount += syncedProducts.length;

    return { syncedProducts, dbProductCount: this.#dbProductCount, planLimit: this.#shop.plan_rules.products };
  }

  async #readyState() {
    if (this.#readyForOperation) return true;

    return new Promise((resolve) => {
      this.#eventEmitter.on("ready", () => resolve(true));
    });
  }

  #calculateNumberOfProductsToSync() {
    const { plan_rules } = this.#shop;
    const planLimit = plan_rules.products || Number.POSITIVE_INFINITY;
    const remainingLimit = planLimit - this.#dbProductCount;

    const chunkSize = 5;

    return remainingLimit < chunkSize ? remainingLimit : chunkSize;
  }

  /**
   *
   * @returns {Promise<any[]>}
   */
  async #saveProducts(shopifyProducts) {
    const savedProducts = [];

    for (let shopifyProduct of shopifyProducts) {
      // Skip products from EasyFlow app and other excluded product types
      if (isExcludedProductType(shopifyProduct.productType)) {
        console.log(
          `[${this.#shopDomain}] Skipping excluded product type: ${shopifyProduct.productType} for product: ${shopifyProduct.id}`
        );
        continue;
      }

      const serializedProduct = this.#serializeShopifyProduct(shopifyProduct);

      if (this.#migrateDataFromApp) {
        shopifyProduct = await AppDataMigrateService.collectAndMergeOtherAppData(
          this.#migrateDataFromApp,
          serializedProduct,
          this.#shopDomain,
          this.#session
        );
      }

      const savedProduct = await ProductService.saveOrUpdateProduct(this.#shop.id, serializedProduct);
      await SitemapService.storeSitemapData(savedProduct, analysisEntityTypes.PRODUCT);
      await ProductAnalysisService.analyseEachProduct({
        shopId: this.#shop.id,
        product: savedProduct,
      });

      if (!savedProduct.focus_keyword) {
        dispatchQueue({
          queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
          message: {
            shopId: this.#shop.id,
            dbResourceId: savedProduct.id,
            resourceType: resourceType.PRODUCT,
          },
        });
      }

      savedProducts.push(savedProduct);
    }

    return savedProducts;
  }

  /**
   *
   * @param {*} shopifyProduct
   * @returns {*}
   */
  #serializeShopifyProduct(shopifyProduct) {
    const serializedProduct = omit(shopifyProduct, ["meta", "mediaImages"]);
    serializedProduct.descriptionHtml = serializedProduct.description || serializedProduct.descriptionHtml || "";

    if (!serializedProduct.featuredMedia) {
      serializedProduct.featuredMedia = {
        id: shopifyProduct.mediaImages[0]?.id,
      };
    }

    serializedProduct.metafields = {
      edges: shopifyProduct.meta
        .filter(
          (m) =>
            m.namespace === NAMESPACE.GLOBAL &&
            (m.key?.endsWith(METAFIELD_KEYS.TITLE_TAG) || m.key?.endsWith(METAFIELD_KEYS.DESCRIPTION_TAG))
        )
        .map((m) => ({ node: { ...omit(m, ["__parentId"]) } })),
    };

    serializedProduct.storeSEOMetaFields = {
      edges: shopifyProduct.meta
        .filter((m) => m.namespace === NAMESPACE.STORE_SEO)
        .map((m) => ({ node: { ...omit(m, ["__parentId"]) } })),
    };

    serializedProduct.mediaImages = {
      edges: shopifyProduct.mediaImages.map((media) => ({
        node: {
          id: media.id,
          url: media.image.url,
          altText: media.image.altText,
          fileSize: media.originalSource.fileSize,
        },
      })),
    };

    return serializedProduct;
  }

  async #loadStoreDetails() {
    this.#shop = await ShopService.getShop(this.#shopDomain);
    this.#session = { shop: this.#shopDomain, accessToken: this.#shop?.access_token };

    this.#dbProductCount = await ProductService.countProducts(this.#shop.id);

    this.#readyForOperation = true;
    this.#eventEmitter.emit("ready");
  }
}

module.exports = ProductSyncService;
