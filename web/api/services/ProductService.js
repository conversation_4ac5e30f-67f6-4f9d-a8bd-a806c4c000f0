const cron = require("node-cron");
const { isEmpty } = require("lodash");
const {
  Product,
  ProductImage,
  ProductMeta,
  ProductImageMapper,
  sequelize,
  Sitemap,
  Op,
  MultiLanguageProducts,
  ProductAnalysis,
  Sequelize,
} = require("../../sequelize");
const ShopifyService = require("./ShopifyService");
const AppDataMigrateService = require("./AppDataMigrateService");
const ProductMetaService = require("./ProductMetaService");
const ProductImageService = require("./ProductImageService");
const ProductAnalysisService = require("./ProductAnalysisService");
const SitemapService = require("./SitemapService");
const logger = require("storeseo-logger");
const {
  replaceSpecialChars,
  extractFocusKeyword,
  preparePagination,
  formatFromDateForQuery,
  formatToDateForQuery,
  extractShopifyIdFromGqlId,
} = require("../utils/helper");
const urlNotificationType = require("storeseo-enums/urlNotificationType");

const { serializeShopifyProductMetas, serializeUpdatedMetadata } = require("../serializers/ProductMetaSerializer");
const { serializeShopifyProductImages, flattenMediaImages } = require("../serializers/ProductImageSerializer");
const {
  serializeListProduct,
  serializeSingleProduct,
  serialiseProduct,
  serializeShopifyProduct,
  serialiseProductSitemap,
  serializeMultiLanguageSingleProduct,
} = require("../serializers/ProductSerializer");
const cache = require("../cache");
const settingKeys = require("storeseo-enums/settingKeys");
const { QueryTypes } = require("sequelize");
const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");
const { NAMESPACE, METAFIELD_KEYS, METAFIELD_TYPES } = require("storeseo-enums/metafields");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const { findSitemapFilter } = require("../utils/sitemapUtils");
const { freeProductLimit } = require("../config/app");
const ResourceType = require("storeseo-enums/resourceType");
const ResourceDataBackupService = require("./resource/ResourceDataBackupService");
const ResourceOptimizationService = require("./resource/ResourceOptimizationService");
const ResourceOPType = require("storeseo-enums/resourceOPType");
const AltTextOptimizationStatus = require("storeseo-enums/altTextOptimization");
const AiOptimizationStatus = require("storeseo-enums/aiOptimization");

class ProductService {
  /**
   * Get products with pagination
   * @param shopId
   * @param {{page: number, limit: number, from: string, to: string, optimize_status: string, ai_optimize_status: string, search: string, sortBy: string, sortOrder: string, languageCode: string}} param1
   * @param {Array} fields
   * @returns {Promise<{pagination: {pageCount: number, pageSize: *, page: *, rowCount: *}, products: (*&{twitterPreviewImage: *, facebookPreviewImage: *, featuredImage: null})[]}>}
   */
  getProducts = async (
    shopId,
    {
      page = 1,
      limit = 20,
      from = "",
      to = "",
      optimize_status = "",
      ai_optimize_status = "",
      search = "",
      sortBy = "created_at",
      sortOrder = "DESC",
      languageCode = "default",
    }
  ) => {
    let fromDate = formatFromDateForQuery(from);

    let toDate = formatToDateForQuery(to);

    const optimizeStatusCombinationToScoreCondition = {
      "NEED_IMPROVEMENT,NOT_OPTIMIZED,OPTIMIZED": `COALESCE(mlp.score, p.score) >= 0`,
      "NEED_IMPROVEMENT,OPTIMIZED": `COALESCE(mlp.score, p.score) >= 50`,
      "NOT_OPTIMIZED,OPTIMIZED": `COALESCE(mlp.score, p.score) < 50 OR COALESCE(mlp.score, p.score) >= 75`,
      "NEED_IMPROVEMENT,NOT_OPTIMIZED": `COALESCE(mlp.score, p.score) >= 0 AND COALESCE(mlp.score, p.score) < 75`,
      OPTIMIZED: `COALESCE(mlp.score, p.score) >= 75`,
      NEED_IMPROVEMENT: `COALESCE(mlp.score, p.score) >= 50 AND COALESCE(mlp.score, p.score) < 75`,
      NOT_OPTIMIZED: `COALESCE(mlp.score, p.score) < 50`,
    };

    optimize_status = optimize_status.split(",").sort().join(",");

    let offset = (page - 1) * limit;
    sortBy = sortBy === "optimize_status" ? "score" : sortBy;

    const whereQuery = `
    WHERE p.shop_id = :shopId
    ${search ? `AND COALESCE(mlp.title, p.title) ILIKE :search` : ""}
    ${from || to ? `AND p.created_at BETWEEN :fromDate AND :toDate` : ""}
    ${
      optimizeStatusCombinationToScoreCondition.hasOwnProperty(optimize_status)
        ? `AND ${optimizeStatusCombinationToScoreCondition[optimize_status]}`
        : ""
    }
    ${
      ai_optimize_status
        ? `AND p.ai_optimization_status IN (${ai_optimize_status
            .split(",")
            .map((s) => `'${s}'`)
            .join(",")})`
        : ""
    }
    `;

    const [{ count, products }] = await sequelize.query(
      `
      WITH filtered_products AS (
          SELECT 
              p.id,
              p.shop_id,
              p.product_id,

              COALESCE(mlp.title, p.title) AS title,
              COALESCE(mlp.body_html, p.description) AS description,
              COALESCE(mlp.handle, p.handle) AS handle,
              COALESCE(mlp.focus_keyword, p.focus_keyword) AS focus_keyword,
              COALESCE(mlp.score, p.score) AS score,
              COALESCE(mlp.issues, p.issues) AS issues,

              p.featured_image_id,
              p.status,
              p.tags,
              p.ai_optimization_status,
              p.created_at,
              p.updated_at,
              
              COALESCE(mlp.language_code, :languageCode) AS "languageCode",
              CASE 
                  WHEN pi.media_id IS NOT NULL THEN jsonb_build_object(
                      'media_id', pi.media_id,
                      'src', pi.src,                 
                      'alt_text', pi.alt_text,       
                      'created_at', pi.created_at
                  )
                  ELSE NULL
              END AS "featuredImage"
          FROM 
              products p
          LEFT JOIN 
              multi_lang_products mlp
              ON p.id = mlp.product_id
              AND mlp.language_code = :languageCode
          LEFT JOIN
              product_images pi
              ON p.featured_media_id = pi.media_id` +
        whereQuery +
        `),
      total_count AS (
          SELECT COUNT(*) AS total FROM filtered_products  
      ),
      paginated_products AS (
          SELECT * FROM filtered_products
          ORDER BY ${sortBy} ${sortOrder}
          LIMIT :limit OFFSET :offset
      )
      SELECT 
          (SELECT total FROM total_count) AS count,   
          jsonb_agg(paginated_products.*) AS products  
      FROM 
          paginated_products;
    `,
      {
        replacements: {
          shopId,
          limit,
          offset,
          search: `%${search}%`,
          fromDate,
          toDate,
          languageCode,
          sortBy,
          sortOrder,
        },
        nest: true,
      }
    );

    return {
      products: products ? products.map((product) => serializeListProduct(product)) : [],
      pagination: preparePagination(count, page, limit),
    };
  };

  /**
   *
   * @param {number} shopId
   * @param {{ limit: number, fields?: string[]}} param1
   */
  getTopScoredProducts = async (shopId, { limit = 5, fields = undefined }) => {
    const sortBy = "score";
    const sortOrder = "DESC";
    const order = [[sortBy, sortOrder]];

    const products = await Product.findAll({
      attributes: ["product_id", ...fields],
      where: { shop_id: shopId },
      limit,
      order,
      include: [
        {
          model: ProductImage,
          as: "featuredImage",
          required: false,
          attributes: ["src", "alt_text"],
        },
      ],
    });

    return products.map((product) => serializeListProduct(product.toJSON()));
  };

  /**
   *
   * @param {number} shopId
   * @param {{ limit: number, fields?: string[]}} param1
   */
  getLeastScoredProducts = async (shopId, { limit = 5, fields = undefined }) => {
    const sortBy = "score";
    const sortOrder = "ASC";
    const order = [[sortBy, sortOrder]];

    const products = await Product.findAll({
      attributes: ["product_id", ...fields],
      where: { shop_id: shopId },
      limit,
      order,
      include: [
        {
          model: ProductImage,
          as: "featuredImage",
          required: false,
          attributes: ["src", "alt_text"],
        },
      ],
    });

    return products.map((product) => serializeListProduct(product.toJSON()));
  };

  /**
   * Get all products by a shop id
   * @param shopId
   * @returns {Promise<*[]|(*&{twitterPreviewImage: *, facebookPreviewImage: *, featuredImage: null})[]>}
   */
  getAllProducts = async (shopId) => {
    try {
      const products = await Product.findAll({ where: { shop_id: shopId } });
      return products.map((product) => serializeListProduct(product.toJSON()));
    } catch (e) {
      // logger.error(e);
      return [];
    }
  };

  /**
   *
   * @param {number} shopId
   * @param {import("sequelize").WhereOptions} conditions
   */
  iterateOverProducts = async function* (shopId, conditions = {}) {
    let limit = 30;
    let offset = 0;
    let products = [1];

    while (products.length > 0) {
      products = await Product.findAll({
        where: { shop_id: shopId, ...conditions },
        limit,
        offset,
        include: [
          "meta",
          "images",
          {
            model: ProductImage,
            as: "featuredImage",
            required: false,
          },
        ],
      });

      offset += limit;

      yield products.map((p) => p.toJSON());
    }
  };

  /**
   * Find Products by shop id & conditions
   * @param shopId
   * @param conditions
   * @returns {Promise<*[]|(*&{twitterPreviewImage: *, facebookPreviewImage: *, featuredImage: {altText: *, src: *, id: *}})[]>}
   */
  getProductsByCondition = async (shopId, conditions) => {
    try {
      const products = await Product.findAll({ where: { shop_id: shopId, ...conditions } });
      return products.map((product) => serializeListProduct(product.toJSON()));
    } catch (e) {
      // logger.error(e);
      return [];
    }
  };

  /**
   * Find Products by shop id & conditions
   * @param shopId
   * @param {import("sequelize").WhereOptions} conditions
   * @param {import("sequelize").FindOptions} [options]
   * @returns {Promise<*[]|(*&{twitterPreviewImage: *, facebookPreviewImage: *, featuredImage: {altText: *, src: *, id: *}})[]>}
   */
  getFullProductsByCondition = async (shopId, conditions, options = {}) => {
    try {
      const products = await Product.findAll({
        ...options,
        where: { shop_id: shopId, ...conditions },
        include: [
          "meta",
          "analysis",
          "images",
          {
            model: ProductImage,
            as: "featuredImage",
            required: false,
          },
        ],
      });
      return products.map((product) => serializeSingleProduct(product.toJSON()));
    } catch (e) {
      // logger.error(e);
      return [];
    }
  };

  /**
   * Find a single product with shop id & conditions
   * @param shopId
   * @param conditions
   * @param reject
   * @returns {Promise<(*&{twitterPreviewImage: *, images: *, facebookPreviewImage: *, featuredImage: {altText: *, src: *, id: *}, meta: {id: *}, analysis: *})|{}>}
   */
  getProductByCondition = async (shopId, conditions, reject = true) => {
    try {
      const product = await Product.findOne({
        where: { shop_id: shopId, ...conditions },
        include: [
          "meta",
          "analysis",
          "images",
          {
            model: ProductImage,
            as: "featuredImage",
            required: false,
          },
        ],
        rejectOnEmpty: reject,
      });
      return serializeSingleProduct(product.toJSON());
    } catch (e) {
      // console.error(e);
      return null;
    }
  };

  /**
   * Check if a product is exists or not
   * @param shopId
   * @param conditions
   * @returns {Promise<null|boolean>}
   */
  hasProductByCondition = async (shopId, conditions) => {
    try {
      const product = await Product.count({
        where: { shop_id: shopId, ...conditions },
      });
      return product > 0;
    } catch (e) {
      return null;
    }
  };

  /**
   * Get product by shop id & shopify product id
   * @param shopId
   * @param shopifyProductId
   * @returns {Promise<{}|*|{}>}
   */
  getProductByShopifyId = async (shopId, shopifyProductId, reject = true) => {
    try {
      const conditions = { product_id: `gid://shopify/Product/${shopifyProductId}` };
      console.log("condition: ", conditions);
      return await this.getProductByCondition(shopId, conditions, reject);
    } catch (e) {
      console.log(e);
      return null;
    }
  };

  /**
   * Get product details by shop id & product id
   * @param shopId
   * @param productId
   * @returns {Promise<{}|*|{}>}
   */
  getProductDetails = async (shopId, productId) => {
    const conditions = { id: productId };
    return await this.getProductByCondition(shopId, conditions);
  };

  getProductDetailsByShopifyId = async (shopId, shopifyProductId) => {
    const product = await Product.scope({ method: ["shopify_id", shopifyProductId] }).findOne({
      where: { shop_id: shopId },
      include: [
        "meta",
        // "analysis",
        // "images",
        {
          association: "images",
          // include: ["products"],
        },
        {
          model: ProductImage,
          as: "featuredImage",
          required: false,
        },
        {
          model: ProductAnalysis,
          as: "analysis",
          where: {
            language_code: "default",
          },
          required: false,
        },
      ],
    });
    return serializeSingleProduct(product.toJSON());
  };

  /**
   * Counts
   * @param shopId
   * @param {import("sequelize").WhereOptions} [conditions]
   * @returns {Promise<number>}
   */
  countProducts = async (shopId, conditions = {}) => {
    return Product.count({ where: { shop_id: shopId, ...conditions } });
  };

  /**
   * Save products from shopify
   * @param user
   * @param shopId
   * @param migrateDataFromApp
   * @returns {Promise<boolean>}
   */
  storeProductsFromShopify = async (user, shopId, migrateDataFromApp = null, limit = 5) => {
    try {
      const productCount = await this.countProducts(shopId);
      if (productCount >= freeProductLimit) {
        console.log(`Limit exceeded. Product cannot be saved.`);
        return false;
      }

      const lastCursor = await this.getLastCursor(shopId);
      const products = await ShopifyService.getProductsFromShopify(user.shop, limit, lastCursor);

      if (products?.edges?.length > 0) {
        for (let product of products.edges) {
          console.log(`[${user.shop}] Syncing Product manually: ${product.node.title}, ID: ${product.node.id}`);
          let shopifyProduct = product.node;
          shopifyProduct.mediaImages = flattenMediaImages(shopifyProduct);

          if (migrateDataFromApp) {
            shopifyProduct = await AppDataMigrateService.collectAndMergeOtherAppData(
              migrateDataFromApp,
              shopifyProduct,
              user.shop,
              user
            );
          }

          const savedProduct = await this.saveOrUpdateProduct(shopId, shopifyProduct);
          if (!isEmpty(savedProduct)) {
            await SitemapService.storeSitemapData(savedProduct, analysisEntityTypes.PRODUCT);
            await ProductAnalysisService.analyseEachProduct({ shopId, product: savedProduct });
          }

          dispatchQueue({
            queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
            message: {
              shopId: shopId,
              dbResourceId: savedProduct.id,
              resourceType: ResourceType.PRODUCT,
            },
          });
        }
      }
    } catch (e) {
      logger.error(e);
    }
    return false;
  };

  /**
   * Inserts new, updates old and deletes removed metafields from database
   * @param shopId
   * @param productId
   * @param shopifyProduct
   * @param transaction
   * @returns {Promise<void>}
   */
  upsertProductMetadata = async (shopId, productId, shopifyProduct, transaction = undefined) => {
    const shopifyMetas = serializeShopifyProductMetas(shopifyProduct);
    await ProductMetaService.deleteDeletedProductMeta(shopId, productId, shopifyMetas);
    return await ProductMetaService.saveOrUpdateProductMetas(shopId, productId, shopifyMetas);
  };

  /**
   * Inserts new, updates old and deletes removed images from database
   * @param shopId
   * @param productId
   * @param shopifyProduct
   * @param transaction
   * @returns {Promise<void>}
   */
  upsertProductImages = async (shopId, productId, shopifyProduct, transaction = undefined) => {
    const images = serializeShopifyProductImages(shopifyProduct);
    const savedImages = await ProductImageService.saveOrUpdateProductImages(shopId, productId, images);
    await ProductImageService.deleteDeletedProductImages(shopId, productId, savedImages);

    return savedImages;
  };

  /**
   * Inserts new or updates products from database
   * @param shopId
   * @param shopifyProduct
   * @param transaction
   * @returns {Promise<Model>}
   */
  upsertShopifyProduct = async (shopId, shopifyProduct, transaction = undefined) => {
    const serializedProduct = serializeShopifyProduct(shopId, shopifyProduct);
    let [product] = await Product.upsert(serializedProduct, { transaction });

    return product;
  };

  /**
   * Inserts new product to database
   * @param shopId
   * @param shopifyProduct
   * @returns {Promise<Model>}
   */
  saveShopifyProduct = async (shopId, shopifyProduct) => {
    const transaction = await sequelize.transaction();
    try {
      const serializedProduct = serializeShopifyProduct(shopId, shopifyProduct);
      const product = await Product.create(serializedProduct);
      const productId = product.get("id");
      await this.upsertProductMetadata(shopId, productId, shopifyProduct, transaction);
      await this.upsertProductImages(shopId, productId, shopifyProduct, transaction);
      await transaction.commit();
      return await this.getProductDetails(shopId, productId);
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  };

  /**
   * Updates product to database
   * @param shopId
   * @param productId
   * @param shopifyProduct
   * @returns {Promise<Model>}
   */
  updateShopifyProduct = async (shopId, productId, shopifyProduct) => {
    const transaction = await sequelize.transaction();
    try {
      const serializedProduct = serializeShopifyProduct(shopId, shopifyProduct);
      await this.updateProduct(shopId, productId, serializedProduct, transaction);
      await this.upsertProductMetadata(shopId, productId, shopifyProduct, transaction);
      await this.upsertProductImages(shopId, productId, shopifyProduct, transaction);
      await transaction.commit();
      return await this.getProductDetails(shopId, productId);
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  };

  /**
   * Save or update product and related data
   * @param shopId
   * @param shopifyProduct
   * @returns {Promise<boolean|{}|*>}
   */
  saveOrUpdateProduct = async (shopId, shopifyProduct) => {
    // const transaction = await sequelize.transaction();
    try {
      let product = await this.upsertShopifyProduct(shopId, shopifyProduct);

      const productId = product.get("id");

      await this.upsertProductMetadata(shopId, productId, shopifyProduct);
      await this.upsertProductImages(shopId, productId, shopifyProduct);
      // await transaction.commit();
      return await this.getProductDetails(shopId, productId);
    } catch (e) {
      console.error("saveOrUpdateProduct= ", e);
      // await transaction.rollback();
      return false;
    }
  };

  /**
   * Get product details by shop id & product id
   * @param shopId
   * @param id
   * @returns {Promise<{}|*|{}>}
   */
  getMultiLanguageProductDetails = async (shopId, id) => {
    const conditions = { id: id };
    return await this.getMultiLanguageProductByCondition(shopId, conditions);
  };

  /**
   * Find a single product with shop id & conditions
   * @param shopId
   * @param conditions
   * @param reject
   * @returns {Promise<(*&{twitterPreviewImage: *, images: *, facebookPreviewImage: *, featuredImage: {altText: *, src: *, id: *}, meta: {id: *}, analysis: *})|{}>}
   */
  getMultiLanguageProductByCondition = async (shopId, conditions, reject = true) => {
    try {
      const product = await MultiLanguageProducts.findOne({
        where: { shop_id: shopId, ...conditions },
        include: [
          {
            model: ProductAnalysis,
            as: "analysis",
            where: Sequelize.where(
              Sequelize.col("analysis.language_code"),
              Sequelize.col("MultiLanguageProducts.language_code")
            ),
            required: false,
          },
          {
            model: Product,
            as: "originalVersion",
            include: [
              "meta",
              "images",
              {
                model: ProductImage,
                as: "featuredImage",
                required: false,
              },
            ],
            required: true,
          },
        ],
        rejectOnEmpty: reject,
      });
      return serializeMultiLanguageSingleProduct(product.toJSON());
    } catch (e) {
      console.error(e);
      return null;
    }
  };

  upsertMulitLanguageProduct = async (mulitLangProductData) => {
    let [product] = await MultiLanguageProducts.upsert(mulitLangProductData);

    return this.getMultiLanguageProductDetails(product.get("shop_id"), product.get("id"));
  };

  updateMulitLanguageProduct = async (shopId, id, data, transaction = undefined) => {
    const [updateCount, products] = await MultiLanguageProducts.update(data, {
      where: { shop_id: shopId, id: id },
      // returning: true,
      transaction,
    });
    return await this.getMultiLanguageProductDetails(shopId, id);
  };

  /**
   * Get product focus keyword by shop id & graphql id
   * @param shopId
   * @param productGqlId
   * @returns {Promise<*|null>}
   */
  getProductFocusKeyword = async (shopId, gqlId) => {
    const product = await Product.findOne({
      attributes: ["focus_keyword"],
      where: {
        shop_id: shopId,
        product_id: gqlId,
      },
      rejectOnEmpty: false,
    });

    return product?.get("focus_keyword") || null;
  };

  /**
   * Deletes product and related models from webhook call
   * @param shopId
   * @param shopifyProductId
   * @returns {Promise<Model<any, TModelAttributes>|boolean>}
   */
  deleteWebhookProduct = async (shopId, shopifyProductId) => {
    try {
      const gqlId = `gid://shopify/Product/${shopifyProductId}`;
      const product = await Product.findOne({ where: { shop_id: shopId, product_id: gqlId }, rejectOnEmpty: false });
      if (product) {
        const productId = product.get("id");
        await this.deleteProduct(shopId, productId);
        return product;
      }
    } catch (e) {
      return false;
    }
  };

  /**
   * Deletes product and related models by shop id, product id
   * @param shopId
   * @param productId
   * @returns {Promise<boolean|*>}
   */
  deleteProduct = async (shopId, productId) => {
    // const transaction = await sequelize.transaction();
    try {
      await Product.destroy({ where: { shop_id: shopId, id: productId } });
      await ProductAnalysisService.deleteProductAnalysis(shopId, productId);
      await ProductMetaService.deleteProductMetas(shopId, productId);
      await ProductImageService.deleteProductImages(shopId, productId);
      await SitemapService.deleteSitemaps(shopId, productId, analysisEntityTypes.PRODUCT);
      // await transaction.commit();
    } catch (e) {
      // await transaction.rollback();
      return false;
    }
  };

  /**
   * Get the last cursor of a shop from database
   * @param shopId
   * @returns {Promise<*|null>}
   */
  getLastCursor = async (shopId) => {
    const product = await Product.findOne({
      attributes: ["cursor", "title", "id"],
      where: {
        shop_id: shopId,
      },
      order: [["id", "desc"]],
      rejectOnEmpty: false,
    });
    return product?.get("cursor") || null;
  };

  /**
   * Update product(s) data by condition
   * @param conditions
   * @param data
   * @returns {Promise<{} | Array<{}>>}
   */
  updateProductByCondition = async (conditions, data) => {
    const [affectedRows, products] = await Product.update(data, {
      where: conditions,
      returning: true,
    });

    if (affectedRows === 1) {
      return products[0].toJSON();
    }

    return products.map((shop) => shop.toJSON());
  };

  /**
   * Update product by shop id, product id
   * @param shopId
   * @param productId
   * @param data
   * @param transaction
   * @returns {Promise<{}|*>}
   */
  updateProduct = async (shopId, productId, data, transaction = undefined) => {
    const [updateCount, products] = await Product.update(data, {
      where: { shop_id: shopId, id: productId },
      // returning: true,
      transaction,
    });
    return await this.getProductDetails(shopId, productId);
  };

  /**
   * Updates product's data to shopify
   * @param shopId
   * @param productId
   * @param body
   * @param session
   * @returns {Promise<boolean|{}|*>}
   */
  updateProductDataToShopify = async (shopId, productId, body, session) => {
    const product = await this.getProductDetails(shopId, productId);

    const { metaTitle, metaDescription, focusKeyword, tags, handle = undefined, createRedirectUrl = false } = body;

    if (createRedirectUrl && handle) {
      let urlRedirect = await ShopifyService.createRedirectURL(session.shop, {
        oldPath: `/products/${product.handle}`,
        newPath: `/products/${handle}`,
      });
    }

    const metadata = serializeUpdatedMetadata(product, { metaTitle, metaDescription });

    // console.log("\n***\nmetadata: ", "productId: ", productId, JSON.stringify(metadata, null, 2), "\n***\n");

    const {
      productUpdate: { product: shopifyProduct, userErrors },
    } = await ShopifyService.saveShopifyProductMeta(session.shop, {
      productId: product.product_id,
      tags,
      metadata,
      handle,
    });

    if (userErrors.length > 0) {
      console.log("\n***\n USER ERROR IN PRODUCT UPDATE: ", JSON.stringify(userErrors, null, 2), "\n***\n");
      // logger.error("Product Metafields update to shopify error.", { userErrors });
      return false;
    }

    await this.upsertProductMetadata(shopId, productId, shopifyProduct);

    const updateData = {
      focus_keyword: replaceSpecialChars(focusKeyword),
      tags: tags || null,
      handle: shopifyProduct.handle,
    };

    return await this.updateProduct(shopId, productId, updateData);
  };

  /**
   * Updates product images related data to shopify
   * @param shopId
   * @param productId
   * @param images
   * @param token
   * @returns {Promise<{}|*|boolean>}
   */
  updateProductImageAltData = async (shopId, productId, images, session) => {
    const product = (
      await Product.findOne({
        attributes: ["id", "product_id"],
        where: { shop_id: shopId, id: productId },
        rejectOnEmpty: false,
      })
    ).toJSON();

    const productGqlId = product.product_id;

    const updatedImages = await this.updateProductMediaData(session, { productGqlId, images });

    const imageFileUpdatePayload = updatedImages.map((img) => ({
      id: img.media_id,
      alt: img.alt_text,
      originalSource: img.src,
    }));
    await ShopifyService.updateImageFiles(session.shop, imageFileUpdatePayload);

    return this.getProductDetails(shopId, productId);
  };

  /**
   *
   * @param {Session} session
   * @param {{productGqlId = String, images: Array}} param1
   * @returns {Promise<*>}
   */
  updateProductMediaData = async (session, { productGqlId, images = [] }) => {
    const filteredImages = images.filter((img) => !img.media_id);

    if (filteredImages.length) {
      const {
        files: { edges },
      } = await ShopifyService.getImageMediaIdWithUrl(session.shop, {
        limit: 250,
        productId: productGqlId.split("/").reverse()[0],
      });

      console.log(
        "\n\nproductId:",
        productGqlId.split("/").reverse()[0],
        "images: ",
        JSON.stringify(edges, null, 2),
        "\n\n"
      );

      images = edges.map(({ node }) => {
        let img = images.find((image) => image.src?.split("?")?.[0] === node.image?.url?.split("?")?.[0]);
        return {
          ...img,
          media_id: node.id,
          alt_text: img?.altText || img?.alt_text,
        };
      });
    }

    const res = await Promise.allSettled(
      images.map(({ id, media_id, altText = "", alt_text = "", src }) =>
        ProductImageService.updateImage(id, {
          media_id,
          alt_text: altText || alt_text,
          src,
        })
      )
    );

    return res
      .filter((row) => row.status === "fulfilled")
      .map((row) => ({ ...row.value }))
      .filter((img) => img && Object.keys(img).length);
  };

  /**
   *
   * @param shopId
   * @returns {Promise<Promise<GroupedCountResultItem[]> | Promise<number>>}
   */
  hasDescCount = async (shopId) => {
    return Product.count({
      where: {
        shop_id: shopId,
        description: {
          [Op.not]: "",
        },
      },
    });
  };

  /**
   * Adds focus keyword to product if there is no focus keyword set
   * @param shopId
   * @param product
   * @returns {Promise<boolean>}
   */
  addFocusKeywordToProduct = async (shopId, product) => {
    if (isEmpty(product.focus_keyword)) {
      await this.updateProduct(shopId, product.id, {
        focus_keyword: extractFocusKeyword(product.title),
      });
      return true;
    }
    return false;
  };

  /**
   *
   * @param shopId
   * @param page
   * @param limit
   * @param search
   * @param filterOn
   * @param filterValue
   * @param sortBy
   * @param sortOrder
   * @returns {Promise<{pagination: Promise<{pageCount: number, pageSize, page, rowCount: Promise<number>}>, sitemaps: ((*&{featuredImage: {altText: *, src: *, id: *}|null, noFollow: boolean, isChecked: boolean, noIndex: boolean, status: boolean})[]|*[])}>}
   */
  getSitemaps = async (
    shopId,
    {
      page = 1,
      limit = 20,
      search = "",
      filterOn = "sitemap",
      filterValue = "-1",
      sortBy = "created_at",
      sortOrder = "DESC",
    }
  ) => {
    const offset = limit * (page - 1);

    const where = { shop_id: shopId };

    let order = [[sortBy, sortOrder]];

    if (search) {
      where.title = { [Op.iLike]: `%${search}%` };
    }

    const sitemapWhere = findSitemapFilter(filterOn, filterValue);

    const { count, rows } = await Product.findAndCountAll({
      attributes: ["id", "shop_id", "product_id", "title", "handle"],
      where,
      include: [
        {
          model: Sitemap,
          as: "sitemap",
          where: [sitemapWhere, { resource_type: analysisEntityTypes.PRODUCT }],
          required: true,
          attributes: ["id", "sitemap_disabled", "no_index", "no_follow"],
        },
        {
          model: ProductImage,
          as: "featuredImage",
          required: false,
        },
      ],
      limit,
      offset,
      order,
    });

    return {
      sitemaps: rows.map((p) => serialiseProductSitemap(p)) || [],
      pagination: preparePagination(count, page, limit),
    };
  };

  /**
   * Updates sitemap data to shopify and database
   * @param shopId
   * @param token
   * @param sitemap
   * @returns {Promise<void>}
   */
  updateSitemap = async ({ shopId, session, sitemap }) => {
    const product = await this.getProductDetailsByShopifyId(shopId, sitemap.id);

    const sitemapData = {
      namespace: NAMESPACE.SEO,
      key: METAFIELD_KEYS.HIDDEN,
      value: sitemap.status ? "0" : "1",
      type: METAFIELD_TYPES.NUMBER_INTEGER,
    };

    let meta = product.meta || [];

    if (!isEmpty(meta)) {
      const productSitemap = meta.find((pm) => pm.namespace === NAMESPACE.SEO && pm.key === METAFIELD_KEYS.HIDDEN);
      if (!isEmpty(productSitemap)) {
        sitemapData.id = productSitemap.id;
        meta = meta.map((pm) => {
          if (pm.namespace === NAMESPACE.SEO && pm.key === METAFIELD_KEYS.HIDDEN) {
            return { ...sitemapData };
          }
          return { ...pm, updatedAt: undefined };
        });
      } else {
        meta.push(sitemapData);
      }
    } else {
      meta.push(sitemapData);
    }

    const {
      productUpdate: { product: shopifyProduct, userErrors },
    } = await ShopifyService.updateProductSitemapMeta(session.shop, {
      productId: product.product_id,
      metadata: meta.map((m) => ({ ...m, updatedAt: undefined })),
    });

    if (userErrors.length > 0) {
      throw new Error(userErrors[0]?.message);
    }

    await SitemapService.updateSitemapData({
      shopId,
      resourceId: product.id,
      resourceType: analysisEntityTypes.PRODUCT,
      metaKey: "sitemap_disabled",
      metaStatus: sitemapData.value,
    });

    await this.upsertProductMetadata(shopId, product.id, shopifyProduct);
  };

  /**
   * Delete product related data of a shop, keeps the data specified in the keep parameter
   * @param {Number|String} shopId
   * @param {Number|String} keep
   * @returns {Promise<boolean>}
   */
  deleteProductRelatedData = async (shopId, keep = 0) => {
    // const transaction = await sequelize.transaction();
    try {
      const product = await Product.findOne({
        attributes: ["id"],
        where: { shop_id: shopId },
        rejectOnEmpty: false,
        offset: keep,
        order: [["id", "asc"]],
      });

      if (product) {
        await Product.destroy({ where: { shop_id: shopId, id: { [Op.gte]: product.id } } });
        await SitemapService.deleteSitemapsGreaterThenResourceId(shopId, product.id, analysisEntityTypes.PRODUCT);
        await ProductAnalysisService.deleteProductAnalysisGreaterThenProductId(shopId, product.id);
        await ProductMetaService.deleteShopMetasGreaterThenProductId(shopId, product.id);
        await ProductImageService.deleteShopImagesGreaterThenProductId(shopId);
      }
      // await transaction.commit();
      return true;
    } catch (e) {
      console.log(e);
      // await transaction.rollback();
      return false;
    }
  };

  /**
   * Index all products
   * @param user
   * @param isEnabled
   * @returns {Promise<void>}
   */
  indexAllProducts = async (user, isEnabled) => {
    const ShopService = require("./ShopService");
    const googleAuthScopes = require("storeseo-enums/googleAuthScopes");
    try {
      // const { configs } = await integrationService.getConfig(user.shopId, integrationType.GOOGLE_SERVICE_JSON);

      const { value: google_integration_info } = await ShopService.getShopSetting(
        user.shopId,
        settingKeys.GOOGLE_INTEGRATION_INFO
      );
      const userHasAuthenticatedForIndexing =
        google_integration_info?.grantedScopes?.includes(googleAuthScopes.INSTANT_INDEXING_READ_WRITE) &&
        google_integration_info?.steps?.indexingPermission;

      if (userHasAuthenticatedForIndexing) {
        const indexStatus = isEnabled ? urlNotificationType.URL_DELETED : urlNotificationType.URL_UPDATED;
        const products = await this.getProductsForIndexing(user.shopId, indexStatus);

        if (products.length > 0) {
          products.map(async (product) => {
            dispatchQueue({
              queueName: QUEUE_NAMES.PRODUCT_INDEXING_GOOGLE,
              message: { user, product, isEnabled },
            });
          });
        }

        await this.calculateAndDispatchCron(user, isEnabled, indexStatus);
      }
    } catch (err) {
      // logger.error(JSON.stringify(err));
    }
  };

  /**
   * Count product by index status
   * @param shopId
   * @param indexStatus
   * @returns {Promise<Promise<GroupedCountResultItem[]> | Promise<number>>}
   */
  countNotIndexedProducts = async (shopId, indexStatus) => {
    indexStatus = indexStatus ? "1" : "0";
    return Product.count({
      where: {
        shop_id: shopId,
        [Op.or]: [{ indexing_status: null }, { indexing_status: indexStatus }],
      },
    });
  };

  /**
   * Calculates no-index items and dispatches a cron to index them
   * @param user
   * @param isEnabled
   * @param indexStatus
   * @returns {Promise<void>}
   */
  calculateAndDispatchCron = async (user, isEnabled, indexStatus) => {
    const notIndexedCount = await this.countNotIndexedProducts(user.shopId, indexStatus);
    if (notIndexedCount > 0) {
      cron.schedule("59 23 * * *", () => {
        console.info(`Cron Dispatched: [${user.shop}], Not Indexed: ${notIndexedCount}.`);
        this.indexAllProducts(user, isEnabled);
      });
    }
  };

  /**
   * Get products for indexing
   * @param shopId
   * @param indexStatus
   * @returns {Promise<U[]>}
   */
  getProductsForIndexing = async (shopId, indexStatus) => {
    try {
      // indexStatus = indexStatus ? "1" : "0";
      const products = await Product.findAll({
        attributes: ["id", "shop_id", ["online_store_url", "url"]],
        where: {
          shop_id: shopId,
          online_store_url: { [Op.not]: null },
          [Op.or]: [{ indexing_status: null }, { indexing_status: indexStatus }],
        },
        limit: 200,
      });
      return products.map((p) => p.toJSON());
    } catch (err) {
      console.log(err);
    }
  };

  /**
   * Serialize product to analyse when updating
   * @param shopId
   * @param productId
   * @param data
   * @returns {Promise<*&{twitterPreviewImage: *, facebookPreviewImage: *, featuredImage: {altText: *, src: *, id: *}}>}
   */
  serializeProductForAnalysis = async (product, data) => {
    const { metaTitle, metaDescription, focusKeyword, images, handle } = data;
    const metadata = serializeUpdatedMetadata(product, { metaTitle, metaDescription });

    const updateData = {
      meta: metadata.length > 0 ? metadata : product.meta,
      focus_keyword: focusKeyword ? replaceSpecialChars(focusKeyword) : product.focus_keyword,
      images: images || product.images,
      handle: handle || product.handle,
    };

    return serialiseProduct({ ...product, ...updateData });
  };

  /**
   * Get products gql_id from database
   * @param shopId
   * @returns {Promise<unknown[]>}
   */
  getDbProductIds = async (shopId) => {
    const products = await Product.findAll({
      attributes: ["product_id"],
      where: { shop_id: shopId },
    });
    return products.map((p) => p.get("product_id"));
  };

  /**
   * Get products id, focus_keyword of a shop
   * @param shopId
   * @param conditions
   * @returns {Promise<*>}
   */
  getProductsFocusKeywordWithId = async (shopId, conditions = {}) => {
    const products = await Product.findAll({
      attributes: ["id", "focus_keyword"],
      where: { shop_id: shopId, ...conditions },
      rejectOnEmpty: false,
    });
    return products?.map((p) => p.toJSON());
  };

  /**
   *
   * @param {number} shopId
   * @param {number} productId
   * @returns {Promise<{ prevId: string | null, nextId: string | null}>}
   */
  idsOfThePrevAndNextProduct = async (shopId, productId) => {
    const [[result]] = await sequelize.query(
      `
          SELECT MAX(CASE WHEN id < :productId THEN product_id END) as nextid,
                 MIN(CASE WHEN id > :productId THEN product_id END) as previd
          FROM products
          WHERE shop_id = :shopId
            and id != :productId
          GROUP BY shop_id
      `,
      {
        replacements: { shopId, productId },
      }
    );

    return {
      previd: extractShopifyIdFromGqlId(result.previd),
      nextid: extractShopifyIdFromGqlId(result.nextid),
    };
  };

  getPaginationOfProduct = async (shopId, productId) => {
    const prefix = `gid://shopify/Product/`;
    const paginationQuery = `
    WITH virtual_table AS (
      SELECT
          REPLACE(product_id, '${prefix}', '') as product_id,
          REPLACE(lag(product_id) OVER w, '${prefix}', '') AS prev,
          REPLACE(lead(product_id) OVER w, '${prefix}', '') AS next
      FROM products
      WHERE shop_id = :shopId
      WINDOW w AS (ORDER BY created_at DESC)
    )

    SELECT prev, next FROM virtual_table WHERE product_id = :productId;
  `;

    const [result] = await sequelize.query(paginationQuery, {
      replacements: { shopId, productId },
      type: QueryTypes.SELECT,
    });

    return result;
  };

  /**
   * Set/read the boolean status flag indicating whether product sync process is currently running or not.
   * @param {string} shop myshopify domain of the shop
   * @param {boolean} [status] leave it empty to read the current flag, provide a value to update the flag.
   * @return {Promise<boolean>}
   */
  syncOngoing = async (shop, status) => cache.productSyncOngoing(shop, status);

  saveSyncCursor = async (shopDomain, cursor) => cache.saveProductSyncCursor(shopDomain, cursor);

  getSyncCursor = async (shopDomain) => cache.getProductSyncCursor(shopDomain);

  deleteSyncCursor = async (shopDomain) => cache.removeProductSyncCursor(shopDomain);

  getProductsByShopId = async (shopId, fields = [], limit = null) => {
    const products = await Product.findAll({ where: { shop_id: shopId }, attributes: ["id", ...fields], limit });
    return products.map((p) => p.toJSON());
  };

  getSitemapEnabledProductsByShopId = async (shopId, fields = [], limit = null) => {
    const products = await Product.findAll({
      where: { shop_id: shopId },
      attributes: ["id", ...fields],
      limit,
      include: [
        {
          model: Sitemap,
          as: "sitemap",
          attributes: ["resource_id", "resource_type", "sitemap_disabled"],
          where: {
            resource_type: analysisEntityTypes.PRODUCT,
            sitemap_disabled: 0,
          },
        },
      ],
    });
    return products.map((p) => p.toJSON());
  };

  /**
   * Delete inactive/deleted shopify products by shopifyProductId and productId.
   * @param {string} shopifyProductId
   * @param {number} productId
   * @returns {Promise<boolean>}
   */
  deleteInactiveProducts = async (shopifyProductId, productId) => {
    try {
      await Product.destroy({ where: { product_id: shopifyProductId } });
      await ProductAnalysis.destroy({ where: { product_id: productId } });
      await ProductMeta.destroy({ where: { product_id: productId } });
      await Sitemap.destroy({
        where: {
          resource_id: productId,
          resource_type: analysisEntityTypes.PRODUCT,
        },
      });
    } catch (err) {
      console.log(err);
      return false;
    }
  };

  /**
   * Restores product content from backup data
   * @param {number} shopId
   * @param {any} productDetails
   * @param {Object} restoreData
   * @param {Object} session
   * @returns {Promise<any>}
   */
  restoreProductContent = async (shopId, productDetails, restoreData, session) => {
    await this.updateProduct(shopId, productDetails.id, {
      ai_optimization_status: AiOptimizationStatus.NOT_OPTIMIZED,
    });

    // Update the product data to Shopify
    await this.updateProductDataToShopify(
      shopId,
      productDetails.id,
      {
        ...restoreData,
        focusKeyword: productDetails?.focus_keyword,
      },
      session
    );

    const productImagesToRestore = productDetails.images.filter(
      (img) => img.alt_text_optimization_status === AltTextOptimizationStatus.OPTIMIZED
    );

    for (let img of productImagesToRestore) {
      const imageBackupData = await ResourceDataBackupService.getByCondition({
        shop_id: shopId,
        resource_id: img.id,
        resource_type: ResourceType.PRODUCT_IMAGE,
        resource_op_type: ResourceOPType.AI_OPTIMIZATION,
      });

      const dbImage = await ProductImageService.getImageByConditions({
        shop_id: shopId,
        media_id: img.media_id,
      });
      const updateImage = {
        ...dbImage,
        alt_text: imageBackupData?.data[img.media_id] ?? "",
      };
      await this.updateProductImageAltData(shopId, productDetails.id, [updateImage], session);

      if (dbImage) {
        const imageResourceOptimization = await ResourceOptimizationService.getByCondition({
          shop_id: shopId,
          resource_id: dbImage.id,
          resource_type: ResourceType.PRODUCT_IMAGE,
          resource_op_type: ResourceOPType.AI_OPTIMIZATION,
        });

        const currentRestoreCount = imageResourceOptimization?.optimization_stats?.restore_count ?? 0;

        const alt_text_optimization_stats = {
          ...(imageResourceOptimization
            ? imageResourceOptimization.optimization_stats
            : ImageOptimization.optimizationStats),
          restore_count: currentRestoreCount + 1,
        };

        await ProductImageService.updateImage(dbImage.id, {
          alt_text_optimization_status: AltTextOptimizationStatus.RESTORED,
        });

        await ResourceOptimizationService.upsert({
          shop_id: shopId,
          resource_id: dbImage.id,
          resource_type: ResourceType.PRODUCT_IMAGE,
          resource_op_type: ResourceOPType.AI_OPTIMIZATION,
          optimization_stats: alt_text_optimization_stats,
        });
      }
    }

    const afterRestoreProduct = await this.getProductByCondition(shopId, {
      id: productDetails.id,
    });

    return await ProductAnalysisService.analyseEachProduct({
      product: afterRestoreProduct,
      shopId,
      oldProduct: productDetails,
    });
  };

  /**
   * Delete products that are not synced (is_synced = false) and their associated data
   * Similar to ArticleService.deleteNotSyncedArticles
   * @param {number} shopId
   * @returns {Promise<void>}
   */
  deleteNotSyncedProducts = async (shopId) => {
    try {
      // Count total not synced products before deletion
      const totalNotSynced = await Product.count({
        where: { shop_id: shopId, is_synced: false },
      });

      if (totalNotSynced === 0) {
        console.log(`[ProductService] No unsynced products to delete for shop ${shopId}`);
        return;
      }

      // Get all not synced products to delete their associated data
      const productsToDelete = await Product.findAll({
        where: { shop_id: shopId, is_synced: false },
        attributes: ["id"],
      });

      const productIds = productsToDelete.map((p) => p.id);

      if (productIds.length > 0) {
        console.log(
          `[ProductService] Deleting ${productIds.length} unsynced products and associated data for shop ${shopId}`
        );

        // Delete associated data first (to avoid foreign key constraints)
        // Use bulk operations with Op.in for better performance
        await ProductAnalysis.destroy({
          where: { shop_id: shopId, product_id: { [Op.in]: productIds } },
        });

        await ProductMeta.destroy({
          where: { shop_id: shopId, product_id: { [Op.in]: productIds } },
        });

        await ProductImageMapper.destroy({
          where: { shop_id: shopId, product_id: { [Op.in]: productIds } },
        });

        await Sitemap.destroy({
          where: {
            shop_id: shopId,
            resource_id: { [Op.in]: productIds },
            resource_type: analysisEntityTypes.PRODUCT,
          },
        });

        // Delete multi-language products
        await MultiLanguageProducts.destroy({
          where: { shop_id: shopId, product_id: { [Op.in]: productIds } },
        });

        // Finally delete the products themselves
        await Product.destroy({
          where: { shop_id: shopId, is_synced: false },
        });

        console.log(`[ProductService] Successfully deleted ${totalNotSynced} unsynced products for shop ${shopId}`);
      }
    } catch (error) {
      console.error(`[ProductService] Error deleting unsynced products for shop ${shopId}:`, error);
      throw error;
    }
  };

  /**
   * Mark all products as not synced for a shop (used at start of sync)
   * @param {number} shopId
   * @returns {Promise<void>}
   */
  markAllProductsAsNotSynced = async (shopId) => {
    try {
      const updatedCount = await Product.update({ is_synced: false }, { where: { shop_id: shopId } });
      console.log(`[ProductService] Marked ${updatedCount[0]} products as not synced for shop ${shopId}`);
    } catch (error) {
      console.error(`[ProductService] Error marking products as not synced for shop ${shopId}:`, error);
      throw error;
    }
  };
}

module.exports = new ProductService();
