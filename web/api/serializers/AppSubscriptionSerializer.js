const {
  calculateShopifyPlanInterval,
  calculateDiscountDataForShopify,
  calculateRecurringPrice,
  calculateRecurringPriceDiscount,
  calculateCappedAmount,
  calculateUsagePrice,
  calculateCreditAddonPrice,
} = require("../utils/subscriptionCalculations");
const { APPLY_IMMEDIATELY } = require("storeseo-enums/appSubscriptionReplacementBehavior");
const addonInterval = require("storeseo-enums/subscriptionAddonInterval");
const planInterval = require("storeseo-enums/planInterval");
const { addonLimitLabels, addonLabels } = require("../config/addons");
const { isSubscriptionTestMode, currency } = require("../config/app");

const prepareAppPricingDetails = (plan, coupon) => {
  if (plan.interval === planInterval.USAGE) {
    const amount = parseFloat(plan.subtotal).toFixed(2);
    const cappedAmount = parseFloat(plan.meta.cappedAmount).toFixed(2);
    const duration = plan.meta?.duration;

    return {
      appUsagePricingDetails: {
        cappedAmount: {
          amount: cappedAmount,
          currencyCode: currency,
        },
        terms: `By choosing this plan you will be charged \$${amount} immediately for next ${duration} months.`,
      },
    };
  }

  const amount = parseFloat(plan.price).toFixed(2);
  return {
    appRecurringPricingDetails: {
      price: {
        amount,
        currencyCode: currency,
      },
      interval: calculateShopifyPlanInterval(plan.interval),
      discount: calculateDiscountDataForShopify(plan, coupon),
    },
  };
};

/**
 * @param {Object} plan
 * @param {String} url
 * @param {Object} coupon
 * @return Object
 */
const serializeVariables = (plan, url, coupon) => {
  return {
    name: plan.slug,
    returnUrl: `${url}/subscription/success`,
    lineItems: [
      {
        plan: prepareAppPricingDetails(plan, coupon),
      },
    ],
    replacementBehavior: APPLY_IMMEDIATELY,
    test: isSubscriptionTestMode || plan?.meta?.isTest || false,
    trialDays: plan?.meta?.trialDays || undefined,
  };
};

const prepareLineItems = (plan, coupon, addons = []) => {
  const lineItems = [];
  const monthlyAddons = addons.filter((addon) => addon.interval === addonInterval.MONTHLY);
  const usageAddons = addons.filter((addon) => addon.interval === addonInterval.USAGE);
  const monthlyRecurringPrice = calculateRecurringPrice(plan, monthlyAddons);
  const usagePrice = calculateUsagePrice(plan, usageAddons);

  if (coupon) plan.coupon = coupon;

  if (monthlyRecurringPrice > 0) {
    lineItems.push({
      plan: {
        appRecurringPricingDetails: {
          price: {
            amount: calculateRecurringPrice(plan, monthlyAddons).toFixed(2),
            currencyCode: currency,
          },
          interval: calculateShopifyPlanInterval(plan.interval),
          discount: usagePrice === 0 ? calculateRecurringPriceDiscount(plan, monthlyAddons) : undefined,
        },
      },
    });
  }

  if (usagePrice) {
    lineItems.push({
      plan: {
        appUsagePricingDetails: {
          cappedAmount: {
            amount: calculateCappedAmount(plan, usageAddons),
            currencyCode: currency,
          },
          terms: generateTerms(usagePrice, plan, usageAddons),
        },
      },
    });
  }

  return lineItems;
};

const generateTerms = (usagePrice, plan, addons = []) => {
  let terms = [`You will be charged \$${usagePrice}`];
  if (plan.interval === planInterval.USAGE) {
    terms.push(`for "${plan.name}" plan for ${plan.meta?.duration} months`);
  }

  // addons.forEach((addon) => {
  //   terms.push(``);
  // });

  return terms.join(", ");
};

/**
 * @param {Object} plan
 * @param {String} url
 * @param {Object} coupon
 * @param {Array} addons
 * @return Object
 */
const serializeVariablesWithAddons = (plan, url, coupon, addons = [], withTrial = true) => {
  return {
    name: planName(plan, addons),
    returnUrl: buildUrl(url, plan, addons),
    lineItems: prepareLineItems(plan, coupon, addons),
    replacementBehavior: APPLY_IMMEDIATELY,
    test: isSubscriptionTestMode || plan?.meta?.isTest || false,
    trialDays: withTrial ? plan?.meta?.trialDays : undefined,
  };
};

/**
 * @param {Object} plan
 * @param {String} url
 * @param {Object} coupon
 * @param {Array} addons
 * @return Object
 */
const serializeOnetimePurchaseVariables = (url, addons = [], coupon) => {
  return {
    name: getAddonName(addons),
    price: {
      amount: calculateCreditAddonPrice(addons),
      currencyCode: currency,
    },
    returnUrl: buildPurchaseUrl(url, addons),
    test: isSubscriptionTestMode || false,
  };
};

const planName = (plan, addons) => {
  let name = [`${plan.name}${plan.interval === planInterval.ANNUALLY ? "(Y)" : ""}`];
  let addonName = getAddonName(addons);
  if (addonName) {
    name.push(addonName);
  }
  return name.join(", ");
};

const getAddonName = (addons = []) => {
  if (addons.length > 0) {
    let names = addons
      .map((a) =>
        serializeAddon(a).features.reduce((addonName, feature) => {
          return `${feature.value} ${feature.label}`;
        }, "")
      )
      .reverse()
      .join(", ");

    return names;
  }
  return "";
};

const buildUrl = (url, plan, addons = []) => {
  const newUrl = new URL(`${url}/verify-subscription`);
  newUrl.searchParams.append("plan", plan.slug);
  if (addons.length > 0) {
    newUrl.searchParams.append(
      "addons",
      addons.map((a) => a.id)
    );
  }
  return newUrl;
};

const buildPurchaseUrl = (url, addons = []) => {
  const newUrl = new URL(`${url}/verify-purchase`);
  if (addons.length > 0) {
    newUrl.searchParams.append(
      "addons",
      addons.map((a) => a.id)
    );
  }
  return newUrl;
};

const serializeAddon = (addon) => {
  return {
    ...addon,
    features: serializeAddonFeatures(addon.group, addon.limit),
  };
};

const serializeAddonFeatures = (group, limit) => {
  return [
    {
      key: group,
      value: limit,
      label: addonLimitLabels[group],
    },
  ];
};

// const serializeAddons = (addons) => {
//   const formattedAddons = [
//     {
//       key: AI_OPTIMIZER,
//       title: addonLabels[AI_OPTIMIZER],
//       items: addons.filter((a) => a.group === AI_OPTIMIZER).map((a) => serializeAddon(a)),
//     },
//   ];

//   return formattedAddons;
// };

const serializeAddons = (addons) => {
  const uniqueGroups = [...new Set(addons.map((addon) => addon.group))];

  const formattedAddons = uniqueGroups.map((group) => ({
    group: group,
    title: addonLabels[group],
    items: addons.filter((addon) => addon.group === group).map((addon) => serializeAddon(addon.toJSON())),
  }));

  return formattedAddons;
};

module.exports = {
  serializeVariables,
  serializeVariablesWithAddons,
  serializeOnetimePurchaseVariables,
  planName,
  serializeAddon,
  serializeAddons,
};
