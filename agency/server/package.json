{"name": "agency-server", "version": "1.0.0", "description": "Agency Express Server", "main": "index.js", "type": "commonjs", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"express": "^4.18.2", "helmet": "^7.1.0", "cors": "^2.8.5", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "http-status": "^1.7.4", "xss-filters": "^1.2.7", "morgan": "^1.10.0", "express-fileupload": "^1.4.3", "yup": "^1.4.0", "dotenv": "^16.4.5"}, "devDependencies": {"nodemon": "^3.0.2"}}