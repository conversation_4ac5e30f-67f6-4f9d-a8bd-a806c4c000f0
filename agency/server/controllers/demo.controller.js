const httpStatus = require("http-status");
const catchAsync = require("../utils/catchAsync");

const getDemoData = catchAsync(async (req, res) => {
  const demoData = {
    message: "Hello from Agency Server API!",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
    environment: process.env.NODE_ENV || "development",
    data: {
      users: [
        { id: 1, name: "<PERSON>", email: "<EMAIL>" },
        { id: 2, name: "<PERSON>", email: "<EMAIL>" },
      ],
      stats: {
        totalUsers: 2,
        activeUsers: 1,
        lastUpdated: new Date().toISOString(),
      },
    },
  };

  res.status(httpStatus.OK).json(demoData);
});

const getHealthCheck = catchAsync(async (req, res) => {
  const healthData = {
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: "1.0.0",
  };

  res.status(httpStatus.OK).json(healthData);
});

module.exports = {
  getDemoData,
  getHealthCheck,
};
