"use strict";
const express_1 = require("express");
const demoRoute = require("./demo.route");
const router = (0, express_1.Router)();
const defaultRoutes = [
    {
        path: "/demo",
        route: demoRoute,
    },
];
// TODO: API documentation with swagger
const devRoutes = [
// routes available only in development mode
];
defaultRoutes.forEach((route) => {
    router.use(route.path, route.route);
});
/* istanbul ignore next */
if (process.env.NODE_ENV === "development") {
    devRoutes.forEach((route) => {
        router.use(route.path, route.route);
    });
}
module.exports = router;
//# sourceMappingURL=index.js.map