"use strict";
try {
    import helmet = require("helmet");
    import cors = require("cors");
    import passport = require("passport");
    import httpStatus = require("http-status");
    import express = require("express");
    import morgan = require("morgan");
    import fileUpload = require("express-fileupload");
    import { middleware as xss } from "./middlewares/xss";
    import jwtStrategy = require("./config/passport");
    import ApiError = require("./utils/apiError");
    import { errorConverter, errorHandler } from "./middlewares/error";
    import routes = require("./routes/v1");
    import config = require("./config/config");
    const app = express();
    // set security HTTP headers
    app.use(helmet());
    // parse json request body
    app.use(express.json());
    // parse urlencoded request body
    app.use(express.urlencoded({ extended: true }));
    // sanitize request data
    app.use((0, xss_1.middleware)());
    // enable cors
    app.use(cors());
    app.options("*", cors());
    // jwt authentication
    app.use(passport.initialize());
    app.use(fileUpload({}));
    passport.use("jwt", jwtStrategy);
    // log requests to console
    app.use(morgan(":method :status :url :res[content-length] - :response-time ms"));
    app.get("/", (req, res) => {
        console.info("Agency server received a request");
        res.json({ message: "Agency Server API - Hello World!" });
    });
    // v1 api routes
    app.use("/v1", routes);
    // send back a 404 error for any unknown api request
    app.use((req, res, next) => {
        next(new ApiError(httpStatus.NOT_FOUND, "Not found"));
    });
    // convert error to ApiError, if needed
    app.use(error_1.errorConverter);
    // handle error
    app.use(error_1.errorHandler);
    app
        .listen(config.port, () => {
        console.info(`Agency server running here ${config.HOST}`);
        console.info(`Press Ctrl+C to quit.`);
    })
        .on("error", (err) => {
        console.error(err);
    });
}
catch (error) {
    console.error("error =", error);
}
//# sourceMappingURL=index.js.map