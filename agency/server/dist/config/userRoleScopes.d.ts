import { AgencyApiActionList } from "../../../web/packages/storeseo-enums/src/agency/agencyApiActionList";
export declare const rolsScopeMap: {
    OWNER: {
        code: string;
        description: string;
        actions: AgencyApiActionList[];
    };
    ADMIN: {
        code: string;
        description: string;
        actions: (AgencyApiActionList.BILLING_READ | AgencyApiActionList.BILLING_WRITE | AgencyApiActionList.STORE_ACCESS_READ | AgencyApiActionList.STORE_ACCESS_WRITE | AgencyApiActionList.USER_MANAGEMENT_READ | AgencyApiActionList.USER_MANAGEMENT_WRITE | AgencyApiActionList.STORE_MANAGEMENT_READ | AgencyApiActionList.STORE_MANAGEMENT_WRITE | AgencyApiActionList.ADVANCED_SEO_FEATURES_READ | AgencyApiActionList.ADVANCED_SEO_FEATURES_WRITE | AgencyApiActionList.TEAM_OVERSIGHT_READ | AgencyApiActionList.TEAM_OVERSIGHT_WRITE | AgencyApiActionList.SEO_OPTIMIZATION_READ | AgencyApiActionList.SEO_OPTIMIZATION_WRITE | AgencyApiActionList.CONTENT_MANAGEMENT_READ | AgencyApiActionList.CONTENT_MANAGEMENT_WRITE | AgencyApiActionList.ANALYTICS_VIEWING_READ | AgencyApiActionList.ANALYTICS_VIEWING_WRITE)[];
    };
    MANAGER: {
        code: string;
        description: string;
        actions: AgencyApiActionList[];
    };
    EDITOR: {
        code: string;
        description: string;
        actions: AgencyApiActionList[];
    };
    VIEWER: {
        code: string;
        description: string;
        actions: AgencyApiActionList[];
    };
};
//# sourceMappingURL=userRoleScopes.d.ts.map