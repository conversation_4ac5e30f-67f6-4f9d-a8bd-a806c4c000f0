"use strict";
const passport_jwt_1 = require("passport-jwt");
const config = require("./config");
const userTokenType = require("./userTokenType");
const jwtOptions = {
    secretOrKey: config.jwt.secret,
    jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
};
const jwtVerify = async (payload, done) => {
    try {
        if (payload.type !== userTokenType.ACCESS) {
            throw new Error("Invalid token type");
        }
        // For now, we'll create a simple user verification
        // This can be extended later with actual user service
        const user = {
            id: payload.sub,
            email: payload.email,
            name: payload.name,
            scopes: payload.scopes || [],
            type: payload.type
        };
        if (!user.id) {
            return done(null, false);
        }
        done(null, user);
    }
    catch (error) {
        done(error, false);
    }
};
module.exports = new passport_jwt_1.Strategy(jwtOptions, jwtVerify);
//# sourceMappingURL=passport.js.map