"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.rolsScopeMap = void 0;
const agencyApiActionList_1 = require("../../../web/packages/storeseo-enums/src/agency/agencyApiActionList");
const allActions = Object.values(agencyApiActionList_1.AgencyApiActionList);
const adminActions = allActions.filter(action => action !== agencyApiActionList_1.AgencyApiActionList.WORKSPACE_DELETE);
const baseRoleScopes = {
    VIEWER: [agencyApiActionList_1.AgencyApiActionList.ANALYTICS_VIEWING_READ],
    EDITOR: [
        agencyApiActionList_1.AgencyApiActionList.SEO_OPTIMIZATION_READ,
        agencyApiActionList_1.AgencyApiActionList.SEO_OPTIMIZATION_WRITE,
        agencyApiActionList_1.AgencyApiActionList.CONTENT_MANAGEMENT_READ,
        agencyApiActionList_1.AgencyApiActionList.CONTENT_MANAGEMENT_WRITE,
        agencyApiActionList_1.AgencyApiActionList.ANALYTICS_VIEWING_READ,
    ],
    MANAGER: [
        agencyApiActionList_1.AgencyApiActionList.STORE_MANAGEMENT_READ,
        agencyApiActionList_1.AgencyApiActionList.STORE_MANAGEMENT_WRITE,
        agencyApiActionList_1.AgencyApiActionList.TEAM_OVERSIGHT_READ,
        agencyApiActionList_1.AgencyApiActionList.TEAM_OVERSIGHT_WRITE,
        agencyApiActionList_1.AgencyApiActionList.ADVANCED_SEO_FEATURES_READ,
        agencyApiActionList_1.AgencyApiActionList.ADVANCED_SEO_FEATURES_WRITE,
    ],
    ADMIN: [], // will be filled later
    OWNER: [], // will be filled later
};
const roleHierarchy = {
    VIEWER: [],
    EDITOR: ["VIEWER"],
    MANAGER: ["EDITOR", "VIEWER"],
    ADMIN: ["MANAGER", "EDITOR", "VIEWER"],
    OWNER: ["ADMIN", "MANAGER", "EDITOR", "VIEWER"],
};
function resolveActions(role, visited = new Set()) {
    if (visited.has(role))
        return [];
    visited.add(role);
    const actions = new Set(baseRoleScopes[role] || []);
    for (const inheritedRole of roleHierarchy[role] || []) {
        for (const action of resolveActions(inheritedRole, visited)) {
            actions.add(action);
        }
    }
    return Array.from(actions);
}
exports.rolsScopeMap = {
    OWNER: {
        code: "WORKSPACE_OWNER",
        description: "Full workspace control, billing, user management, all store access",
        actions: allActions,
    },
    ADMIN: {
        code: "WORKSPACE_ADMIN",
        description: "User management, store management, all SEO features",
        actions: adminActions, // ADMIN has everything
    },
    MANAGER: {
        code: "WORKSPACE_MANAGER",
        description: "Store management, team oversight, advanced SEO features",
        actions: resolveActions("MANAGER"),
    },
    EDITOR: {
        code: "WORKSPACE_EDITOR",
        description: "SEO optimization, content management, analytics viewing",
        actions: resolveActions("EDITOR"),
    },
    VIEWER: {
        code: "WORKSPACE_VIEWER",
        description: "Read-only access to analytics and reports",
        actions: resolveActions("VIEWER"),
    },
};
//# sourceMappingURL=userRoleScopes.js.map