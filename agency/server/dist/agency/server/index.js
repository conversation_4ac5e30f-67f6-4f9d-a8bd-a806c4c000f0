"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const helmet = require("helmet");
const cors = require("cors");
const passport = require("passport");
const httpStatus = require("http-status");
const express = require("express");
const morgan = require("morgan");
const fileUpload = require("express-fileupload");
const { errorConverter, errorHandler } = require("./middlewares/error");
const { middleware: xss } = require("./middlewares/xss");
const jwtStrategy = require("./config/passport");
const ApiError = require("./utils/apiError");
const routes = require("./routes/v1");
const config = require("./config/config");
try {
    const app = express();
    app.use(helmet());
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    app.use(xss());
    app.use(cors());
    app.options("*", cors());
    app.use(passport.initialize());
    app.use(fileUpload({}));
    passport.use("jwt", jwtStrategy);
    app.use(morgan(":method :status :url :res[content-length] - :response-time ms"));
    app.get("/", (req, res) => {
        console.info("Agency server received a request");
        res.json({ message: "Agency Server API - Hello World!" });
    });
    app.use("/v1", routes);
    app.use((req, res, next) => {
        next(new ApiError(httpStatus.NOT_FOUND, "Not found"));
    });
    app.use(errorConverter);
    app.use(errorHandler);
    app
        .listen(config.port, () => {
        console.info(`Agency server running here ${config.HOST}`);
        console.info(`Press Ctrl+C to quit.`);
    })
        .on("error", (err) => {
        console.error(err);
    });
}
catch (error) {
    console.error("error =", error);
}
