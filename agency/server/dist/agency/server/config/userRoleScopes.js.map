{"version": 3, "file": "userRoleScopes.js", "sourceRoot": "", "sources": ["../../../../config/userRoleScopes.ts"], "names": [], "mappings": ";;;AAAA,6GAA0G;AAE1G,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,yCAAmB,CAAC,CAAC;AAEtD,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,KAAK,yCAAmB,CAAC,gBAAgB,CAAC,CAAA;AAGjG,MAAM,cAAc,GAAG;IACrB,MAAM,EAAE,CAAC,yCAAmB,CAAC,sBAAsB,CAAC;IACpD,MAAM,EAAE;QACN,yCAAmB,CAAC,qBAAqB;QACzC,yCAAmB,CAAC,sBAAsB;QAC1C,yCAAmB,CAAC,uBAAuB;QAC3C,yCAAmB,CAAC,wBAAwB;QAC5C,yCAAmB,CAAC,sBAAsB;KAC3C;IACD,OAAO,EAAE;QACP,yCAAmB,CAAC,qBAAqB;QACzC,yCAAmB,CAAC,sBAAsB;QAC1C,yCAAmB,CAAC,mBAAmB;QACvC,yCAAmB,CAAC,oBAAoB;QACxC,yCAAmB,CAAC,0BAA0B;QAC9C,yCAAmB,CAAC,2BAA2B;KAChD;IACD,KAAK,EAAE,EAAE,EAAE,uBAAuB;IAClC,KAAK,EAAE,EAAE,EAAE,uBAAuB;CACnC,CAAC;AAEF,MAAM,aAAa,GAAyE;IAC1F,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,CAAC,QAAQ,CAAC;IAClB,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAC7B,KAAK,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACtC,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC;CAChD,CAAC;AAEF,SAAS,cAAc,CAAC,IAAiC,EAAE,UAAU,IAAI,GAAG,EAAU;IACpF,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;QAAE,OAAO,EAAE,CAAC;IAEjC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAElB,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IAEpD,KAAK,MAAM,aAAa,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;QACtD,KAAK,MAAM,MAAM,IAAI,cAAc,CAAC,aAAa,EAAE,OAAO,CAAC,EAAE,CAAC;YAC5D,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7B,CAAC;AAEY,QAAA,YAAY,GAAG;IAC1B,KAAK,EAAE;QACL,IAAI,EAAE,iBAAiB;QACvB,WAAW,EAAE,oEAAoE;QACjF,OAAO,EAAE,UAAU;KACpB;IACD,KAAK,EAAE;QACL,IAAI,EAAE,iBAAiB;QACvB,WAAW,EAAE,qDAAqD;QAClE,OAAO,EAAC,YAAY,EAAE,uBAAuB;KAC9C;IACD,OAAO,EAAE;QACP,IAAI,EAAE,mBAAmB;QACzB,WAAW,EAAE,yDAAyD;QACtE,OAAO,EAAE,cAAc,CAAC,SAAS,CAAC;KACnC;IACD,MAAM,EAAE;QACN,IAAI,EAAE,kBAAkB;QACxB,WAAW,EAAE,yDAAyD;QACtE,OAAO,EAAE,cAAc,CAAC,QAAQ,CAAC;KAClC;IACD,MAAM,EAAE;QACN,IAAI,EAAE,kBAAkB;QACxB,WAAW,EAAE,2CAA2C;QACxD,OAAO,EAAE,cAAc,CAAC,QAAQ,CAAC;KAClC;CACF,CAAC"}