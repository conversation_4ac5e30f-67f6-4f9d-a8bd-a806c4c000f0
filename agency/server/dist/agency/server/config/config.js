"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
const dotenv = __importStar(require("dotenv"));
const path = __importStar(require("path"));
const yup = __importStar(require("yup"));
dotenv.config({ path: path.join(process.cwd(), ".env") });
const envVarsSchema = yup.object().shape({
    NODE_ENV: yup.string().oneOf(["production", "development", "test"]).required(),
    AGENCY_SERVER_PORT: yup.number().default(8001).required(),
    AGENCY_SERVER_HOST: yup.string().required(),
    AGENCY_JWT_SECRET: yup.string().required(),
    AGENCY_JWT_ACCESS_EXPIRATION: yup.number().default(300000),
    AGENCY_JWT_REFRESH_EXPIRATION: yup.number().default(300000),
});
let config;
try {
    const envVars = envVarsSchema.validateSync(process.env, { abortEarly: false });
    config = {
        env: envVars.NODE_ENV,
        port: envVars.AGENCY_SERVER_PORT,
        HOST: envVars.AGENCY_SERVER_HOST,
        jwt: {
            secret: envVars.AGENCY_JWT_SECRET,
            accessTokenExpirationDuration: envVars.AGENCY_JWT_ACCESS_EXPIRATION,
            refreshTokenExpirationDuration: envVars.AGENCY_JWT_REFRESH_EXPIRATION,
        },
    };
}
catch (error) {
    throw new Error(`Config validation error: ${error.errors.join(", ")}`);
}
module.exports = config;
