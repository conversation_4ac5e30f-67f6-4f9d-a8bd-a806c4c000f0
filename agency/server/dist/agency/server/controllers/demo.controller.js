"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
const httpStatus = __importStar(require("http-status"));
const catchAsync = require("../utils/catchAsync");
const getDemoData = catchAsync(async (req, res) => {
    const demoData = {
        message: "Hello from Agency Server API!",
        timestamp: new Date().toISOString(),
        version: "1.0.0",
        environment: process.env.NODE_ENV || "development",
        data: {
            users: [
                { id: 1, name: "John Doe", email: "<EMAIL>" },
                { id: 2, name: "Jane Smith", email: "<EMAIL>" },
            ],
            stats: {
                totalUsers: 2,
                activeUsers: 1,
                lastUpdated: new Date().toISOString(),
            },
        },
    };
    res.status(httpStatus.OK).json(demoData);
});
const getHealthCheck = catchAsync(async (req, res) => {
    const healthData = {
        status: "healthy",
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: "1.0.0",
    };
    res.status(httpStatus.OK).json(healthData);
});
module.exports = {
    getDemoData,
    getHealthCheck,
};
