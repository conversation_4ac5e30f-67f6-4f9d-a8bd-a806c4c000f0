import { Request, Response, NextFunction } from 'express';
/**
 * A utility function to catch errors in async functions
 * @param fn - The async function to wrap
 * @returns Express middleware function
 */
type AsyncFunction = (req: Request, res: Response, next: NextFunction) => Promise<any>;
declare const catchAsync: (fn: AsyncFunction) => (req: Request, res: Response, next: NextFunction) => void;
export = catchAsync;
//# sourceMappingURL=catchAsync.d.ts.map