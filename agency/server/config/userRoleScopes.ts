import { AgencyApiActionList } from "../../../web/packages/storeseo-enums/src/agency/agencyApiActionList";

const allActions = Object.values(AgencyApiActionList);

const adminActions = allActions.filter(action => action !== AgencyApiActionList.WORKSPACE_DELETE)


const baseRoleScopes = {
  VIEWER: [AgencyApiActionList.ANALYTICS_VIEWING_READ],
  EDITOR: [
    AgencyApiActionList.SEO_OPTIMIZATION_READ,
    AgencyApiActionList.SEO_OPTIMIZATION_WRITE,
    AgencyApiActionList.CONTENT_MANAGEMENT_READ,
    AgencyApiActionList.CONTENT_MANAGEMENT_WRITE,
    AgencyApiActionList.ANALYTICS_VIEWING_READ,
  ],
  MANAGER: [
    AgencyApiActionList.STORE_MANAGEMENT_READ,
    AgencyApiActionList.STORE_MANAGEMENT_WRITE,
    AgencyApiActionList.TEAM_OVERSIGHT_READ,
    AgencyApiActionList.TEAM_OVERSIGHT_WRITE,
    AgencyApiActionList.ADVANCED_SEO_FEATURES_READ,
    AgencyApiActionList.ADVANCED_SEO_FEATURES_WRITE,
  ],
  ADMIN: [], // will be filled later
  OWNER: [], // will be filled later
};

const roleHierarchy: Record<keyof typeof baseRoleScopes, (keyof typeof baseRoleScopes)[]> = {
  VIEWER: [],
  EDITOR: ["VIEWER"],
  MANAGER: ["EDITOR", "VIEWER"],
  ADMIN: ["MANAGER", "EDITOR", "VIEWER"],
  OWNER: ["ADMIN", "MANAGER", "EDITOR", "VIEWER"],
};

function resolveActions(role: keyof typeof baseRoleScopes, visited = new Set<string>()): AgencyApiActionList[] {
  if (visited.has(role)) return [];

  visited.add(role);

  const actions = new Set(baseRoleScopes[role] || []);

  for (const inheritedRole of roleHierarchy[role] || []) {
    for (const action of resolveActions(inheritedRole, visited)) {
      actions.add(action);
    }
  }

  return Array.from(actions);
}

export const rolsScopeMap = {
  OWNER: {
    code: "WORKSPACE_OWNER",
    description: "Full workspace control, billing, user management, all store access",
    actions: allActions, 
  },
  ADMIN: {
    code: "WORKSPACE_ADMIN",
    description: "User management, store management, all SEO features",
    actions:adminActions, // ADMIN has everything
  },
  MANAGER: {
    code: "WORKSPACE_MANAGER",
    description: "Store management, team oversight, advanced SEO features",
    actions: resolveActions("MANAGER"),
  },
  EDITOR: {
    code: "WORKSPACE_EDITOR",
    description: "SEO optimization, content management, analytics viewing",
    actions: resolveActions("EDITOR"),
  },
  VIEWER: {
    code: "WORKSPACE_VIEWER",
    description: "Read-only access to analytics and reports",
    actions: resolveActions("VIEWER"),
  },
};

