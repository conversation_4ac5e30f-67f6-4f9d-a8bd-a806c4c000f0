// @ts-check
const dotenv = require("dotenv");
const path = require("path");
const yup = require("yup");

dotenv.config({ path: path.join(process.cwd(), ".env") });

const envVarsSchema = yup.object().shape({
  NODE_ENV: yup.string().oneOf(["production", "development", "test"]).required(),
  AGENCY_SERVER_PORT: yup.number().default(8001).required(),
  AGENCY_SERVER_HOST: yup.string().required(),
  AGENCY_JWT_SECRET: yup.string().required(),
  AGENCY_JWT_ACCESS_EXPIRATION: yup.number().default(300000),
  AGENCY_JWT_REFRESH_EXPIRATION: yup.number().default(300000),
});

try {
  const envVars = envVarsSchema.validateSync(process.env, { abortEarly: false });
  module.exports = {
    env: envVars.NODE_ENV,
    port: envVars.AGENCY_SERVER_PORT,
    HOST: envVars.AGENCY_SERVER_HOST,
    jwt: {
      secret: envVars.AGENCY_JWT_SECRET,
      accessTokenExpirationDuration: envVars.AGENCY_JWT_ACCESS_EXPIRATION,
      refreshTokenExpirationDuration: envVars.AGENCY_JWT_REFRESH_EXPIRATION,
    },
  };
} catch (error) {
  throw new Error(`Config validation error: ${error.errors.join(", ")}`);
}
