// Generated by React Router

import "react-router"

declare module "react-router" {
  interface Register {
    pages: Pages
    routeFiles: RouteFiles
  }
}

type Pages = {
  "/": {
    params: {};
  };
  "/auth/register": {
    params: {};
  };
  "/dashboard": {
    params: {};
  };
};

type RouteFiles = {
  "root.tsx": {
    id: "root";
    page: "/" | "/auth/register" | "/dashboard";
  };
  "routes/home.tsx": {
    id: "routes/home";
    page: "/";
  };
  "routes/auth/register.tsx": {
    id: "routes/auth/register";
    page: "/auth/register";
  };
  "routes/dashboard.tsx": {
    id: "routes/dashboard";
    page: "/dashboard";
  };
};