function pa(e,t){for(var r=0;r<t.length;r++){const n=t[r];if(typeof n!="string"&&!Array.isArray(n)){for(const a in n)if(a!=="default"&&!(a in e)){const o=Object.getOwnPropertyDescriptor(n,a);o&&Object.defineProperty(e,a,o.get?o:{enumerable:!0,get:()=>n[a]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}function ya(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ut={exports:{}},Ze={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ir;function va(){if(Ir)return Ze;Ir=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function r(n,a,o){var l=null;if(o!==void 0&&(l=""+o),a.key!==void 0&&(l=""+a.key),"key"in a){o={};for(var u in a)u!=="key"&&(o[u]=a[u])}else o=a;return a=o.ref,{$$typeof:e,type:n,key:l,ref:a!==void 0?a:null,props:o}}return Ze.Fragment=t,Ze.jsx=r,Ze.jsxs=r,Ze}var Fr;function ga(){return Fr||(Fr=1,Ut.exports=va()),Ut.exports}var gl=ga(),zt={exports:{}},Y={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jr;function wa(){if(jr)return Y;jr=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),o=Symbol.for("react.consumer"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),i=Symbol.for("react.suspense"),s=Symbol.for("react.memo"),d=Symbol.for("react.lazy"),m=Symbol.iterator;function p(h){return h===null||typeof h!="object"?null:(h=m&&h[m]||h["@@iterator"],typeof h=="function"?h:null)}var g={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},R=Object.assign,b={};function x(h,C,k){this.props=h,this.context=C,this.refs=b,this.updater=k||g}x.prototype.isReactComponent={},x.prototype.setState=function(h,C){if(typeof h!="object"&&typeof h!="function"&&h!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,h,C,"setState")},x.prototype.forceUpdate=function(h){this.updater.enqueueForceUpdate(this,h,"forceUpdate")};function E(){}E.prototype=x.prototype;function I(h,C,k){this.props=h,this.context=C,this.refs=b,this.updater=k||g}var T=I.prototype=new E;T.constructor=I,R(T,x.prototype),T.isPureReactComponent=!0;var _=Array.isArray,S={H:null,A:null,T:null,S:null,V:null},v=Object.prototype.hasOwnProperty;function M(h,C,k,D,V,Z){return k=Z.ref,{$$typeof:e,type:h,key:C,ref:k!==void 0?k:null,props:Z}}function z(h,C){return M(h.type,C,void 0,void 0,void 0,h.props)}function U(h){return typeof h=="object"&&h!==null&&h.$$typeof===e}function Q(h){var C={"=":"=0",":":"=2"};return"$"+h.replace(/[=:]/g,function(k){return C[k]})}var oe=/\/+/g;function ue(h,C){return typeof h=="object"&&h!==null&&h.key!=null?Q(""+h.key):C.toString(36)}function le(){}function G(h){switch(h.status){case"fulfilled":return h.value;case"rejected":throw h.reason;default:switch(typeof h.status=="string"?h.then(le,le):(h.status="pending",h.then(function(C){h.status==="pending"&&(h.status="fulfilled",h.value=C)},function(C){h.status==="pending"&&(h.status="rejected",h.reason=C)})),h.status){case"fulfilled":return h.value;case"rejected":throw h.reason}}throw h}function K(h,C,k,D,V){var Z=typeof h;(Z==="undefined"||Z==="boolean")&&(h=null);var B=!1;if(h===null)B=!0;else switch(Z){case"bigint":case"string":case"number":B=!0;break;case"object":switch(h.$$typeof){case e:case t:B=!0;break;case d:return B=h._init,K(B(h._payload),C,k,D,V)}}if(B)return V=V(h),B=D===""?"."+ue(h,0):D,_(V)?(k="",B!=null&&(k=B.replace(oe,"$&/")+"/"),K(V,C,k,"",function($t){return $t})):V!=null&&(U(V)&&(V=z(V,k+(V.key==null||h&&h.key===V.key?"":(""+V.key).replace(oe,"$&/")+"/")+B)),C.push(V)),1;B=0;var Se=D===""?".":D+":";if(_(h))for(var ie=0;ie<h.length;ie++)D=h[ie],Z=Se+ue(D,ie),B+=K(D,C,k,Z,V);else if(ie=p(h),typeof ie=="function")for(h=ie.call(h),ie=0;!(D=h.next()).done;)D=D.value,Z=Se+ue(D,ie++),B+=K(D,C,k,Z,V);else if(Z==="object"){if(typeof h.then=="function")return K(G(h),C,k,D,V);throw C=String(h),Error("Objects are not valid as a React child (found: "+(C==="[object Object]"?"object with keys {"+Object.keys(h).join(", ")+"}":C)+"). If you meant to render a collection of children, use an array instead.")}return B}function X(h,C,k){if(h==null)return h;var D=[],V=0;return K(h,D,"","",function(Z){return C.call(k,Z,V++)}),D}function re(h){if(h._status===-1){var C=h._result;C=C(),C.then(function(k){(h._status===0||h._status===-1)&&(h._status=1,h._result=k)},function(k){(h._status===0||h._status===-1)&&(h._status=2,h._result=k)}),h._status===-1&&(h._status=0,h._result=C)}if(h._status===1)return h._result.default;throw h._result}var de=typeof reportError=="function"?reportError:function(h){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var C=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof h=="object"&&h!==null&&typeof h.message=="string"?String(h.message):String(h),error:h});if(!window.dispatchEvent(C))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",h);return}console.error(h)};function ce(){}return Y.Children={map:X,forEach:function(h,C,k){X(h,function(){C.apply(this,arguments)},k)},count:function(h){var C=0;return X(h,function(){C++}),C},toArray:function(h){return X(h,function(C){return C})||[]},only:function(h){if(!U(h))throw Error("React.Children.only expected to receive a single React element child.");return h}},Y.Component=x,Y.Fragment=r,Y.Profiler=a,Y.PureComponent=I,Y.StrictMode=n,Y.Suspense=i,Y.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=S,Y.__COMPILER_RUNTIME={__proto__:null,c:function(h){return S.H.useMemoCache(h)}},Y.cache=function(h){return function(){return h.apply(null,arguments)}},Y.cloneElement=function(h,C,k){if(h==null)throw Error("The argument must be a React element, but you passed "+h+".");var D=R({},h.props),V=h.key,Z=void 0;if(C!=null)for(B in C.ref!==void 0&&(Z=void 0),C.key!==void 0&&(V=""+C.key),C)!v.call(C,B)||B==="key"||B==="__self"||B==="__source"||B==="ref"&&C.ref===void 0||(D[B]=C[B]);var B=arguments.length-2;if(B===1)D.children=k;else if(1<B){for(var Se=Array(B),ie=0;ie<B;ie++)Se[ie]=arguments[ie+2];D.children=Se}return M(h.type,V,void 0,void 0,Z,D)},Y.createContext=function(h){return h={$$typeof:l,_currentValue:h,_currentValue2:h,_threadCount:0,Provider:null,Consumer:null},h.Provider=h,h.Consumer={$$typeof:o,_context:h},h},Y.createElement=function(h,C,k){var D,V={},Z=null;if(C!=null)for(D in C.key!==void 0&&(Z=""+C.key),C)v.call(C,D)&&D!=="key"&&D!=="__self"&&D!=="__source"&&(V[D]=C[D]);var B=arguments.length-2;if(B===1)V.children=k;else if(1<B){for(var Se=Array(B),ie=0;ie<B;ie++)Se[ie]=arguments[ie+2];V.children=Se}if(h&&h.defaultProps)for(D in B=h.defaultProps,B)V[D]===void 0&&(V[D]=B[D]);return M(h,Z,void 0,void 0,null,V)},Y.createRef=function(){return{current:null}},Y.forwardRef=function(h){return{$$typeof:u,render:h}},Y.isValidElement=U,Y.lazy=function(h){return{$$typeof:d,_payload:{_status:-1,_result:h},_init:re}},Y.memo=function(h,C){return{$$typeof:s,type:h,compare:C===void 0?null:C}},Y.startTransition=function(h){var C=S.T,k={};S.T=k;try{var D=h(),V=S.S;V!==null&&V(k,D),typeof D=="object"&&D!==null&&typeof D.then=="function"&&D.then(ce,de)}catch(Z){de(Z)}finally{S.T=C}},Y.unstable_useCacheRefresh=function(){return S.H.useCacheRefresh()},Y.use=function(h){return S.H.use(h)},Y.useActionState=function(h,C,k){return S.H.useActionState(h,C,k)},Y.useCallback=function(h,C){return S.H.useCallback(h,C)},Y.useContext=function(h){return S.H.useContext(h)},Y.useDebugValue=function(){},Y.useDeferredValue=function(h,C){return S.H.useDeferredValue(h,C)},Y.useEffect=function(h,C,k){var D=S.H;if(typeof k=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return D.useEffect(h,C)},Y.useId=function(){return S.H.useId()},Y.useImperativeHandle=function(h,C,k){return S.H.useImperativeHandle(h,C,k)},Y.useInsertionEffect=function(h,C){return S.H.useInsertionEffect(h,C)},Y.useLayoutEffect=function(h,C){return S.H.useLayoutEffect(h,C)},Y.useMemo=function(h,C){return S.H.useMemo(h,C)},Y.useOptimistic=function(h,C){return S.H.useOptimistic(h,C)},Y.useReducer=function(h,C,k){return S.H.useReducer(h,C,k)},Y.useRef=function(h){return S.H.useRef(h)},Y.useState=function(h){return S.H.useState(h)},Y.useSyncExternalStore=function(h,C,k){return S.H.useSyncExternalStore(h,C,k)},Y.useTransition=function(){return S.H.useTransition()},Y.version="19.1.1",Y}var Hr;function Ea(){return Hr||(Hr=1,zt.exports=wa()),zt.exports}var f=Ea();const Ra=ya(f),wl=pa({__proto__:null,default:Ra},[f]);/**
 * react-router v7.7.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var hn=e=>{throw TypeError(e)},ba=(e,t,r)=>t.has(e)||hn("Cannot "+r),Bt=(e,t,r)=>(ba(e,t,"read from private field"),r?r.call(e):t.get(e)),Sa=(e,t,r)=>t.has(e)?hn("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),Ur="popstate";function El(e={}){function t(n,a){let{pathname:o,search:l,hash:u}=n.location;return at("",{pathname:o,search:l,hash:u},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(n,a){return typeof a=="string"?a:Ae(a)}return Ca(t,r,null,e)}function J(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function ae(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function xa(){return Math.random().toString(36).substring(2,10)}function zr(e,t){return{usr:e.state,key:e.key,idx:t}}function at(e,t,r=null,n){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?ke(t):t,state:r,key:t&&t.key||n||xa()}}function Ae({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function ke(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substring(n),e=e.substring(0,n)),e&&(t.pathname=e)}return t}function Ca(e,t,r,n={}){let{window:a=document.defaultView,v5Compat:o=!1}=n,l=a.history,u="POP",i=null,s=d();s==null&&(s=0,l.replaceState({...l.state,idx:s},""));function d(){return(l.state||{idx:null}).idx}function m(){u="POP";let x=d(),E=x==null?null:x-s;s=x,i&&i({action:u,location:b.location,delta:E})}function p(x,E){u="PUSH";let I=at(b.location,x,E);s=d()+1;let T=zr(I,s),_=b.createHref(I);try{l.pushState(T,"",_)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;a.location.assign(_)}o&&i&&i({action:u,location:b.location,delta:1})}function g(x,E){u="REPLACE";let I=at(b.location,x,E);s=d();let T=zr(I,s),_=b.createHref(I);l.replaceState(T,"",_),o&&i&&i({action:u,location:b.location,delta:0})}function R(x){return mn(x)}let b={get action(){return u},get location(){return e(a,l)},listen(x){if(i)throw new Error("A history only accepts one active listener");return a.addEventListener(Ur,m),i=x,()=>{a.removeEventListener(Ur,m),i=null}},createHref(x){return t(a,x)},createURL:R,encodeLocation(x){let E=R(x);return{pathname:E.pathname,search:E.search,hash:E.hash}},push:p,replace:g,go(x){return l.go(x)}};return b}function mn(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),J(r,"No window.location.(origin|href) available to create URL");let n=typeof e=="string"?e:Ae(e);return n=n.replace(/ $/,"%20"),!t&&n.startsWith("//")&&(n=r+n),new URL(n,r)}var nt,Br=class{constructor(e){if(Sa(this,nt,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(Bt(this,nt).has(e))return Bt(this,nt).get(e);if(e.defaultValue!==void 0)return e.defaultValue;throw new Error("No value found for context")}set(e,t){Bt(this,nt).set(e,t)}};nt=new WeakMap;var La=new Set(["lazy","caseSensitive","path","id","index","children"]);function Pa(e){return La.has(e)}var Ta=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function Ma(e){return Ta.has(e)}function _a(e){return e.index===!0}function ot(e,t,r=[],n={},a=!1){return e.map((o,l)=>{let u=[...r,String(l)],i=typeof o.id=="string"?o.id:u.join("-");if(J(o.index!==!0||!o.children,"Cannot specify children on an index route"),J(a||!n[i],`Found a route id collision on id "${i}".  Route id's must be globally unique within Data Router usages`),_a(o)){let s={...o,...t(o),id:i};return n[i]=s,s}else{let s={...o,...t(o),id:i,children:void 0};return n[i]=s,o.children&&(s.children=ot(o.children,t,u,n,a)),s}})}function Te(e,t,r="/"){return St(e,t,r,!1)}function St(e,t,r,n){let a=typeof t=="string"?ke(t):t,o=we(a.pathname||"/",r);if(o==null)return null;let l=yn(e);Da(l);let u=null;for(let i=0;u==null&&i<l.length;++i){let s=Ua(o);u=Ha(l[i],s,n)}return u}function pn(e,t){let{route:r,pathname:n,params:a}=e;return{id:r.id,pathname:n,params:a,data:t[r.id],handle:r.handle}}function yn(e,t=[],r=[],n=""){let a=(o,l,u)=>{let i={relativePath:u===void 0?o.path||"":u,caseSensitive:o.caseSensitive===!0,childrenIndex:l,route:o};i.relativePath.startsWith("/")&&(J(i.relativePath.startsWith(n),`Absolute route path "${i.relativePath}" nested under path "${n}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(n.length));let s=Ce([n,i.relativePath]),d=r.concat(i);o.children&&o.children.length>0&&(J(o.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${s}".`),yn(o.children,t,d,s)),!(o.path==null&&!o.index)&&t.push({path:s,score:Fa(s,o.index),routesMeta:d})};return e.forEach((o,l)=>{var u;if(o.path===""||!((u=o.path)!=null&&u.includes("?")))a(o,l);else for(let i of vn(o.path))a(o,l,i)}),t}function vn(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,a=r.endsWith("?"),o=r.replace(/\?$/,"");if(n.length===0)return a?[o,""]:[o];let l=vn(n.join("/")),u=[];return u.push(...l.map(i=>i===""?o:[o,i].join("/"))),a&&u.push(...l),u.map(i=>e.startsWith("/")&&i===""?"/":i)}function Da(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:ja(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}var Oa=/^:[\w-]+$/,Aa=3,ka=2,Na=1,$a=10,Ia=-2,Wr=e=>e==="*";function Fa(e,t){let r=e.split("/"),n=r.length;return r.some(Wr)&&(n+=Ia),t&&(n+=ka),r.filter(a=>!Wr(a)).reduce((a,o)=>a+(Oa.test(o)?Aa:o===""?Na:$a),n)}function ja(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function Ha(e,t,r=!1){let{routesMeta:n}=e,a={},o="/",l=[];for(let u=0;u<n.length;++u){let i=n[u],s=u===n.length-1,d=o==="/"?t:t.slice(o.length)||"/",m=Mt({path:i.relativePath,caseSensitive:i.caseSensitive,end:s},d),p=i.route;if(!m&&s&&r&&!n[n.length-1].route.index&&(m=Mt({path:i.relativePath,caseSensitive:i.caseSensitive,end:!1},d)),!m)return null;Object.assign(a,m.params),l.push({params:a,pathname:Ce([o,m.pathname]),pathnameBase:Ya(Ce([o,m.pathnameBase])),route:p}),m.pathnameBase!=="/"&&(o=Ce([o,m.pathnameBase]))}return l}function Mt(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=gn(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let o=a[0],l=o.replace(/(.)\/+$/,"$1"),u=a.slice(1);return{params:n.reduce((s,{paramName:d,isOptional:m},p)=>{if(d==="*"){let R=u[p]||"";l=o.slice(0,o.length-R.length).replace(/(.)\/+$/,"$1")}const g=u[p];return m&&!g?s[d]=void 0:s[d]=(g||"").replace(/%2F/g,"/"),s},{}),pathname:o,pathnameBase:l,pattern:e}}function gn(e,t=!1,r=!0){ae(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,u,i)=>(n.push({paramName:u,isOptional:i!=null}),i?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function Ua(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return ae(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function we(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function za({basename:e,pathname:t}){return t==="/"?e:Ce([e,t])}function Ba(e,t="/"){let{pathname:r,search:n="",hash:a=""}=typeof e=="string"?ke(e):e;return{pathname:r?r.startsWith("/")?r:Wa(r,t):t,search:Va(n),hash:Ja(a)}}function Wa(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function Wt(e,t,r,n){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(n)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function wn(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function sr(e){let t=wn(e);return t.map((r,n)=>n===t.length-1?r.pathname:r.pathnameBase)}function ur(e,t,r,n=!1){let a;typeof e=="string"?a=ke(e):(a={...e},J(!a.pathname||!a.pathname.includes("?"),Wt("?","pathname","search",a)),J(!a.pathname||!a.pathname.includes("#"),Wt("#","pathname","hash",a)),J(!a.search||!a.search.includes("#"),Wt("#","search","hash",a)));let o=e===""||a.pathname==="",l=o?"/":a.pathname,u;if(l==null)u=r;else{let m=t.length-1;if(!n&&l.startsWith("..")){let p=l.split("/");for(;p[0]==="..";)p.shift(),m-=1;a.pathname=p.join("/")}u=m>=0?t[m]:"/"}let i=Ba(a,u),s=l&&l!=="/"&&l.endsWith("/"),d=(o||l===".")&&r.endsWith("/");return!i.pathname.endsWith("/")&&(s||d)&&(i.pathname+="/"),i}var Ce=e=>e.join("/").replace(/\/\/+/g,"/"),Ya=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Va=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ja=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e,Ga=class{constructor(e,t){this.type="DataWithResponseInit",this.data=e,this.init=t||null}};function Xa(e,t){return new Ga(e,typeof t=="number"?{status:t}:t)}var Ka=(e,t=302)=>{let r=t;typeof r=="number"?r={status:r}:typeof r.status>"u"&&(r.status=302);let n=new Headers(r.headers);return n.set("Location",e),new Response(null,{...r,headers:n})},Ue=class{constructor(e,t,r,n=!1){this.status=e,this.statusText=t||"",this.internal=n,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function ze(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var En=["POST","PUT","PATCH","DELETE"],qa=new Set(En),Qa=["GET",...En],Za=new Set(Qa),eo=new Set([301,302,303,307,308]),to=new Set([307,308]),Yt={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},ro={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},et={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},no=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,cr=e=>no.test(e),ao=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),Rn="remix-router-transitions",bn=Symbol("ResetLoaderData");function Rl(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u";J(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let n=e.hydrationRouteProperties||[],a=e.mapRouteProperties||ao,o={},l=ot(e.routes,a,void 0,o),u,i=e.basename||"/",s=e.dataStrategy||uo,d={unstable_middleware:!1,...e.future},m=null,p=new Set,g=null,R=null,b=null,x=e.hydrationData!=null,E=Te(l,e.history.location,i),I=!1,T=null,_;if(E==null&&!e.patchRoutesOnNavigation){let c=Ee(404,{pathname:e.history.location.pathname}),{matches:y,route:w}=rn(l);_=!0,E=y,T={[w.id]:c}}else if(E&&!e.hydrationData&&ft(E,l,e.history.location.pathname).active&&(E=null),E)if(E.some(c=>c.route.lazy))_=!1;else if(!E.some(c=>c.route.loader))_=!0;else{let c=e.hydrationData?e.hydrationData.loaderData:null,y=e.hydrationData?e.hydrationData.errors:null;if(y){let w=E.findIndex(L=>y[L.route.id]!==void 0);_=E.slice(0,w+1).every(L=>!Qt(L.route,c,y))}else _=E.every(w=>!Qt(w.route,c,y))}else{_=!1,E=[];let c=ft(null,l,e.history.location.pathname);c.active&&c.matches&&(I=!0,E=c.matches)}let S,v={historyAction:e.history.action,location:e.history.location,matches:E,initialized:_,navigation:Yt,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||T,fetchers:new Map,blockers:new Map},M="POP",z=!1,U,Q=!1,oe=new Map,ue=null,le=!1,G=!1,K=new Set,X=new Map,re=0,de=-1,ce=new Map,h=new Set,C=new Map,k=new Map,D=new Set,V=new Map,Z,B=null;function Se(){if(m=e.history.listen(({action:c,location:y,delta:w})=>{if(Z){Z(),Z=void 0;return}ae(V.size===0||w!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let L=Ar({currentLocation:v.location,nextLocation:y,historyAction:c});if(L&&w!=null){let P=new Promise(A=>{Z=A});e.history.go(w*-1),dt(L,{state:"blocked",location:y,proceed(){dt(L,{state:"proceeding",proceed:void 0,reset:void 0,location:y}),P.then(()=>e.history.go(w))},reset(){let A=new Map(v.blockers);A.set(L,et),fe({blockers:A})}});return}return Ne(c,y)}),r){Ro(t,oe);let c=()=>bo(t,oe);t.addEventListener("pagehide",c),ue=()=>t.removeEventListener("pagehide",c)}return v.initialized||Ne("POP",v.location,{initialHydration:!0}),S}function ie(){m&&m(),ue&&ue(),p.clear(),U&&U.abort(),v.fetchers.forEach((c,y)=>Ft(y)),v.blockers.forEach((c,y)=>Or(y))}function $t(c){return p.add(c),()=>p.delete(c)}function fe(c,y={}){c.matches&&(c.matches=c.matches.map(P=>{let A=o[P.route.id],N=P.route;return N.element!==A.element||N.errorElement!==A.errorElement||N.hydrateFallbackElement!==A.hydrateFallbackElement?{...P,route:A}:P})),v={...v,...c};let w=[],L=[];v.fetchers.forEach((P,A)=>{P.state==="idle"&&(D.has(A)?w.push(A):L.push(A))}),D.forEach(P=>{!v.fetchers.has(P)&&!X.has(P)&&w.push(P)}),[...p].forEach(P=>P(v,{deletedFetchers:w,viewTransitionOpts:y.viewTransitionOpts,flushSync:y.flushSync===!0})),w.forEach(P=>Ft(P)),L.forEach(P=>v.fetchers.delete(P))}function We(c,y,{flushSync:w}={}){var j,$;let L=v.actionData!=null&&v.navigation.formMethod!=null&&ve(v.navigation.formMethod)&&v.navigation.state==="loading"&&((j=c.state)==null?void 0:j._isRedirect)!==!0,P;y.actionData?Object.keys(y.actionData).length>0?P=y.actionData:P=null:L?P=v.actionData:P=null;let A=y.loaderData?en(v.loaderData,y.loaderData,y.matches||[],y.errors):v.loaderData,N=v.blockers;N.size>0&&(N=new Map(N),N.forEach((W,te)=>N.set(te,et)));let O=le?!1:Nr(c,y.matches||v.matches),F=z===!0||v.navigation.formMethod!=null&&ve(v.navigation.formMethod)&&(($=c.state)==null?void 0:$._isRedirect)!==!0;u&&(l=u,u=void 0),le||M==="POP"||(M==="PUSH"?e.history.push(c,c.state):M==="REPLACE"&&e.history.replace(c,c.state));let H;if(M==="POP"){let W=oe.get(v.location.pathname);W&&W.has(c.pathname)?H={currentLocation:v.location,nextLocation:c}:oe.has(c.pathname)&&(H={currentLocation:c,nextLocation:v.location})}else if(Q){let W=oe.get(v.location.pathname);W?W.add(c.pathname):(W=new Set([c.pathname]),oe.set(v.location.pathname,W)),H={currentLocation:v.location,nextLocation:c}}fe({...y,actionData:P,loaderData:A,historyAction:M,location:c,initialized:!0,navigation:Yt,revalidation:"idle",restoreScrollPosition:O,preventScrollReset:F,blockers:N},{viewTransitionOpts:H,flushSync:w===!0}),M="POP",z=!1,Q=!1,le=!1,G=!1,B==null||B.resolve(),B=null}async function Cr(c,y){if(typeof c=="number"){e.history.go(c);return}let w=qt(v.location,v.matches,i,c,y==null?void 0:y.fromRouteId,y==null?void 0:y.relative),{path:L,submission:P,error:A}=Yr(!1,w,y),N=v.location,O=at(v.location,L,y&&y.state);O={...O,...e.history.encodeLocation(O)};let F=y&&y.replace!=null?y.replace:void 0,H="PUSH";F===!0?H="REPLACE":F===!1||P!=null&&ve(P.formMethod)&&P.formAction===v.location.pathname+v.location.search&&(H="REPLACE");let j=y&&"preventScrollReset"in y?y.preventScrollReset===!0:void 0,$=(y&&y.flushSync)===!0,W=Ar({currentLocation:N,nextLocation:O,historyAction:H});if(W){dt(W,{state:"blocked",location:O,proceed(){dt(W,{state:"proceeding",proceed:void 0,reset:void 0,location:O}),Cr(c,y)},reset(){let te=new Map(v.blockers);te.set(W,et),fe({blockers:te})}});return}await Ne(H,O,{submission:P,pendingError:A,preventScrollReset:j,replace:y&&y.replace,enableViewTransition:y&&y.viewTransition,flushSync:$})}function ta(){B||(B=So()),It(),fe({revalidation:"loading"});let c=B.promise;return v.navigation.state==="submitting"?c:v.navigation.state==="idle"?(Ne(v.historyAction,v.location,{startUninterruptedRevalidation:!0}),c):(Ne(M||v.historyAction,v.navigation.location,{overrideNavigation:v.navigation,enableViewTransition:Q===!0}),c)}async function Ne(c,y,w){U&&U.abort(),U=null,M=c,le=(w&&w.startUninterruptedRevalidation)===!0,da(v.location,v.matches),z=(w&&w.preventScrollReset)===!0,Q=(w&&w.enableViewTransition)===!0;let L=u||l,P=w&&w.overrideNavigation,A=w!=null&&w.initialHydration&&v.matches&&v.matches.length>0&&!I?v.matches:Te(L,y,i),N=(w&&w.flushSync)===!0;if(A&&v.initialized&&!G&&yo(v.location,y)&&!(w&&w.submission&&ve(w.submission.formMethod))){We(y,{matches:A},{flushSync:N});return}let O=ft(A,L,y.pathname);if(O.active&&O.matches&&(A=O.matches),!A){let{error:pe,notFoundMatches:ne,route:ee}=jt(y.pathname);We(y,{matches:ne,loaderData:{},errors:{[ee.id]:pe}},{flushSync:N});return}U=new AbortController;let F=Ye(e.history,y,U.signal,w&&w.submission),H=new Br(e.unstable_getContext?await e.unstable_getContext():void 0),j;if(w&&w.pendingError)j=[je(A).route.id,{type:"error",error:w.pendingError}];else if(w&&w.submission&&ve(w.submission.formMethod)){let pe=await ra(F,y,w.submission,A,H,O.active,w&&w.initialHydration===!0,{replace:w.replace,flushSync:N});if(pe.shortCircuited)return;if(pe.pendingActionResult){let[ne,ee]=pe.pendingActionResult;if(ge(ee)&&ze(ee.error)&&ee.error.status===404){U=null,We(y,{matches:pe.matches,loaderData:{},errors:{[ne]:ee.error}});return}}A=pe.matches||A,j=pe.pendingActionResult,P=Vt(y,w.submission),N=!1,O.active=!1,F=Ye(e.history,F.url,F.signal)}let{shortCircuited:$,matches:W,loaderData:te,errors:he}=await na(F,y,A,H,O.active,P,w&&w.submission,w&&w.fetcherSubmission,w&&w.replace,w&&w.initialHydration===!0,N,j);$||(U=null,We(y,{matches:W||A,...tn(j),loaderData:te,errors:he}))}async function ra(c,y,w,L,P,A,N,O={}){It();let F=wo(y,w);if(fe({navigation:F},{flushSync:O.flushSync===!0}),A){let $=await ht(L,y.pathname,c.signal);if($.type==="aborted")return{shortCircuited:!0};if($.type==="error"){let W=je($.partialMatches).route.id;return{matches:$.partialMatches,pendingActionResult:[W,{type:"error",error:$.error}]}}else if($.matches)L=$.matches;else{let{notFoundMatches:W,error:te,route:he}=jt(y.pathname);return{matches:W,pendingActionResult:[he.id,{type:"error",error:te}]}}}let H,j=xt(L,y);if(!j.route.action&&!j.route.lazy)H={type:"error",error:Ee(405,{method:c.method,pathname:y.pathname,routeId:j.route.id})};else{let $=Ve(a,o,c,L,j,N?[]:n,P),W=await Xe(c,$,P,null);if(H=W[j.route.id],!H){for(let te of L)if(W[te.route.id]){H=W[te.route.id];break}}if(c.signal.aborted)return{shortCircuited:!0}}if(He(H)){let $;return O&&O.replace!=null?$=O.replace:$=qr(H.response.headers.get("Location"),new URL(c.url),i)===v.location.pathname+v.location.search,await $e(c,H,!0,{submission:w,replace:$}),{shortCircuited:!0}}if(ge(H)){let $=je(L,j.route.id);return(O&&O.replace)!==!0&&(M="PUSH"),{matches:L,pendingActionResult:[$.route.id,H,j.route.id]}}return{matches:L,pendingActionResult:[j.route.id,H]}}async function na(c,y,w,L,P,A,N,O,F,H,j,$){let W=A||Vt(y,N),te=N||O||nn(W),he=!le&&!H;if(P){if(he){let me=Lr($);fe({navigation:W,...me!==void 0?{actionData:me}:{}},{flushSync:j})}let q=await ht(w,y.pathname,c.signal);if(q.type==="aborted")return{shortCircuited:!0};if(q.type==="error"){let me=je(q.partialMatches).route.id;return{matches:q.partialMatches,loaderData:{},errors:{[me]:q.error}}}else if(q.matches)w=q.matches;else{let{error:me,notFoundMatches:yt,route:Qe}=jt(y.pathname);return{matches:yt,loaderData:{},errors:{[Qe.id]:me}}}}let pe=u||l,{dsMatches:ne,revalidatingFetchers:ee}=Vr(c,L,a,o,e.history,v,w,te,y,H?[]:n,H===!0,G,K,D,C,h,pe,i,e.patchRoutesOnNavigation!=null,$);if(de=++re,!e.dataStrategy&&!ne.some(q=>q.shouldLoad)&&ee.length===0){let q=_r();return We(y,{matches:w,loaderData:{},errors:$&&ge($[1])?{[$[0]]:$[1].error}:null,...tn($),...q?{fetchers:new Map(v.fetchers)}:{}},{flushSync:j}),{shortCircuited:!0}}if(he){let q={};if(!P){q.navigation=W;let me=Lr($);me!==void 0&&(q.actionData=me)}ee.length>0&&(q.fetchers=aa(ee)),fe(q,{flushSync:j})}ee.forEach(q=>{_e(q.key),q.controller&&X.set(q.key,q.controller)});let Ie=()=>ee.forEach(q=>_e(q.key));U&&U.signal.addEventListener("abort",Ie);let{loaderResults:Ke,fetcherResults:De}=await Pr(ne,ee,c,L);if(c.signal.aborted)return{shortCircuited:!0};U&&U.signal.removeEventListener("abort",Ie),ee.forEach(q=>X.delete(q.key));let xe=gt(Ke);if(xe)return await $e(c,xe.result,!0,{replace:F}),{shortCircuited:!0};if(xe=gt(De),xe)return h.add(xe.key),await $e(c,xe.result,!0,{replace:F}),{shortCircuited:!0};let{loaderData:Ht,errors:qe}=Zr(v,w,Ke,$,ee,De);H&&v.errors&&(qe={...v.errors,...qe});let Fe=_r(),mt=Dr(de),pt=Fe||mt||ee.length>0;return{matches:w,loaderData:Ht,errors:qe,...pt?{fetchers:new Map(v.fetchers)}:{}}}function Lr(c){if(c&&!ge(c[1]))return{[c[0]]:c[1].data};if(v.actionData)return Object.keys(v.actionData).length===0?null:v.actionData}function aa(c){return c.forEach(y=>{let w=v.fetchers.get(y.key),L=tt(void 0,w?w.data:void 0);v.fetchers.set(y.key,L)}),new Map(v.fetchers)}async function oa(c,y,w,L){_e(c);let P=(L&&L.flushSync)===!0,A=u||l,N=qt(v.location,v.matches,i,w,y,L==null?void 0:L.relative),O=Te(A,N,i),F=ft(O,A,N);if(F.active&&F.matches&&(O=F.matches),!O){Pe(c,y,Ee(404,{pathname:N}),{flushSync:P});return}let{path:H,submission:j,error:$}=Yr(!0,N,L);if($){Pe(c,y,$,{flushSync:P});return}let W=new Br(e.unstable_getContext?await e.unstable_getContext():void 0),te=(L&&L.preventScrollReset)===!0;if(j&&ve(j.formMethod)){await ia(c,y,H,O,W,F.active,P,te,j);return}C.set(c,{routeId:y,path:H}),await la(c,y,H,O,W,F.active,P,te,j)}async function ia(c,y,w,L,P,A,N,O,F){It(),C.delete(c);let H=v.fetchers.get(c);Me(c,Eo(F,H),{flushSync:N});let j=new AbortController,$=Ye(e.history,w,j.signal,F);if(A){let se=await ht(L,new URL($.url).pathname,$.signal,c);if(se.type==="aborted")return;if(se.type==="error"){Pe(c,y,se.error,{flushSync:N});return}else if(se.matches)L=se.matches;else{Pe(c,y,Ee(404,{pathname:w}),{flushSync:N});return}}let W=xt(L,w);if(!W.route.action&&!W.route.lazy){let se=Ee(405,{method:F.formMethod,pathname:w,routeId:y});Pe(c,y,se,{flushSync:N});return}X.set(c,j);let te=re,he=Ve(a,o,$,L,W,n,P),ne=(await Xe($,he,P,c))[W.route.id];if($.signal.aborted){X.get(c)===j&&X.delete(c);return}if(D.has(c)){if(He(ne)||ge(ne)){Me(c,Oe(void 0));return}}else{if(He(ne))if(X.delete(c),de>te){Me(c,Oe(void 0));return}else return h.add(c),Me(c,tt(F)),$e($,ne,!1,{fetcherSubmission:F,preventScrollReset:O});if(ge(ne)){Pe(c,y,ne.error);return}}let ee=v.navigation.location||v.location,Ie=Ye(e.history,ee,j.signal),Ke=u||l,De=v.navigation.state!=="idle"?Te(Ke,v.navigation.location,i):v.matches;J(De,"Didn't find any matches after fetcher action");let xe=++re;ce.set(c,xe);let Ht=tt(F,ne.data);v.fetchers.set(c,Ht);let{dsMatches:qe,revalidatingFetchers:Fe}=Vr(Ie,P,a,o,e.history,v,De,F,ee,n,!1,G,K,D,C,h,Ke,i,e.patchRoutesOnNavigation!=null,[W.route.id,ne]);Fe.filter(se=>se.key!==c).forEach(se=>{let vt=se.key,$r=v.fetchers.get(vt),ma=tt(void 0,$r?$r.data:void 0);v.fetchers.set(vt,ma),_e(vt),se.controller&&X.set(vt,se.controller)}),fe({fetchers:new Map(v.fetchers)});let mt=()=>Fe.forEach(se=>_e(se.key));j.signal.addEventListener("abort",mt);let{loaderResults:pt,fetcherResults:q}=await Pr(qe,Fe,Ie,P);if(j.signal.aborted)return;if(j.signal.removeEventListener("abort",mt),ce.delete(c),X.delete(c),Fe.forEach(se=>X.delete(se.key)),v.fetchers.has(c)){let se=Oe(ne.data);v.fetchers.set(c,se)}let me=gt(pt);if(me)return $e(Ie,me.result,!1,{preventScrollReset:O});if(me=gt(q),me)return h.add(me.key),$e(Ie,me.result,!1,{preventScrollReset:O});let{loaderData:yt,errors:Qe}=Zr(v,De,pt,void 0,Fe,q);Dr(xe),v.navigation.state==="loading"&&xe>de?(J(M,"Expected pending action"),U&&U.abort(),We(v.navigation.location,{matches:De,loaderData:yt,errors:Qe,fetchers:new Map(v.fetchers)})):(fe({errors:Qe,loaderData:en(v.loaderData,yt,De,Qe),fetchers:new Map(v.fetchers)}),G=!1)}async function la(c,y,w,L,P,A,N,O,F){let H=v.fetchers.get(c);Me(c,tt(F,H?H.data:void 0),{flushSync:N});let j=new AbortController,$=Ye(e.history,w,j.signal);if(A){let ee=await ht(L,new URL($.url).pathname,$.signal,c);if(ee.type==="aborted")return;if(ee.type==="error"){Pe(c,y,ee.error,{flushSync:N});return}else if(ee.matches)L=ee.matches;else{Pe(c,y,Ee(404,{pathname:w}),{flushSync:N});return}}let W=xt(L,w);X.set(c,j);let te=re,he=Ve(a,o,$,L,W,n,P),ne=(await Xe($,he,P,c))[W.route.id];if(X.get(c)===j&&X.delete(c),!$.signal.aborted){if(D.has(c)){Me(c,Oe(void 0));return}if(He(ne))if(de>te){Me(c,Oe(void 0));return}else{h.add(c),await $e($,ne,!1,{preventScrollReset:O});return}if(ge(ne)){Pe(c,y,ne.error);return}Me(c,Oe(ne.data))}}async function $e(c,y,w,{submission:L,fetcherSubmission:P,preventScrollReset:A,replace:N}={}){y.response.headers.has("X-Remix-Revalidate")&&(G=!0);let O=y.response.headers.get("Location");J(O,"Expected a Location header on the redirect Response"),O=qr(O,new URL(c.url),i);let F=at(v.location,O,{_isRedirect:!0});if(r){let he=!1;if(y.response.headers.has("X-Remix-Reload-Document"))he=!0;else if(cr(O)){const pe=mn(O,!0);he=pe.origin!==t.location.origin||we(pe.pathname,i)==null}if(he){N?t.location.replace(O):t.location.assign(O);return}}U=null;let H=N===!0||y.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:j,formAction:$,formEncType:W}=v.navigation;!L&&!P&&j&&$&&W&&(L=nn(v.navigation));let te=L||P;if(to.has(y.response.status)&&te&&ve(te.formMethod))await Ne(H,F,{submission:{...te,formAction:O},preventScrollReset:A||z,enableViewTransition:w?Q:void 0});else{let he=Vt(F,L);await Ne(H,F,{overrideNavigation:he,fetcherSubmission:P,preventScrollReset:A||z,enableViewTransition:w?Q:void 0})}}async function Xe(c,y,w,L){let P,A={};try{P=await co(s,c,y,L,w,!1)}catch(N){return y.filter(O=>O.shouldLoad).forEach(O=>{A[O.route.id]={type:"error",error:N}}),A}if(c.signal.aborted)return A;for(let[N,O]of Object.entries(P))if(vo(O)){let F=O.result;A[N]={type:"redirect",response:mo(F,c,N,y,i)}}else A[N]=await ho(O);return A}async function Pr(c,y,w,L){let P=Xe(w,c,L,null),A=Promise.all(y.map(async F=>{if(F.matches&&F.match&&F.request&&F.controller){let j=(await Xe(F.request,F.matches,L,F.key))[F.match.route.id];return{[F.key]:j}}else return Promise.resolve({[F.key]:{type:"error",error:Ee(404,{pathname:F.path})}})})),N=await P,O=(await A).reduce((F,H)=>Object.assign(F,H),{});return{loaderResults:N,fetcherResults:O}}function It(){G=!0,C.forEach((c,y)=>{X.has(y)&&K.add(y),_e(y)})}function Me(c,y,w={}){v.fetchers.set(c,y),fe({fetchers:new Map(v.fetchers)},{flushSync:(w&&w.flushSync)===!0})}function Pe(c,y,w,L={}){let P=je(v.matches,y);Ft(c),fe({errors:{[P.route.id]:w},fetchers:new Map(v.fetchers)},{flushSync:(L&&L.flushSync)===!0})}function Tr(c){return k.set(c,(k.get(c)||0)+1),D.has(c)&&D.delete(c),v.fetchers.get(c)||ro}function Ft(c){let y=v.fetchers.get(c);X.has(c)&&!(y&&y.state==="loading"&&ce.has(c))&&_e(c),C.delete(c),ce.delete(c),h.delete(c),D.delete(c),K.delete(c),v.fetchers.delete(c)}function sa(c){let y=(k.get(c)||0)-1;y<=0?(k.delete(c),D.add(c)):k.set(c,y),fe({fetchers:new Map(v.fetchers)})}function _e(c){let y=X.get(c);y&&(y.abort(),X.delete(c))}function Mr(c){for(let y of c){let w=Tr(y),L=Oe(w.data);v.fetchers.set(y,L)}}function _r(){let c=[],y=!1;for(let w of h){let L=v.fetchers.get(w);J(L,`Expected fetcher: ${w}`),L.state==="loading"&&(h.delete(w),c.push(w),y=!0)}return Mr(c),y}function Dr(c){let y=[];for(let[w,L]of ce)if(L<c){let P=v.fetchers.get(w);J(P,`Expected fetcher: ${w}`),P.state==="loading"&&(_e(w),ce.delete(w),y.push(w))}return Mr(y),y.length>0}function ua(c,y){let w=v.blockers.get(c)||et;return V.get(c)!==y&&V.set(c,y),w}function Or(c){v.blockers.delete(c),V.delete(c)}function dt(c,y){let w=v.blockers.get(c)||et;J(w.state==="unblocked"&&y.state==="blocked"||w.state==="blocked"&&y.state==="blocked"||w.state==="blocked"&&y.state==="proceeding"||w.state==="blocked"&&y.state==="unblocked"||w.state==="proceeding"&&y.state==="unblocked",`Invalid blocker state transition: ${w.state} -> ${y.state}`);let L=new Map(v.blockers);L.set(c,y),fe({blockers:L})}function Ar({currentLocation:c,nextLocation:y,historyAction:w}){if(V.size===0)return;V.size>1&&ae(!1,"A router only supports one blocker at a time");let L=Array.from(V.entries()),[P,A]=L[L.length-1],N=v.blockers.get(P);if(!(N&&N.state==="proceeding")&&A({currentLocation:c,nextLocation:y,historyAction:w}))return P}function jt(c){let y=Ee(404,{pathname:c}),w=u||l,{matches:L,route:P}=rn(w);return{notFoundMatches:L,route:P,error:y}}function ca(c,y,w){if(g=c,b=y,R=w||null,!x&&v.navigation===Yt){x=!0;let L=Nr(v.location,v.matches);L!=null&&fe({restoreScrollPosition:L})}return()=>{g=null,b=null,R=null}}function kr(c,y){return R&&R(c,y.map(L=>pn(L,v.loaderData)))||c.key}function da(c,y){if(g&&b){let w=kr(c,y);g[w]=b()}}function Nr(c,y){if(g){let w=kr(c,y),L=g[w];if(typeof L=="number")return L}return null}function ft(c,y,w){if(e.patchRoutesOnNavigation)if(c){if(Object.keys(c[0].params).length>0)return{active:!0,matches:St(y,w,i,!0)}}else return{active:!0,matches:St(y,w,i,!0)||[]};return{active:!1,matches:null}}async function ht(c,y,w,L){if(!e.patchRoutesOnNavigation)return{type:"success",matches:c};let P=c;for(;;){let A=u==null,N=u||l,O=o;try{await e.patchRoutesOnNavigation({signal:w,path:y,matches:P,fetcherKey:L,patch:(j,$)=>{w.aborted||Jr(j,$,N,O,a,!1)}})}catch(j){return{type:"error",error:j,partialMatches:P}}finally{A&&!w.aborted&&(l=[...l])}if(w.aborted)return{type:"aborted"};let F=Te(N,y,i);if(F)return{type:"success",matches:F};let H=St(N,y,i,!0);if(!H||P.length===H.length&&P.every((j,$)=>j.route.id===H[$].route.id))return{type:"success",matches:null};P=H}}function fa(c){o={},u=ot(c,a,void 0,o)}function ha(c,y,w=!1){let L=u==null;Jr(c,y,u||l,o,a,w),L&&(l=[...l],fe({}))}return S={get basename(){return i},get future(){return d},get state(){return v},get routes(){return l},get window(){return t},initialize:Se,subscribe:$t,enableScrollRestoration:ca,navigate:Cr,fetch:oa,revalidate:ta,createHref:c=>e.history.createHref(c),encodeLocation:c=>e.history.encodeLocation(c),getFetcher:Tr,deleteFetcher:sa,dispose:ie,getBlocker:ua,deleteBlocker:Or,patchRoutes:ha,_internalFetchControllers:X,_internalSetRoutes:fa,_internalSetStateDoNotUseOrYouWillBreakYourApp(c){fe(c)}},S}function oo(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function qt(e,t,r,n,a,o){let l,u;if(a){l=[];for(let s of t)if(l.push(s),s.route.id===a){u=s;break}}else l=t,u=t[t.length-1];let i=ur(n||".",sr(l),we(e.pathname,r)||e.pathname,o==="path");if(n==null&&(i.search=e.search,i.hash=e.hash),(n==null||n===""||n===".")&&u){let s=fr(i.search);if(u.route.index&&!s)i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index";else if(!u.route.index&&s){let d=new URLSearchParams(i.search),m=d.getAll("index");d.delete("index"),m.filter(g=>g).forEach(g=>d.append("index",g));let p=d.toString();i.search=p?`?${p}`:""}}return r!=="/"&&(i.pathname=za({basename:r,pathname:i.pathname})),Ae(i)}function Yr(e,t,r){if(!r||!oo(r))return{path:t};if(r.formMethod&&!go(r.formMethod))return{path:t,error:Ee(405,{method:r.formMethod})};let n=()=>({path:t,error:Ee(400,{type:"invalid-body"})}),o=(r.formMethod||"get").toUpperCase(),l=Tn(t);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!ve(o))return n();let m=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((p,[g,R])=>`${p}${g}=${R}
`,""):String(r.body);return{path:t,submission:{formMethod:o,formAction:l,formEncType:r.formEncType,formData:void 0,json:void 0,text:m}}}else if(r.formEncType==="application/json"){if(!ve(o))return n();try{let m=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:o,formAction:l,formEncType:r.formEncType,formData:void 0,json:m,text:void 0}}}catch{return n()}}}J(typeof FormData=="function","FormData is not available in this environment");let u,i;if(r.formData)u=er(r.formData),i=r.formData;else if(r.body instanceof FormData)u=er(r.body),i=r.body;else if(r.body instanceof URLSearchParams)u=r.body,i=Qr(u);else if(r.body==null)u=new URLSearchParams,i=new FormData;else try{u=new URLSearchParams(r.body),i=Qr(u)}catch{return n()}let s={formMethod:o,formAction:l,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:i,json:void 0,text:void 0};if(ve(s.formMethod))return{path:t,submission:s};let d=ke(t);return e&&d.search&&fr(d.search)&&u.append("index",""),d.search=`?${u}`,{path:Ae(d),submission:s}}function Vr(e,t,r,n,a,o,l,u,i,s,d,m,p,g,R,b,x,E,I,T){var le;let _=T?ge(T[1])?T[1].error:T[1].data:void 0,S=a.createURL(o.location),v=a.createURL(i),M;if(d&&o.errors){let G=Object.keys(o.errors)[0];M=l.findIndex(K=>K.route.id===G)}else if(T&&ge(T[1])){let G=T[0];M=l.findIndex(K=>K.route.id===G)-1}let z=T?T[1].statusCode:void 0,U=z&&z>=400,Q={currentUrl:S,currentParams:((le=o.matches[0])==null?void 0:le.params)||{},nextUrl:v,nextParams:l[0].params,...u,actionResult:_,actionStatus:z},oe=l.map((G,K)=>{let{route:X}=G,re=null;if(M!=null&&K>M?re=!1:X.lazy?re=!0:X.loader==null?re=!1:d?re=Qt(X,o.loaderData,o.errors):io(o.loaderData,o.matches[K],G)&&(re=!0),re!==null)return Zt(r,n,e,G,s,t,re);let de=U?!1:m||S.pathname+S.search===v.pathname+v.search||S.search!==v.search||lo(o.matches[K],G),ce={...Q,defaultShouldRevalidate:de},h=_t(G,ce);return Zt(r,n,e,G,s,t,h,ce)}),ue=[];return R.forEach((G,K)=>{if(d||!l.some(D=>D.route.id===G.routeId)||g.has(K))return;let X=o.fetchers.get(K),re=X&&X.state!=="idle"&&X.data===void 0,de=Te(x,G.path,E);if(!de){if(I&&re)return;ue.push({key:K,routeId:G.routeId,path:G.path,matches:null,match:null,request:null,controller:null});return}if(b.has(K))return;let ce=xt(de,G.path),h=new AbortController,C=Ye(a,G.path,h.signal),k=null;if(p.has(K))p.delete(K),k=Ve(r,n,C,de,ce,s,t);else if(re)m&&(k=Ve(r,n,C,de,ce,s,t));else{let D={...Q,defaultShouldRevalidate:U?!1:m};_t(ce,D)&&(k=Ve(r,n,C,de,ce,s,t,D))}k&&ue.push({key:K,routeId:G.routeId,path:G.path,matches:k,match:ce,request:C,controller:h})}),{dsMatches:oe,revalidatingFetchers:ue}}function Qt(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=t!=null&&e.id in t,a=r!=null&&r[e.id]!==void 0;return!n&&a?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!n&&!a}function io(e,t,r){let n=!t||r.route.id!==t.route.id,a=!e.hasOwnProperty(r.route.id);return n||a}function lo(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function _t(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function Jr(e,t,r,n,a,o){let l;if(e){let s=n[e];J(s,`No route found to patch children into: routeId = ${e}`),s.children||(s.children=[]),l=s.children}else l=r;let u=[],i=[];if(t.forEach(s=>{let d=l.find(m=>Sn(s,m));d?i.push({existingRoute:d,newRoute:s}):u.push(s)}),u.length>0){let s=ot(u,a,[e||"_","patch",String((l==null?void 0:l.length)||"0")],n);l.push(...s)}if(o&&i.length>0)for(let s=0;s<i.length;s++){let{existingRoute:d,newRoute:m}=i[s],p=d,[g]=ot([m],a,[],{},!0);Object.assign(p,{element:g.element?g.element:p.element,errorElement:g.errorElement?g.errorElement:p.errorElement,hydrateFallbackElement:g.hydrateFallbackElement?g.hydrateFallbackElement:p.hydrateFallbackElement})}}function Sn(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,n)=>{var a;return(a=t.children)==null?void 0:a.some(o=>Sn(r,o))}):!1}var Gr=new WeakMap,xn=({key:e,route:t,manifest:r,mapRouteProperties:n})=>{let a=r[t.id];if(J(a,"No route found in manifest"),!a.lazy||typeof a.lazy!="object")return;let o=a.lazy[e];if(!o)return;let l=Gr.get(a);l||(l={},Gr.set(a,l));let u=l[e];if(u)return u;let i=(async()=>{let s=Pa(e),m=a[e]!==void 0&&e!=="hasErrorBoundary";if(s)ae(!s,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),l[e]=Promise.resolve();else if(m)ae(!1,`Route "${a.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let p=await o();p!=null&&(Object.assign(a,{[e]:p}),Object.assign(a,n(a)))}typeof a.lazy=="object"&&(a.lazy[e]=void 0,Object.values(a.lazy).every(p=>p===void 0)&&(a.lazy=void 0))})();return l[e]=i,i},Xr=new WeakMap;function so(e,t,r,n,a){let o=r[e.id];if(J(o,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof e.lazy=="function"){let d=Xr.get(o);if(d)return{lazyRoutePromise:d,lazyHandlerPromise:d};let m=(async()=>{J(typeof e.lazy=="function","No lazy route function found");let p=await e.lazy(),g={};for(let R in p){let b=p[R];if(b===void 0)continue;let x=Ma(R),I=o[R]!==void 0&&R!=="hasErrorBoundary";x?ae(!x,"Route property "+R+" is not a supported property to be returned from a lazy route function. This property will be ignored."):I?ae(!I,`Route "${o.id}" has a static property "${R}" defined but its lazy function is also returning a value for this property. The lazy route property "${R}" will be ignored.`):g[R]=b}Object.assign(o,g),Object.assign(o,{...n(o),lazy:void 0})})();return Xr.set(o,m),m.catch(()=>{}),{lazyRoutePromise:m,lazyHandlerPromise:m}}let l=Object.keys(e.lazy),u=[],i;for(let d of l){if(a&&a.includes(d))continue;let m=xn({key:d,route:e,manifest:r,mapRouteProperties:n});m&&(u.push(m),d===t&&(i=m))}let s=u.length>0?Promise.all(u).then(()=>{}):void 0;return s==null||s.catch(()=>{}),i==null||i.catch(()=>{}),{lazyRoutePromise:s,lazyHandlerPromise:i}}async function Kr(e){let t=e.matches.filter(a=>a.shouldLoad),r={};return(await Promise.all(t.map(a=>a.resolve()))).forEach((a,o)=>{r[t[o].route.id]=a}),r}async function uo(e){return e.matches.some(t=>t.route.unstable_middleware)?Cn(e,!1,()=>Kr(e),(t,r)=>({[r]:{type:"error",result:t}})):Kr(e)}async function Cn(e,t,r,n){let{matches:a,request:o,params:l,context:u}=e,i={handlerResult:void 0};try{let s=a.flatMap(m=>m.route.unstable_middleware?m.route.unstable_middleware.map(p=>[m.route.id,p]):[]),d=await Ln({request:o,params:l,context:u},s,t,i,r);return t?d:i.handlerResult}catch(s){if(!i.middlewareError)throw s;let d=await n(i.middlewareError.error,i.middlewareError.routeId);return i.handlerResult?Object.assign(i.handlerResult,d):d}}async function Ln(e,t,r,n,a,o=0){let{request:l}=e;if(l.signal.aborted)throw l.signal.reason?l.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${l.method} ${l.url}`);let u=t[o];if(!u)return n.handlerResult=await a(),n.handlerResult;let[i,s]=u,d=!1,m,p=async()=>{if(d)throw new Error("You may only call `next()` once per middleware");d=!0,await Ln(e,t,r,n,a,o+1)};try{let g=await s({request:e.request,params:e.params,context:e.context},p);return d?g===void 0?m:g:p()}catch(g){throw n.middlewareError?n.middlewareError.error!==g&&(n.middlewareError={routeId:i,error:g}):n.middlewareError={routeId:i,error:g},g}}function Pn(e,t,r,n,a){let o=xn({key:"unstable_middleware",route:n.route,manifest:t,mapRouteProperties:e}),l=so(n.route,ve(r.method)?"action":"loader",t,e,a);return{middleware:o,route:l.lazyRoutePromise,handler:l.lazyHandlerPromise}}function Zt(e,t,r,n,a,o,l,u=null){let i=!1,s=Pn(e,t,r,n,a);return{...n,_lazyPromises:s,shouldLoad:l,unstable_shouldRevalidateArgs:u,unstable_shouldCallHandler(d){return i=!0,u?typeof d=="boolean"?_t(n,{...u,defaultShouldRevalidate:d}):_t(n,u):l},resolve(d){return i||l||d&&!ve(r.method)&&(n.route.lazy||n.route.loader)?fo({request:r,match:n,lazyHandlerPromise:s==null?void 0:s.handler,lazyRoutePromise:s==null?void 0:s.route,handlerOverride:d,scopedContext:o}):Promise.resolve({type:"data",result:void 0})}}}function Ve(e,t,r,n,a,o,l,u=null){return n.map(i=>i.route.id!==a.route.id?{...i,shouldLoad:!1,unstable_shouldRevalidateArgs:u,unstable_shouldCallHandler:()=>!1,_lazyPromises:Pn(e,t,r,i,o),resolve:()=>Promise.resolve({type:"data",result:void 0})}:Zt(e,t,r,i,o,l,!0,u))}async function co(e,t,r,n,a,o){r.some(s=>{var d;return(d=s._lazyPromises)==null?void 0:d.middleware})&&await Promise.all(r.map(s=>{var d;return(d=s._lazyPromises)==null?void 0:d.middleware}));let l={request:t,params:r[0].params,context:a,matches:r},i=await e({...l,fetcherKey:n,unstable_runClientMiddleware:s=>{let d=l;return Cn(d,!1,()=>s({...d,fetcherKey:n,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(m,p)=>({[p]:{type:"error",result:m}}))}});try{await Promise.all(r.flatMap(s=>{var d,m;return[(d=s._lazyPromises)==null?void 0:d.handler,(m=s._lazyPromises)==null?void 0:m.route]}))}catch{}return i}async function fo({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:n,handlerOverride:a,scopedContext:o}){let l,u,i=ve(e.method),s=i?"action":"loader",d=m=>{let p,g=new Promise((x,E)=>p=E);u=()=>p(),e.signal.addEventListener("abort",u);let R=x=>typeof m!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${s}" [routeId: ${t.route.id}]`)):m({request:e,params:t.params,context:o},...x!==void 0?[x]:[]),b=(async()=>{try{return{type:"data",result:await(a?a(E=>R(E)):R())}}catch(x){return{type:"error",result:x}}})();return Promise.race([b,g])};try{let m=i?t.route.action:t.route.loader;if(r||n)if(m){let p,[g]=await Promise.all([d(m).catch(R=>{p=R}),r,n]);if(p!==void 0)throw p;l=g}else{await r;let p=i?t.route.action:t.route.loader;if(p)[l]=await Promise.all([d(p),n]);else if(s==="action"){let g=new URL(e.url),R=g.pathname+g.search;throw Ee(405,{method:e.method,pathname:R,routeId:t.route.id})}else return{type:"data",result:void 0}}else if(m)l=await d(m);else{let p=new URL(e.url),g=p.pathname+p.search;throw Ee(404,{pathname:g})}}catch(m){return{type:"error",result:m}}finally{u&&e.signal.removeEventListener("abort",u)}return l}async function ho(e){var n,a,o,l,u,i;let{result:t,type:r}=e;if(dr(t)){let s;try{let d=t.headers.get("Content-Type");d&&/\bapplication\/json\b/.test(d)?t.body==null?s=null:s=await t.json():s=await t.text()}catch(d){return{type:"error",error:d}}return r==="error"?{type:"error",error:new Ue(t.status,t.statusText,s),statusCode:t.status,headers:t.headers}:{type:"data",data:s,statusCode:t.status,headers:t.headers}}return r==="error"?tr(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:(n=t.init)==null?void 0:n.status,headers:(a=t.init)!=null&&a.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new Ue(((o=t.init)==null?void 0:o.status)||500,void 0,t.data),statusCode:ze(t)?t.status:void 0,headers:(l=t.init)!=null&&l.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:ze(t)?t.status:void 0}:tr(t)?{type:"data",data:t.data,statusCode:(u=t.init)==null?void 0:u.status,headers:(i=t.init)!=null&&i.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function mo(e,t,r,n,a){let o=e.headers.get("Location");if(J(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!cr(o)){let l=n.slice(0,n.findIndex(u=>u.route.id===r)+1);o=qt(new URL(t.url),l,a,o),e.headers.set("Location",o)}return e}function qr(e,t,r){if(cr(e)){let n=e,a=n.startsWith("//")?new URL(t.protocol+n):new URL(n),o=we(a.pathname,r)!=null;if(a.origin===t.origin&&o)return a.pathname+a.search+a.hash}return e}function Ye(e,t,r,n){let a=e.createURL(Tn(t)).toString(),o={signal:r};if(n&&ve(n.formMethod)){let{formMethod:l,formEncType:u}=n;o.method=l.toUpperCase(),u==="application/json"?(o.headers=new Headers({"Content-Type":u}),o.body=JSON.stringify(n.json)):u==="text/plain"?o.body=n.text:u==="application/x-www-form-urlencoded"&&n.formData?o.body=er(n.formData):o.body=n.formData}return new Request(a,o)}function er(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,typeof n=="string"?n:n.name);return t}function Qr(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function po(e,t,r,n=!1,a=!1){let o={},l=null,u,i=!1,s={},d=r&&ge(r[1])?r[1].error:void 0;return e.forEach(m=>{if(!(m.route.id in t))return;let p=m.route.id,g=t[p];if(J(!He(g),"Cannot handle redirect results in processLoaderData"),ge(g)){let R=g.error;if(d!==void 0&&(R=d,d=void 0),l=l||{},a)l[p]=R;else{let b=je(e,p);l[b.route.id]==null&&(l[b.route.id]=R)}n||(o[p]=bn),i||(i=!0,u=ze(g.error)?g.error.status:500),g.headers&&(s[p]=g.headers)}else o[p]=g.data,g.statusCode&&g.statusCode!==200&&!i&&(u=g.statusCode),g.headers&&(s[p]=g.headers)}),d!==void 0&&r&&(l={[r[0]]:d},r[2]&&(o[r[2]]=void 0)),{loaderData:o,errors:l,statusCode:u||200,loaderHeaders:s}}function Zr(e,t,r,n,a,o){let{loaderData:l,errors:u}=po(t,r,n);return a.filter(i=>!i.matches||i.matches.some(s=>s.shouldLoad)).forEach(i=>{let{key:s,match:d,controller:m}=i,p=o[s];if(J(p,"Did not find corresponding fetcher result"),!(m&&m.signal.aborted))if(ge(p)){let g=je(e.matches,d==null?void 0:d.route.id);u&&u[g.route.id]||(u={...u,[g.route.id]:p.error}),e.fetchers.delete(s)}else if(He(p))J(!1,"Unhandled fetcher revalidation redirect");else{let g=Oe(p.data);e.fetchers.set(s,g)}}),{loaderData:l,errors:u}}function en(e,t,r,n){let a=Object.entries(t).filter(([,o])=>o!==bn).reduce((o,[l,u])=>(o[l]=u,o),{});for(let o of r){let l=o.route.id;if(!t.hasOwnProperty(l)&&e.hasOwnProperty(l)&&o.route.loader&&(a[l]=e[l]),n&&n.hasOwnProperty(l))break}return a}function tn(e){return e?ge(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function je(e,t){return(t?e.slice(0,e.findIndex(n=>n.route.id===t)+1):[...e]).reverse().find(n=>n.route.hasErrorBoundary===!0)||e[0]}function rn(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function Ee(e,{pathname:t,routeId:r,method:n,type:a,message:o}={}){let l="Unknown Server Error",u="Unknown @remix-run/router error";return e===400?(l="Bad Request",n&&t&&r?u=`You made a ${n} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:a==="invalid-body"&&(u="Unable to encode submission body")):e===403?(l="Forbidden",u=`Route "${r}" does not match URL "${t}"`):e===404?(l="Not Found",u=`No route matches URL "${t}"`):e===405&&(l="Method Not Allowed",n&&t&&r?u=`You made a ${n.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:n&&(u=`Invalid request method "${n.toUpperCase()}"`)),new Ue(e||500,l,new Error(u),!0)}function gt(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[n,a]=t[r];if(He(a))return{key:n,result:a}}}function Tn(e){let t=typeof e=="string"?ke(e):e;return Ae({...t,hash:""})}function yo(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function vo(e){return dr(e.result)&&eo.has(e.result.status)}function ge(e){return e.type==="error"}function He(e){return(e&&e.type)==="redirect"}function tr(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function dr(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function go(e){return Za.has(e.toUpperCase())}function ve(e){return qa.has(e.toUpperCase())}function fr(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function xt(e,t){let r=typeof t=="string"?ke(t).search:t.search;if(e[e.length-1].route.index&&fr(r||""))return e[e.length-1];let n=wn(e);return n[n.length-1]}function nn(e){let{formMethod:t,formAction:r,formEncType:n,text:a,formData:o,json:l}=e;if(!(!t||!r||!n)){if(a!=null)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:a};if(o!=null)return{formMethod:t,formAction:r,formEncType:n,formData:o,json:void 0,text:void 0};if(l!==void 0)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:l,text:void 0}}}function Vt(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function wo(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function tt(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function Eo(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function Oe(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function Ro(e,t){try{let r=e.sessionStorage.getItem(Rn);if(r){let n=JSON.parse(r);for(let[a,o]of Object.entries(n||{}))o&&Array.isArray(o)&&t.set(a,new Set(o||[]))}}catch{}}function bo(e,t){if(t.size>0){let r={};for(let[n,a]of t)r[n]=[...a];try{e.sessionStorage.setItem(Rn,JSON.stringify(r))}catch(n){ae(!1,`Failed to save applied view transitions in sessionStorage (${n}).`)}}}function So(){let e,t,r=new Promise((n,a)=>{e=async o=>{n(o);try{await r}catch{}},t=async o=>{a(o);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var Be=f.createContext(null);Be.displayName="DataRouter";var Je=f.createContext(null);Je.displayName="DataRouterState";var xo=f.createContext(!1);function Co(){return f.useContext(xo)}var hr=f.createContext({isTransitioning:!1});hr.displayName="ViewTransition";var Mn=f.createContext(new Map);Mn.displayName="Fetchers";var Lo=f.createContext(null);Lo.displayName="Await";var Re=f.createContext(null);Re.displayName="Navigation";var Ot=f.createContext(null);Ot.displayName="Location";var be=f.createContext({outlet:null,matches:[],isDataRoute:!1});be.displayName="Route";var mr=f.createContext(null);mr.displayName="RouteError";function Po(e,{relative:t}={}){J(lt(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:n}=f.useContext(Re),{hash:a,pathname:o,search:l}=st(e,{relative:t}),u=o;return r!=="/"&&(u=o==="/"?r:Ce([r,o])),n.createHref({pathname:u,search:l,hash:a})}function lt(){return f.useContext(Ot)!=null}function Le(){return J(lt(),"useLocation() may be used only in the context of a <Router> component."),f.useContext(Ot).location}var _n="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Dn(e){f.useContext(Re).static||f.useLayoutEffect(e)}function To(){let{isDataRoute:e}=f.useContext(be);return e?zo():Mo()}function Mo(){J(lt(),"useNavigate() may be used only in the context of a <Router> component.");let e=f.useContext(Be),{basename:t,navigator:r}=f.useContext(Re),{matches:n}=f.useContext(be),{pathname:a}=Le(),o=JSON.stringify(sr(n)),l=f.useRef(!1);return Dn(()=>{l.current=!0}),f.useCallback((i,s={})=>{if(ae(l.current,_n),!l.current)return;if(typeof i=="number"){r.go(i);return}let d=ur(i,JSON.parse(o),a,s.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:Ce([t,d.pathname])),(s.replace?r.replace:r.push)(d,s.state,s)},[t,r,o,a,e])}var _o=f.createContext(null);function Do(e){let t=f.useContext(be).outlet;return t&&f.createElement(_o.Provider,{value:e},t)}function On(){let{matches:e}=f.useContext(be),t=e[e.length-1];return t?t.params:{}}function st(e,{relative:t}={}){let{matches:r}=f.useContext(be),{pathname:n}=Le(),a=JSON.stringify(sr(r));return f.useMemo(()=>ur(e,JSON.parse(a),n,t==="path"),[e,a,n,t])}function Oo(e,t,r,n){J(lt(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a}=f.useContext(Re),{matches:o}=f.useContext(be),l=o[o.length-1],u=l?l.params:{},i=l?l.pathname:"/",s=l?l.pathnameBase:"/",d=l&&l.route;{let E=d&&d.path||"";Nn(i,!d||E.endsWith("*")||E.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${i}" (under <Route path="${E}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${E}"> to <Route path="${E==="/"?"*":`${E}/*`}">.`)}let m=Le(),p;p=m;let g=p.pathname||"/",R=g;if(s!=="/"){let E=s.replace(/^\//,"").split("/");R="/"+g.replace(/^\//,"").split("/").slice(E.length).join("/")}let b=Te(e,{pathname:R});return ae(d||b!=null,`No routes matched location "${p.pathname}${p.search}${p.hash}" `),ae(b==null||b[b.length-1].route.element!==void 0||b[b.length-1].route.Component!==void 0||b[b.length-1].route.lazy!==void 0,`Matched leaf route at location "${p.pathname}${p.search}${p.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),Io(b&&b.map(E=>Object.assign({},E,{params:Object.assign({},u,E.params),pathname:Ce([s,a.encodeLocation?a.encodeLocation(E.pathname).pathname:E.pathname]),pathnameBase:E.pathnameBase==="/"?s:Ce([s,a.encodeLocation?a.encodeLocation(E.pathnameBase).pathname:E.pathnameBase])})),o,r,n)}function Ao(){let e=vr(),t=ze(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:n},o={padding:"2px 4px",backgroundColor:n},l=null;return console.error("Error handled by React Router default ErrorBoundary:",e),l=f.createElement(f.Fragment,null,f.createElement("p",null,"💿 Hey developer 👋"),f.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",f.createElement("code",{style:o},"ErrorBoundary")," or"," ",f.createElement("code",{style:o},"errorElement")," prop on your route.")),f.createElement(f.Fragment,null,f.createElement("h2",null,"Unexpected Application Error!"),f.createElement("h3",{style:{fontStyle:"italic"}},t),r?f.createElement("pre",{style:a},r):null,l)}var ko=f.createElement(Ao,null),No=class extends f.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?f.createElement(be.Provider,{value:this.props.routeContext},f.createElement(mr.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function $o({routeContext:e,match:t,children:r}){let n=f.useContext(Be);return n&&n.static&&n.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=t.route.id),f.createElement(be.Provider,{value:e},r)}function Io(e,t=[],r=null,n=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let a=e,o=r==null?void 0:r.errors;if(o!=null){let i=a.findIndex(s=>s.route.id&&(o==null?void 0:o[s.route.id])!==void 0);J(i>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),a=a.slice(0,Math.min(a.length,i+1))}let l=!1,u=-1;if(r)for(let i=0;i<a.length;i++){let s=a[i];if((s.route.HydrateFallback||s.route.hydrateFallbackElement)&&(u=i),s.route.id){let{loaderData:d,errors:m}=r,p=s.route.loader&&!d.hasOwnProperty(s.route.id)&&(!m||m[s.route.id]===void 0);if(s.route.lazy||p){l=!0,u>=0?a=a.slice(0,u+1):a=[a[0]];break}}}return a.reduceRight((i,s,d)=>{let m,p=!1,g=null,R=null;r&&(m=o&&s.route.id?o[s.route.id]:void 0,g=s.route.errorElement||ko,l&&(u<0&&d===0?(Nn("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),p=!0,R=null):u===d&&(p=!0,R=s.route.hydrateFallbackElement||null)));let b=t.concat(a.slice(0,d+1)),x=()=>{let E;return m?E=g:p?E=R:s.route.Component?E=f.createElement(s.route.Component,null):s.route.element?E=s.route.element:E=i,f.createElement($o,{match:s,routeContext:{outlet:i,matches:b,isDataRoute:r!=null},children:E})};return r&&(s.route.ErrorBoundary||s.route.errorElement||d===0)?f.createElement(No,{location:r.location,revalidation:r.revalidation,component:g,error:m,children:x(),routeContext:{outlet:null,matches:b,isDataRoute:!0}}):x()},null)}function pr(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Fo(e){let t=f.useContext(Be);return J(t,pr(e)),t}function ut(e){let t=f.useContext(Je);return J(t,pr(e)),t}function jo(e){let t=f.useContext(be);return J(t,pr(e)),t}function ct(e){let t=jo(e),r=t.matches[t.matches.length-1];return J(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function Ho(){return ct("useRouteId")}function Uo(){return ut("useNavigation").navigation}function yr(){let{matches:e,loaderData:t}=ut("useMatches");return f.useMemo(()=>e.map(r=>pn(r,t)),[e,t])}function An(){let e=ut("useLoaderData"),t=ct("useLoaderData");return e.loaderData[t]}function kn(){let e=ut("useActionData"),t=ct("useLoaderData");return e.actionData?e.actionData[t]:void 0}function vr(){var n;let e=f.useContext(mr),t=ut("useRouteError"),r=ct("useRouteError");return e!==void 0?e:(n=t.errors)==null?void 0:n[r]}function zo(){let{router:e}=Fo("useNavigate"),t=ct("useNavigate"),r=f.useRef(!1);return Dn(()=>{r.current=!0}),f.useCallback(async(a,o={})=>{ae(r.current,_n),r.current&&(typeof a=="number"?e.navigate(a):await e.navigate(a,{fromRouteId:t,...o}))},[e,t])}var an={};function Nn(e,t,r){!t&&!an[e]&&(an[e]=!0,ae(!1,r))}var on={};function rr(e,t){!e&&!on[t]&&(on[t]=!0,console.warn(t))}function bl(e){let t={hasErrorBoundary:e.hasErrorBoundary||e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&ae(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:f.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&ae(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:f.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&ae(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:f.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var Sl=["HydrateFallback","hydrateFallbackElement"],Bo=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",e(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",t(r))}})}};function xl({router:e,flushSync:t}){let[r,n]=f.useState(e.state),[a,o]=f.useState(),[l,u]=f.useState({isTransitioning:!1}),[i,s]=f.useState(),[d,m]=f.useState(),[p,g]=f.useState(),R=f.useRef(new Map),b=f.useCallback((T,{deletedFetchers:_,flushSync:S,viewTransitionOpts:v})=>{T.fetchers.forEach((z,U)=>{z.data!==void 0&&R.current.set(U,z.data)}),_.forEach(z=>R.current.delete(z)),rr(S===!1||t!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let M=e.window!=null&&e.window.document!=null&&typeof e.window.document.startViewTransition=="function";if(rr(v==null||M,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!v||!M){t&&S?t(()=>n(T)):f.startTransition(()=>n(T));return}if(t&&S){t(()=>{d&&(i&&i.resolve(),d.skipTransition()),u({isTransitioning:!0,flushSync:!0,currentLocation:v.currentLocation,nextLocation:v.nextLocation})});let z=e.window.document.startViewTransition(()=>{t(()=>n(T))});z.finished.finally(()=>{t(()=>{s(void 0),m(void 0),o(void 0),u({isTransitioning:!1})})}),t(()=>m(z));return}d?(i&&i.resolve(),d.skipTransition(),g({state:T,currentLocation:v.currentLocation,nextLocation:v.nextLocation})):(o(T),u({isTransitioning:!0,flushSync:!1,currentLocation:v.currentLocation,nextLocation:v.nextLocation}))},[e.window,t,d,i]);f.useLayoutEffect(()=>e.subscribe(b),[e,b]),f.useEffect(()=>{l.isTransitioning&&!l.flushSync&&s(new Bo)},[l]),f.useEffect(()=>{if(i&&a&&e.window){let T=a,_=i.promise,S=e.window.document.startViewTransition(async()=>{f.startTransition(()=>n(T)),await _});S.finished.finally(()=>{s(void 0),m(void 0),o(void 0),u({isTransitioning:!1})}),m(S)}},[a,i,e.window]),f.useEffect(()=>{i&&a&&r.location.key===a.location.key&&i.resolve()},[i,d,r.location,a]),f.useEffect(()=>{!l.isTransitioning&&p&&(o(p.state),u({isTransitioning:!0,flushSync:!1,currentLocation:p.currentLocation,nextLocation:p.nextLocation}),g(void 0))},[l.isTransitioning,p]);let x=f.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:T=>e.navigate(T),push:(T,_,S)=>e.navigate(T,{state:_,preventScrollReset:S==null?void 0:S.preventScrollReset}),replace:(T,_,S)=>e.navigate(T,{replace:!0,state:_,preventScrollReset:S==null?void 0:S.preventScrollReset})}),[e]),E=e.basename||"/",I=f.useMemo(()=>({router:e,navigator:x,static:!1,basename:E}),[e,x,E]);return f.createElement(f.Fragment,null,f.createElement(Be.Provider,{value:I},f.createElement(Je.Provider,{value:r},f.createElement(Mn.Provider,{value:R.current},f.createElement(hr.Provider,{value:l},f.createElement(Vo,{basename:E,location:r.location,navigationType:r.historyAction,navigator:x},f.createElement(Wo,{routes:e.routes,future:e.future,state:r})))))),null)}var Wo=f.memo(Yo);function Yo({routes:e,future:t,state:r}){return Oo(e,void 0,r,t)}function Cl(e){return Do(e.context)}function Vo({basename:e="/",children:t=null,location:r,navigationType:n="POP",navigator:a,static:o=!1}){J(!lt(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=e.replace(/^\/*/,"/"),u=f.useMemo(()=>({basename:l,navigator:a,static:o,future:{}}),[l,a,o]);typeof r=="string"&&(r=ke(r));let{pathname:i="/",search:s="",hash:d="",state:m=null,key:p="default"}=r,g=f.useMemo(()=>{let R=we(i,l);return R==null?null:{location:{pathname:R,search:s,hash:d,state:m,key:p},navigationType:n}},[l,i,s,d,m,p,n]);return ae(g!=null,`<Router basename="${l}"> is not able to match the URL "${i}${s}${d}" because it does not start with the basename, so the <Router> won't render anything.`),g==null?null:f.createElement(Re.Provider,{value:u},f.createElement(Ot.Provider,{children:t,value:g}))}function Jo(){return{params:On(),loaderData:An(),actionData:kn(),matches:yr()}}function Ll(e){return function(){const r=Jo();return f.createElement(e,r)}}function Go(){return{params:On(),loaderData:An(),actionData:kn(),error:vr()}}function Pl(e){return function(){const r=Go();return f.createElement(e,r)}}var Ct="get",Lt="application/x-www-form-urlencoded";function At(e){return e!=null&&typeof e.tagName=="string"}function Xo(e){return At(e)&&e.tagName.toLowerCase()==="button"}function Ko(e){return At(e)&&e.tagName.toLowerCase()==="form"}function qo(e){return At(e)&&e.tagName.toLowerCase()==="input"}function Qo(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Zo(e,t){return e.button===0&&(!t||t==="_self")&&!Qo(e)}var wt=null;function ei(){if(wt===null)try{new FormData(document.createElement("form"),0),wt=!1}catch{wt=!0}return wt}var ti=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Jt(e){return e!=null&&!ti.has(e)?(ae(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Lt}"`),null):e}function ri(e,t){let r,n,a,o,l;if(Ko(e)){let u=e.getAttribute("action");n=u?we(u,t):null,r=e.getAttribute("method")||Ct,a=Jt(e.getAttribute("enctype"))||Lt,o=new FormData(e)}else if(Xo(e)||qo(e)&&(e.type==="submit"||e.type==="image")){let u=e.form;if(u==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let i=e.getAttribute("formaction")||u.getAttribute("action");if(n=i?we(i,t):null,r=e.getAttribute("formmethod")||u.getAttribute("method")||Ct,a=Jt(e.getAttribute("formenctype"))||Jt(u.getAttribute("enctype"))||Lt,o=new FormData(u,e),!ei()){let{name:s,type:d,value:m}=e;if(d==="image"){let p=s?`${s}.`:"";o.append(`${p}x`,"0"),o.append(`${p}y`,"0")}else s&&o.append(s,m)}}else{if(At(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=Ct,n=null,a=Lt,l=e}return o&&a==="text/plain"&&(l=o,o=void 0),{action:n,method:r.toLowerCase(),encType:a,formData:o,body:l}}var ni=-1,ai=-2,oi=-3,ii=-4,li=-5,si=-6,ui=-7,ci="B",di="D",$n="E",fi="M",hi="N",In="P",mi="R",pi="S",yi="Y",vi="U",gi="Z",Fn=class{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}};function wi(){const e=new TextDecoder;let t="";return new TransformStream({transform(r,n){const a=e.decode(r,{stream:!0}),o=(t+a).split(`
`);t=o.pop()||"";for(const l of o)n.enqueue(l)},flush(r){t&&r.enqueue(t)}})}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Gt=typeof window<"u"?window:typeof globalThis<"u"?globalThis:void 0;function nr(e){const{hydrated:t,values:r}=this;if(typeof e=="number")return ln.call(this,e);if(!Array.isArray(e)||!e.length)throw new SyntaxError;const n=r.length;for(const a of e)r.push(a);return t.length=r.length,ln.call(this,n)}function ln(e){const{hydrated:t,values:r,deferred:n,plugins:a}=this;let o;const l=[[e,i=>{o=i}]];let u=[];for(;l.length>0;){const[i,s]=l.pop();switch(i){case ui:s(void 0);continue;case li:s(null);continue;case ai:s(NaN);continue;case si:s(1/0);continue;case oi:s(-1/0);continue;case ii:s(-0);continue}if(t[i]){s(t[i]);continue}const d=r[i];if(!d||typeof d!="object"){t[i]=d,s(d);continue}if(Array.isArray(d))if(typeof d[0]=="string"){const[m,p,g]=d;switch(m){case di:s(t[i]=new Date(p));continue;case vi:s(t[i]=new URL(p));continue;case ci:s(t[i]=BigInt(p));continue;case mi:s(t[i]=new RegExp(p,g));continue;case yi:s(t[i]=Symbol.for(p));continue;case pi:const R=new Set;t[i]=R;for(let _=d.length-1;_>0;_--)l.push([d[_],S=>{R.add(S)}]);s(R);continue;case fi:const b=new Map;t[i]=b;for(let _=d.length-2;_>0;_-=2){const S=[];l.push([d[_+1],v=>{S[1]=v}]),l.push([d[_],v=>{S[0]=v}]),u.push(()=>{b.set(S[0],S[1])})}s(b);continue;case hi:const x=Object.create(null);t[i]=x;for(const _ of Object.keys(p).reverse()){const S=[];l.push([p[_],v=>{S[1]=v}]),l.push([Number(_.slice(1)),v=>{S[0]=v}]),u.push(()=>{x[S[0]]=S[1]})}s(x);continue;case In:if(t[p])s(t[i]=t[p]);else{const _=new Fn;n[p]=_,s(t[i]=_.promise)}continue;case $n:const[,E,I]=d;let T=I&&Gt&&Gt[I]?new Gt[I](E):new Error(E);t[i]=T,s(T);continue;case gi:s(t[i]=t[p]);continue;default:if(Array.isArray(a)){const _=[],S=d.slice(1);for(let v=0;v<S.length;v++){const M=S[v];l.push([M,z=>{_[v]=z}])}u.push(()=>{for(const v of a){const M=v(d[0],..._);if(M){s(t[i]=M.value);return}}throw new SyntaxError});continue}throw new SyntaxError}}else{const m=[];t[i]=m;for(let p=0;p<d.length;p++){const g=d[p];g!==ni&&l.push([g,R=>{m[p]=R}])}s(m);continue}else{const m={};t[i]=m;for(const p of Object.keys(d).reverse()){const g=[];l.push([d[p],R=>{g[1]=R}]),l.push([Number(p.slice(1)),R=>{g[0]=R}]),u.push(()=>{m[g[0]]=g[1]})}s(m);continue}}for(;u.length>0;)u.pop()();return o}async function Ei(e,t){const{plugins:r}=t??{},n=new Fn,a=e.pipeThrough(wi()).getReader(),o={values:[],hydrated:[],deferred:{},plugins:r},l=await Ri.call(o,a);let u=n.promise;return l.done?n.resolve():u=bi.call(o,a).then(n.resolve).catch(i=>{for(const s of Object.values(o.deferred))s.reject(i);n.reject(i)}),{done:u.then(()=>a.closed),value:l.value}}async function Ri(e){const t=await e.read();if(!t.value)throw new SyntaxError;let r;try{r=JSON.parse(t.value)}catch{throw new SyntaxError}return{done:t.done,value:nr.call(this,r)}}async function bi(e){let t=await e.read();for(;!t.done;){if(!t.value)continue;const r=t.value;switch(r[0]){case In:{const n=r.indexOf(":"),a=Number(r.slice(1,n)),o=this.deferred[a];if(!o)throw new Error(`Deferred ID ${a} not found in stream`);const l=r.slice(n+1);let u;try{u=JSON.parse(l)}catch{throw new SyntaxError}const i=nr.call(this,u);o.resolve(i);break}case $n:{const n=r.indexOf(":"),a=Number(r.slice(1,n)),o=this.deferred[a];if(!o)throw new Error(`Deferred ID ${a} not found in stream`);const l=r.slice(n+1);let u;try{u=JSON.parse(l)}catch{throw new SyntaxError}const i=nr.call(this,u);o.reject(i);break}default:throw new SyntaxError}t=await e.read()}}async function Si(e){let t={signal:e.signal};if(e.method!=="GET"){t.method=e.method;let r=e.headers.get("Content-Type");r&&/\bapplication\/json\b/.test(r)?(t.headers={"Content-Type":r},t.body=JSON.stringify(await e.json())):r&&/\btext\/plain\b/.test(r)?(t.headers={"Content-Type":r},t.body=await e.text()):r&&/\bapplication\/x-www-form-urlencoded\b/.test(r)?t.body=new URLSearchParams(await e.text()):t.body=await e.formData()}return t}function sn(e){return{__html:e}}function ye(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}var ar=Symbol("SingleFetchRedirect"),jn=class extends Error{},xi=202,Ci=new Set([100,101,204,205]);function Tl(e,t,r,n,a){let o=Li(e,l=>{let u=t.routes[l.route.id];ye(u,"Route not found in manifest");let i=r[l.route.id];return{hasLoader:u.hasLoader,hasClientLoader:u.hasClientLoader,hasShouldRevalidate:!!(i!=null&&i.shouldRevalidate)}},Ai,n,a);return async l=>l.unstable_runClientMiddleware(o)}function Li(e,t,r,n,a,o=()=>!0){return async l=>{let{request:u,matches:i,fetcherKey:s}=l,d=e();if(u.method!=="GET")return Pi(l,r,a);let m=i.some(p=>{let{hasLoader:g,hasClientLoader:R}=t(p);return p.unstable_shouldCallHandler()&&g&&!R});return!n&&!m?Ti(l,t,r,a):s?Di(l,r,a):Mi(l,d,t,r,n,a,o)}}async function Pi(e,t,r){let n=e.matches.find(l=>l.unstable_shouldCallHandler());ye(n,"No action match found");let a,o=await n.resolve(async l=>await l(async()=>{let{data:i,status:s}=await t(e,r,[n.route.id]);return a=s,it(i,n.route.id)}));return dr(o.result)||ze(o.result)||tr(o.result)?{[n.route.id]:o}:{[n.route.id]:{type:o.type,result:Xa(o.result,a)}}}async function Ti(e,t,r,n){let a=e.matches.filter(l=>l.unstable_shouldCallHandler()),o={};return await Promise.all(a.map(l=>l.resolve(async u=>{try{let{hasClientLoader:i}=t(l),s=l.route.id,d=i?await u(async()=>{let{data:m}=await r(e,n,[s]);return it(m,s)}):await u();o[l.route.id]={type:"data",result:d}}catch(i){o[l.route.id]={type:"error",result:i}}}))),o}async function Mi(e,t,r,n,a,o,l=()=>!0){let u=new Set,i=!1,s=e.matches.map(()=>un()),d=un(),m={},p=Promise.all(e.matches.map(async(R,b)=>R.resolve(async x=>{s[b].resolve();let E=R.route.id,{hasLoader:I,hasClientLoader:T,hasShouldRevalidate:_}=r(R),S=!R.unstable_shouldRevalidateArgs||R.unstable_shouldRevalidateArgs.actionStatus==null||R.unstable_shouldRevalidateArgs.actionStatus<400;if(!R.unstable_shouldCallHandler(S)){i||(i=R.unstable_shouldRevalidateArgs!=null&&I&&_===!0);return}if(l(R)&&T){I&&(i=!0);try{let M=await x(async()=>{let{data:z}=await n(e,o,[E]);return it(z,E)});m[E]={type:"data",result:M}}catch(M){m[E]={type:"error",result:M}}return}I&&u.add(E);try{let M=await x(async()=>{let z=await d.promise;return it(z,E)});m[E]={type:"data",result:M}}catch(M){m[E]={type:"error",result:M}}})));if(await Promise.all(s.map(R=>R.promise)),(!t.state.initialized&&t.state.navigation.state==="idle"||u.size===0)&&!window.__reactRouterHdrActive)d.resolve({routes:{}});else{let R=a&&i&&u.size>0?[...u.keys()]:void 0;try{let b=await n(e,o,R);d.resolve(b.data)}catch(b){d.reject(b)}}return await p,await _i(d.promise,e.matches,u,m),m}async function _i(e,t,r,n){try{let a,o=await e;if("routes"in o){for(let l of t)if(l.route.id in o.routes){let u=o.routes[l.route.id];if("error"in u){a=u.error;break}}}a!==void 0&&Array.from(r.values()).forEach(l=>{n[l].result instanceof jn&&(n[l].result=a)})}catch{}}async function Di(e,t,r){let n=e.matches.find(l=>l.unstable_shouldCallHandler());ye(n,"No fetcher match found");let a=n.route.id,o=await n.resolve(async l=>l(async()=>{let{data:u}=await t(e,r,[a]);return it(u,a)}));return{[n.route.id]:o}}function Oi(e){let t=e.searchParams.getAll("index");e.searchParams.delete("index");let r=[];for(let n of t)n&&r.push(n);for(let n of r)e.searchParams.append("index",n);return e}function Hn(e,t,r){let n=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return n.pathname==="/"?n.pathname=`_root.${r}`:t&&we(n.pathname,t)==="/"?n.pathname=`${t.replace(/\/$/,"")}/_root.${r}`:n.pathname=`${n.pathname.replace(/\/$/,"")}.${r}`,n}async function Ai(e,t,r){let{request:n}=e,a=Hn(n.url,t,"data");n.method==="GET"&&(a=Oi(a),r&&a.searchParams.set("_routes",r.join(",")));let o=await fetch(a,await Si(n));if(o.status===404&&!o.headers.has("X-Remix-Response"))throw new Ue(404,"Not Found",!0);if(o.status===204&&o.headers.has("X-Remix-Redirect"))return{status:xi,data:{redirect:{redirect:o.headers.get("X-Remix-Redirect"),status:Number(o.headers.get("X-Remix-Status")||"302"),revalidate:o.headers.get("X-Remix-Revalidate")==="true",reload:o.headers.get("X-Remix-Reload-Document")==="true",replace:o.headers.get("X-Remix-Replace")==="true"}}};if(Ci.has(o.status)){let l={};return r&&n.method!=="GET"&&(l[r[0]]={data:void 0}),{status:o.status,data:{routes:l}}}ye(o.body,"No response body to decode");try{let l=await ki(o.body,window),u;if(n.method==="GET"){let i=l.value;ar in i?u={redirect:i[ar]}:u={routes:i}}else{let i=l.value,s=r==null?void 0:r[0];ye(s,"No routeId found for single fetch call decoding"),"redirect"in i?u={redirect:i}:u={routes:{[s]:i}}}return{status:o.status,data:u}}catch{throw new Error("Unable to decode turbo-stream response")}}function ki(e,t){return Ei(e,{plugins:[(r,...n)=>{if(r==="SanitizedError"){let[a,o,l]=n,u=Error;a&&a in t&&typeof t[a]=="function"&&(u=t[a]);let i=new u(o);return i.stack=l,{value:i}}if(r==="ErrorResponse"){let[a,o,l]=n;return{value:new Ue(o,l,a)}}if(r==="SingleFetchRedirect")return{value:{[ar]:n[0]}};if(r==="SingleFetchClassInstance")return{value:n[0]};if(r==="SingleFetchFallback")return{value:void 0}}]})}function it(e,t){if("redirect"in e){let{redirect:n,revalidate:a,reload:o,replace:l,status:u}=e.redirect;throw Ka(n,{status:u,headers:{...a?{"X-Remix-Revalidate":"yes"}:null,...o?{"X-Remix-Reload-Document":"yes"}:null,...l?{"X-Remix-Replace":"yes"}:null}})}let r=e.routes[t];if(r==null)throw new jn(`No result found for routeId "${t}"`);if("error"in r)throw r.error;if("data"in r)return r.data;throw new Error(`Invalid response found for routeId "${t}"`)}function un(){let e,t,r=new Promise((n,a)=>{e=async o=>{n(o);try{await r}catch{}},t=async o=>{a(o);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}async function Un(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Ni(e,t,r){let n=e.map(o=>{var i;let l=t[o.route.id],u=r.routes[o.route.id];return[u&&u.css?u.css.map(s=>({rel:"stylesheet",href:s})):[],((i=l==null?void 0:l.links)==null?void 0:i.call(l))||[]]}).flat(2),a=wr(e,r);return Yn(n,a)}function zn(e){return e.css?e.css.map(t=>({rel:"stylesheet",href:t})):[]}async function $i(e){if(!e.css)return;let t=zn(e);await Promise.all(t.map(Wn))}async function Bn(e,t){if(!e.css&&!t.links||!Ui())return;let r=[];if(e.css&&r.push(...zn(e)),t.links&&r.push(...t.links()),r.length===0)return;let n=[];for(let a of r)!gr(a)&&a.rel==="stylesheet"&&n.push({...a,rel:"preload",as:"style"});await Promise.all(n.map(Wn))}async function Wn(e){return new Promise(t=>{if(e.media&&!window.matchMedia(e.media).matches||document.querySelector(`link[rel="stylesheet"][href="${e.href}"]`))return t();let r=document.createElement("link");Object.assign(r,e);function n(){document.head.contains(r)&&document.head.removeChild(r)}r.onload=()=>{n(),t()},r.onerror=()=>{n(),t()},document.head.appendChild(r)})}function gr(e){return e!=null&&typeof e.page=="string"}function Ii(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Fi(e,t,r){let n=await Promise.all(e.map(async a=>{let o=t.routes[a.route.id];if(o){let l=await Un(o,r);return l.links?l.links():[]}return[]}));return Yn(n.flat(1).filter(Ii).filter(a=>a.rel==="stylesheet"||a.rel==="preload").map(a=>a.rel==="stylesheet"?{...a,rel:"prefetch",as:"style"}:{...a,rel:"prefetch"}))}function cn(e,t,r,n,a,o){let l=(i,s)=>r[s]?i.route.id!==r[s].route.id:!0,u=(i,s)=>{var d;return r[s].pathname!==i.pathname||((d=r[s].route.path)==null?void 0:d.endsWith("*"))&&r[s].params["*"]!==i.params["*"]};return o==="assets"?t.filter((i,s)=>l(i,s)||u(i,s)):o==="data"?t.filter((i,s)=>{var m;let d=n.routes[i.route.id];if(!d||!d.hasLoader)return!1;if(l(i,s)||u(i,s))return!0;if(i.route.shouldRevalidate){let p=i.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:((m=r[0])==null?void 0:m.params)||{},nextUrl:new URL(e,window.origin),nextParams:i.params,defaultShouldRevalidate:!0});if(typeof p=="boolean")return p}return!0}):[]}function wr(e,t,{includeHydrateFallback:r}={}){return ji(e.map(n=>{let a=t.routes[n.route.id];if(!a)return[];let o=[a.module];return a.clientActionModule&&(o=o.concat(a.clientActionModule)),a.clientLoaderModule&&(o=o.concat(a.clientLoaderModule)),r&&a.hydrateFallbackModule&&(o=o.concat(a.hydrateFallbackModule)),a.imports&&(o=o.concat(a.imports)),o}).flat(1))}function ji(e){return[...new Set(e)]}function Hi(e){let t={},r=Object.keys(e).sort();for(let n of r)t[n]=e[n];return t}function Yn(e,t){let r=new Set,n=new Set(t);return e.reduce((a,o)=>{if(t&&!gr(o)&&o.as==="script"&&o.href&&n.has(o.href))return a;let u=JSON.stringify(Hi(o));return r.has(u)||(r.add(u),a.push({key:u,link:o})),a},[])}var Et;function Ui(){if(Et!==void 0)return Et;let e=document.createElement("link");return Et=e.relList.supports("preload"),e=null,Et}function zi(){return f.createElement(or,{title:"Loading...",renderScripts:!0},f.createElement("script",{dangerouslySetInnerHTML:{__html:`
              console.log(
                "💿 Hey developer 👋. You can provide a way better UX than this " +
                "when your app is loading JS modules and/or running \`clientLoader\` " +
                "functions. Check out https://reactrouter.com/start/framework/route-module#hydratefallback " +
                "for more information."
              );
            `}}))}function Vn(e){let t={};return Object.values(e).forEach(r=>{if(r){let n=r.parentId||"";t[n]||(t[n]=[]),t[n].push(r)}}),t}function Bi(e,t,r){let n=Jn(t),a=t.HydrateFallback&&(!r||e.id==="root")?t.HydrateFallback:e.id==="root"?zi:void 0,o=t.ErrorBoundary?t.ErrorBoundary:e.id==="root"?()=>f.createElement(Kn,{error:vr()}):void 0;return e.id==="root"&&t.Layout?{...n?{element:f.createElement(t.Layout,null,f.createElement(n,null))}:{Component:n},...o?{errorElement:f.createElement(t.Layout,null,f.createElement(o,null))}:{ErrorBoundary:o},...a?{hydrateFallbackElement:f.createElement(t.Layout,null,f.createElement(a,null))}:{HydrateFallback:a}}:{Component:n,ErrorBoundary:o,HydrateFallback:a}}function Ml(e,t,r,n,a,o){return Er(t,r,n,a,o,"",Vn(t),e)}function Rt(e,t){if(e==="loader"&&!t.hasLoader||e==="action"&&!t.hasAction){let n=`You are trying to call ${e==="action"?"serverAction()":"serverLoader()"} on a route that does not have a server ${e} (routeId: "${t.id}")`;throw console.error(n),new Ue(400,"Bad Request",new Error(n),!0)}}function Xt(e,t){let r=e==="clientAction"?"a":"an",n=`Route "${t}" does not have ${r} ${e}, but you are trying to submit to it. To fix this, please add ${r} \`${e}\` function to the route`;throw console.error(n),new Ue(405,"Method Not Allowed",new Error(n),!0)}function Er(e,t,r,n,a,o="",l=Vn(e),u){return(l[o]||[]).map(i=>{var I,T,_;let s=t[i.id];function d(S){return ye(typeof S=="function","No single fetch function available for route handler"),S()}function m(S){return i.hasLoader?d(S):Promise.resolve(null)}function p(S){if(!i.hasAction)throw Xt("action",i.id);return d(S)}function g(S){import(S)}function R(S){S.clientActionModule&&g(S.clientActionModule),S.clientLoaderModule&&g(S.clientLoaderModule)}async function b(S){let v=t[i.id],M=v?Bn(i,v):Promise.resolve();try{return S()}finally{await M}}let x={id:i.id,index:i.index,path:i.path};if(s){Object.assign(x,{...x,...Bi(i,s,a),unstable_middleware:s.unstable_clientMiddleware,handle:s.handle,shouldRevalidate:dn(x.path,s,i,n,u)});let S=r&&r.loaderData&&i.id in r.loaderData,v=S?(I=r==null?void 0:r.loaderData)==null?void 0:I[i.id]:void 0,M=r&&r.errors&&i.id in r.errors,z=M?(T=r==null?void 0:r.errors)==null?void 0:T[i.id]:void 0,U=u==null&&(((_=s.clientLoader)==null?void 0:_.hydrate)===!0||!i.hasLoader);x.loader=async({request:Q,params:oe,context:ue},le)=>{try{return await b(async()=>(ye(s,"No `routeModule` available for critical-route loader"),s.clientLoader?s.clientLoader({request:Q,params:oe,context:ue,async serverLoader(){if(Rt("loader",i),U){if(S)return v;if(M)throw z}return m(le)}}):m(le)))}finally{U=!1}},x.loader.hydrate=Vi(i.id,s.clientLoader,i.hasLoader,a),x.action=({request:Q,params:oe,context:ue},le)=>b(async()=>{if(ye(s,"No `routeModule` available for critical-route action"),!s.clientAction){if(a)throw Xt("clientAction",i.id);return p(le)}return s.clientAction({request:Q,params:oe,context:ue,async serverAction(){return Rt("action",i),p(le)}})})}else{i.hasClientLoader||(x.loader=(M,z)=>b(()=>m(z))),i.hasClientAction||(x.action=(M,z)=>b(()=>{if(a)throw Xt("clientAction",i.id);return p(z)}));let S;async function v(){return S?await S:(S=(async()=>{(i.clientLoaderModule||i.clientActionModule)&&await new Promise(z=>setTimeout(z,0));let M=Yi(i,t);return R(i),await M})(),await S)}x.lazy={loader:i.hasClientLoader?async()=>{let{clientLoader:M}=i.clientLoaderModule?await import(i.clientLoaderModule):await v();return ye(M,"No `clientLoader` export found"),(z,U)=>M({...z,async serverLoader(){return Rt("loader",i),m(U)}})}:void 0,action:i.hasClientAction?async()=>{let M=i.clientActionModule?import(i.clientActionModule):v();R(i);let{clientAction:z}=await M;return ye(z,"No `clientAction` export found"),(U,Q)=>z({...U,async serverAction(){return Rt("action",i),p(Q)}})}:void 0,unstable_middleware:i.hasClientMiddleware?async()=>{let{unstable_clientMiddleware:M}=i.clientMiddlewareModule?await import(i.clientMiddlewareModule):await v();return ye(M,"No `unstable_clientMiddleware` export found"),M}:void 0,shouldRevalidate:async()=>{let M=await v();return dn(x.path,M,i,n,u)},handle:async()=>(await v()).handle,Component:async()=>(await v()).Component,ErrorBoundary:i.hasErrorBoundary?async()=>(await v()).ErrorBoundary:void 0}}let E=Er(e,t,r,n,a,i.id,l,u);return E.length>0&&(x.children=E),x})}function dn(e,t,r,n,a){if(a)return Wi(r.id,t.shouldRevalidate,a);if(!n&&r.hasLoader&&!r.hasClientLoader){let o=e?gn(e)[1].map(u=>u.paramName):[];const l=u=>o.some(i=>u.currentParams[i]!==u.nextParams[i]);if(t.shouldRevalidate){let u=t.shouldRevalidate;return i=>u({...i,defaultShouldRevalidate:l(i)})}else return u=>l(u)}if(n&&t.shouldRevalidate){let o=t.shouldRevalidate;return l=>o({...l,defaultShouldRevalidate:!0})}return t.shouldRevalidate}function Wi(e,t,r){let n=!1;return a=>n?t?t(a):a.defaultShouldRevalidate:(n=!0,r.has(e))}async function Yi(e,t){let r=Un(e,t),n=$i(e),a=await r;return await Promise.all([n,Bn(e,a)]),{Component:Jn(a),ErrorBoundary:a.ErrorBoundary,unstable_clientMiddleware:a.unstable_clientMiddleware,clientAction:a.clientAction,clientLoader:a.clientLoader,handle:a.handle,links:a.links,meta:a.meta,shouldRevalidate:a.shouldRevalidate}}function Jn(e){if(e.default==null)return;if(!(typeof e.default=="object"&&Object.keys(e.default).length===0))return e.default}function Vi(e,t,r,n){return n&&e!=="root"||t!=null&&(t.hydrate===!0||r!==!0)}var Pt=new Set,Ji=1e3,Dt=new Set,Gi=7680;function Rr(e,t){return e.mode==="lazy"&&t===!0}function Xi({sri:e,...t},r){let n=new Set(r.state.matches.map(u=>u.route.id)),a=r.state.location.pathname.split("/").filter(Boolean),o=["/"];for(a.pop();a.length>0;)o.push(`/${a.join("/")}`),a.pop();o.forEach(u=>{let i=Te(r.routes,u,r.basename);i&&i.forEach(s=>n.add(s.route.id))});let l=[...n].reduce((u,i)=>Object.assign(u,{[i]:t.routes[i]}),{});return{...t,routes:l,sri:e?!0:void 0}}function _l(e,t,r,n,a,o){if(Rr(n,r))return async({path:l,patch:u,signal:i,fetcherKey:s})=>{Dt.has(l)||await Gn([l],s?window.location.href:l,e,t,r,a,o,n.manifestPath,u,i)}}function Dl(e,t,r,n,a,o){f.useEffect(()=>{var d,m;if(!Rr(a,n)||((m=(d=window.navigator)==null?void 0:d.connection)==null?void 0:m.saveData)===!0)return;function l(p){let g=p.tagName==="FORM"?p.getAttribute("action"):p.getAttribute("href");if(!g)return;let R=p.tagName==="A"?p.pathname:new URL(g,window.location.origin).pathname;Dt.has(R)||Pt.add(R)}async function u(){document.querySelectorAll("a[data-discover], form[data-discover]").forEach(l);let p=Array.from(Pt.keys()).filter(g=>Dt.has(g)?(Pt.delete(g),!1):!0);if(p.length!==0)try{await Gn(p,null,t,r,n,o,e.basename,a.manifestPath,e.patchRoutes)}catch(g){console.error("Failed to fetch manifest patches",g)}}let i=Qi(u,100);u();let s=new MutationObserver(()=>i());return s.observe(document.documentElement,{subtree:!0,childList:!0,attributes:!0,attributeFilter:["data-discover","href","action"]}),()=>s.disconnect()},[n,o,t,r,e,a])}function Ki(e,t){let r=e||"/__manifest";return t==null?r:`${t}${r}`.replace(/\/+/g,"/")}var Kt="react-router-manifest-version";async function Gn(e,t,r,n,a,o,l,u,i,s){let d=new URL(Ki(u,l),window.location.origin);if(e.sort().forEach(b=>d.searchParams.append("p",b)),d.searchParams.set("version",r.version),d.toString().length>Gi){Pt.clear();return}let m;try{let b=await fetch(d,{signal:s});if(b.ok){if(b.status===204&&b.headers.has("X-Remix-Reload-Document")){if(!t){console.warn("Detected a manifest version mismatch during eager route discovery. The next navigation/fetch to an undiscovered route will result in a new document navigation to sync up with the latest manifest.");return}if(sessionStorage.getItem(Kt)===r.version){console.error("Unable to discover routes due to manifest version mismatch.");return}sessionStorage.setItem(Kt,r.version),window.location.href=t,console.warn("Detected manifest version mismatch, reloading..."),await new Promise(()=>{})}else if(b.status>=400)throw new Error(await b.text())}else throw new Error(`${b.status} ${b.statusText}`);sessionStorage.removeItem(Kt),m=await b.json()}catch(b){if(s!=null&&s.aborted)return;throw b}let p=new Set(Object.keys(r.routes)),g=Object.values(m).reduce((b,x)=>(x&&!p.has(x.id)&&(b[x.id]=x),b),{});Object.assign(r.routes,g),e.forEach(b=>qi(b,Dt));let R=new Set;Object.values(g).forEach(b=>{b&&(!b.parentId||!g[b.parentId])&&R.add(b.parentId)}),R.forEach(b=>i(b||null,Er(g,n,null,a,o,b)))}function qi(e,t){if(t.size>=Ji){let r=t.values().next().value;t.delete(r)}t.add(e)}function Qi(e,t){let r;return(...n)=>{window.clearTimeout(r),r=window.setTimeout(()=>e(...n),t)}}function br(){let e=f.useContext(Be);return ye(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function kt(){let e=f.useContext(Je);return ye(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var Nt=f.createContext(void 0);Nt.displayName="FrameworkContext";function Ge(){let e=f.useContext(Nt);return ye(e,"You must render this element inside a <HydratedRouter> element"),e}function Zi(e,t){let r=f.useContext(Nt),[n,a]=f.useState(!1),[o,l]=f.useState(!1),{onFocus:u,onBlur:i,onMouseEnter:s,onMouseLeave:d,onTouchStart:m}=t,p=f.useRef(null);f.useEffect(()=>{if(e==="render"&&l(!0),e==="viewport"){let b=E=>{E.forEach(I=>{l(I.isIntersecting)})},x=new IntersectionObserver(b,{threshold:.5});return p.current&&x.observe(p.current),()=>{x.disconnect()}}},[e]),f.useEffect(()=>{if(n){let b=setTimeout(()=>{l(!0)},100);return()=>{clearTimeout(b)}}},[n]);let g=()=>{a(!0)},R=()=>{a(!1),l(!1)};return r?e!=="intent"?[o,p,{}]:[o,p,{onFocus:rt(u,g),onBlur:rt(i,R),onMouseEnter:rt(s,g),onMouseLeave:rt(d,R),onTouchStart:rt(m,g)}]:[!1,p,{}]}function rt(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function Sr(e,t,r){if(r&&!Tt)return[e[0]];if(t){let n=e.findIndex(a=>t[a.route.id]!==void 0);return e.slice(0,n+1)}return e}var fn="data-react-router-critical-css";function Ol(){let{isSpaMode:e,manifest:t,routeModules:r,criticalCss:n}=Ge(),{errors:a,matches:o}=kt(),l=Sr(o,a,e),u=f.useMemo(()=>Ni(l,r,t),[l,r,t]);return f.createElement(f.Fragment,null,typeof n=="string"?f.createElement("style",{[fn]:"",dangerouslySetInnerHTML:{__html:n}}):null,typeof n=="object"?f.createElement("link",{[fn]:"",rel:"stylesheet",href:n.href}):null,u.map(({key:i,link:s})=>gr(s)?f.createElement(Xn,{key:i,...s}):f.createElement("link",{key:i,...s})))}function Xn({page:e,...t}){let{router:r}=br(),n=f.useMemo(()=>Te(r.routes,e,r.basename),[r.routes,e,r.basename]);return n?f.createElement(tl,{page:e,matches:n,...t}):null}function el(e){let{manifest:t,routeModules:r}=Ge(),[n,a]=f.useState([]);return f.useEffect(()=>{let o=!1;return Fi(e,t,r).then(l=>{o||a(l)}),()=>{o=!0}},[e,t,r]),n}function tl({page:e,matches:t,...r}){let n=Le(),{manifest:a,routeModules:o}=Ge(),{basename:l}=br(),{loaderData:u,matches:i}=kt(),s=f.useMemo(()=>cn(e,t,i,a,n,"data"),[e,t,i,a,n]),d=f.useMemo(()=>cn(e,t,i,a,n,"assets"),[e,t,i,a,n]),m=f.useMemo(()=>{if(e===n.pathname+n.search+n.hash)return[];let R=new Set,b=!1;if(t.forEach(E=>{var T;let I=a.routes[E.route.id];!I||!I.hasLoader||(!s.some(_=>_.route.id===E.route.id)&&E.route.id in u&&((T=o[E.route.id])!=null&&T.shouldRevalidate)||I.hasClientLoader?b=!0:R.add(E.route.id))}),R.size===0)return[];let x=Hn(e,l,"data");return b&&R.size>0&&x.searchParams.set("_routes",t.filter(E=>R.has(E.route.id)).map(E=>E.route.id).join(",")),[x.pathname+x.search]},[l,u,n,a,s,t,e,o]),p=f.useMemo(()=>wr(d,a),[d,a]),g=el(d);return f.createElement(f.Fragment,null,m.map(R=>f.createElement("link",{key:R,rel:"prefetch",as:"fetch",href:R,...r})),p.map(R=>f.createElement("link",{key:R,rel:"modulepreload",href:R,...r})),g.map(({key:R,link:b})=>f.createElement("link",{key:R,...b})))}function Al(){let{isSpaMode:e,routeModules:t}=Ge(),{errors:r,matches:n,loaderData:a}=kt(),o=Le(),l=Sr(n,r,e),u=null;r&&(u=r[l[l.length-1].route.id]);let i=[],s=null,d=[];for(let m=0;m<l.length;m++){let p=l[m],g=p.route.id,R=a[g],b=p.params,x=t[g],E=[],I={id:g,data:R,meta:[],params:p.params,pathname:p.pathname,handle:p.route.handle,error:u};if(d[m]=I,x!=null&&x.meta?E=typeof x.meta=="function"?x.meta({data:R,params:b,location:o,matches:d,error:u}):Array.isArray(x.meta)?[...x.meta]:x.meta:s&&(E=[...s]),E=E||[],!Array.isArray(E))throw new Error("The route at "+p.route.path+` returns an invalid value. All route meta functions must return an array of meta objects.

To reference the meta function API, see https://remix.run/route/meta`);I.meta=E,d[m]=I,i=[...E],s=i}return f.createElement(f.Fragment,null,i.flat().map(m=>{if(!m)return null;if("tagName"in m){let{tagName:p,...g}=m;if(!rl(p))return console.warn(`A meta object uses an invalid tagName: ${p}. Expected either 'link' or 'meta'`),null;let R=p;return f.createElement(R,{key:JSON.stringify(g),...g})}if("title"in m)return f.createElement("title",{key:"title"},String(m.title));if("charset"in m&&(m.charSet??(m.charSet=m.charset),delete m.charset),"charSet"in m&&m.charSet!=null)return typeof m.charSet=="string"?f.createElement("meta",{key:"charSet",charSet:m.charSet}):null;if("script:ld+json"in m)try{let p=JSON.stringify(m["script:ld+json"]);return f.createElement("script",{key:`script:ld+json:${p}`,type:"application/ld+json",dangerouslySetInnerHTML:{__html:p}})}catch{return null}return f.createElement("meta",{key:JSON.stringify(m),...m})}))}function rl(e){return typeof e=="string"&&/^(meta|link)$/.test(e)}var Tt=!1;function nl(e){let{manifest:t,serverHandoffString:r,isSpaMode:n,renderMeta:a,routeDiscovery:o,ssr:l}=Ge(),{router:u,static:i,staticContext:s}=br(),{matches:d}=kt(),m=Co(),p=Rr(o,l);a&&(a.didRenderScripts=!0);let g=Sr(d,null,n);f.useEffect(()=>{Tt=!0},[]);let R=f.useMemo(()=>{var _;if(m)return null;let I=s?`window.__reactRouterContext = ${r};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());`:" ",T=i?`${(_=t.hmr)!=null&&_.runtime?`import ${JSON.stringify(t.hmr.runtime)};`:""}${p?"":`import ${JSON.stringify(t.url)}`};
${g.map((S,v)=>{let M=`route${v}`,z=t.routes[S.route.id];ye(z,`Route ${S.route.id} not found in manifest`);let{clientActionModule:U,clientLoaderModule:Q,clientMiddlewareModule:oe,hydrateFallbackModule:ue,module:le}=z,G=[...U?[{module:U,varName:`${M}_clientAction`}]:[],...Q?[{module:Q,varName:`${M}_clientLoader`}]:[],...oe?[{module:oe,varName:`${M}_clientMiddleware`}]:[],...ue?[{module:ue,varName:`${M}_HydrateFallback`}]:[],{module:le,varName:`${M}_main`}];if(G.length===1)return`import * as ${M} from ${JSON.stringify(le)};`;let K=G.map(re=>`import * as ${re.varName} from "${re.module}";`).join(`
`),X=`const ${M} = {${G.map(re=>`...${re.varName}`).join(",")}};`;return[K,X].join(`
`)}).join(`
`)}
  ${p?`window.__reactRouterManifest = ${JSON.stringify(Xi(t,u),null,2)};`:""}
  window.__reactRouterRouteModules = {${g.map((S,v)=>`${JSON.stringify(S.route.id)}:route${v}`).join(",")}};

import(${JSON.stringify(t.entry.module)});`:" ";return f.createElement(f.Fragment,null,f.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:sn(I),type:void 0}),f.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:sn(T),type:"module",async:!0}))},[]),b=Tt||m?[]:al(t.entry.imports.concat(wr(g,t,{includeHydrateFallback:!0}))),x=typeof t.sri=="object"?t.sri:{};return rr(!m,"The <Scripts /> element is a no-op when using RSC and can be safely removed."),Tt||m?null:f.createElement(f.Fragment,null,typeof t.sri=="object"?f.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:x})}}):null,p?null:f.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:x[t.url],suppressHydrationWarning:!0}),f.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:x[t.entry.module],suppressHydrationWarning:!0}),b.map(E=>f.createElement("link",{key:E,rel:"modulepreload",href:E,crossOrigin:e.crossOrigin,integrity:x[E],suppressHydrationWarning:!0})),R)}function al(e){return[...new Set(e)]}function ol(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var kl=class extends f.Component{constructor(e){super(e),this.state={error:e.error||null,location:e.location}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location?{error:e.error||null,location:e.location}:{error:e.error||t.error,location:t.location}}render(){return this.state.error?f.createElement(Kn,{error:this.state.error,isOutsideRemixApp:!0}):this.props.children}};function Kn({error:e,isOutsideRemixApp:t}){console.error(e);let r=f.createElement("script",{dangerouslySetInnerHTML:{__html:`
        console.log(
          "💿 Hey developer 👋. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."
        );
      `}});if(ze(e))return f.createElement(or,{title:"Unhandled Thrown Response!"},f.createElement("h1",{style:{fontSize:"24px"}},e.status," ",e.statusText),r);let n;if(e instanceof Error)n=e;else{let a=e==null?"Unknown Error":typeof e=="object"&&"toString"in e?e.toString():JSON.stringify(e);n=new Error(a)}return f.createElement(or,{title:"Application Error!",isOutsideRemixApp:t},f.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),f.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},n.stack),r)}function or({title:e,renderScripts:t,isOutsideRemixApp:r,children:n}){var o;let{routeModules:a}=Ge();return(o=a.root)!=null&&o.Layout&&!r?n:f.createElement("html",{lang:"en"},f.createElement("head",null,f.createElement("meta",{charSet:"utf-8"}),f.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),f.createElement("title",null,e)),f.createElement("body",null,f.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},n,t?f.createElement(nl,null):null)))}var qn=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{qn&&(window.__reactRouterVersion="7.7.1")}catch{}var Qn=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Zn=f.forwardRef(function({onClick:t,discover:r="render",prefetch:n="none",relative:a,reloadDocument:o,replace:l,state:u,target:i,to:s,preventScrollReset:d,viewTransition:m,...p},g){let{basename:R}=f.useContext(Re),b=typeof s=="string"&&Qn.test(s),x,E=!1;if(typeof s=="string"&&b&&(x=s,qn))try{let U=new URL(window.location.href),Q=s.startsWith("//")?new URL(U.protocol+s):new URL(s),oe=we(Q.pathname,R);Q.origin===U.origin&&oe!=null?s=oe+Q.search+Q.hash:E=!0}catch{ae(!1,`<Link to="${s}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let I=Po(s,{relative:a}),[T,_,S]=Zi(n,p),v=cl(s,{replace:l,state:u,target:i,preventScrollReset:d,relative:a,viewTransition:m});function M(U){t&&t(U),U.defaultPrevented||v(U)}let z=f.createElement("a",{...p,...S,href:x||I,onClick:E||o?t:M,ref:ol(g,_),target:i,"data-discover":!b&&r==="render"?"true":void 0});return T&&!b?f.createElement(f.Fragment,null,z,f.createElement(Xn,{page:I})):z});Zn.displayName="Link";var il=f.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:n="",end:a=!1,style:o,to:l,viewTransition:u,children:i,...s},d){let m=st(l,{relative:s.relative}),p=Le(),g=f.useContext(Je),{navigator:R,basename:b}=f.useContext(Re),x=g!=null&&vl(m)&&u===!0,E=R.encodeLocation?R.encodeLocation(m).pathname:m.pathname,I=p.pathname,T=g&&g.navigation&&g.navigation.location?g.navigation.location.pathname:null;r||(I=I.toLowerCase(),T=T?T.toLowerCase():null,E=E.toLowerCase()),T&&b&&(T=we(T,b)||T);const _=E!=="/"&&E.endsWith("/")?E.length-1:E.length;let S=I===E||!a&&I.startsWith(E)&&I.charAt(_)==="/",v=T!=null&&(T===E||!a&&T.startsWith(E)&&T.charAt(E.length)==="/"),M={isActive:S,isPending:v,isTransitioning:x},z=S?t:void 0,U;typeof n=="function"?U=n(M):U=[n,S?"active":null,v?"pending":null,x?"transitioning":null].filter(Boolean).join(" ");let Q=typeof o=="function"?o(M):o;return f.createElement(Zn,{...s,"aria-current":z,className:U,ref:d,style:Q,to:l,viewTransition:u},typeof i=="function"?i(M):i)});il.displayName="NavLink";var ll=f.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:n,replace:a,state:o,method:l=Ct,action:u,onSubmit:i,relative:s,preventScrollReset:d,viewTransition:m,...p},g)=>{let R=hl(),b=ml(u,{relative:s}),x=l.toLowerCase()==="get"?"get":"post",E=typeof u=="string"&&Qn.test(u),I=T=>{if(i&&i(T),T.defaultPrevented)return;T.preventDefault();let _=T.nativeEvent.submitter,S=(_==null?void 0:_.getAttribute("formmethod"))||l;R(_||T.currentTarget,{fetcherKey:t,method:S,navigate:r,replace:a,state:o,relative:s,preventScrollReset:d,viewTransition:m})};return f.createElement("form",{ref:g,method:x,action:b,onSubmit:n?i:I,...p,"data-discover":!E&&e==="render"?"true":void 0})});ll.displayName="Form";function sl({getKey:e,storageKey:t,...r}){let n=f.useContext(Nt),{basename:a}=f.useContext(Re),o=Le(),l=yr();pl({getKey:e,storageKey:t});let u=f.useMemo(()=>{if(!n||!e)return null;let s=lr(o,l,a,e);return s!==o.key?s:null},[]);if(!n||n.isSpaMode)return null;let i=((s,d)=>{if(!window.history.state||!window.history.state.key){let m=Math.random().toString(32).slice(2);window.history.replaceState({key:m},"")}try{let p=JSON.parse(sessionStorage.getItem(s)||"{}")[d||window.history.state.key];typeof p=="number"&&window.scrollTo(0,p)}catch(m){console.error(m),sessionStorage.removeItem(s)}}).toString();return f.createElement("script",{...r,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:`(${i})(${JSON.stringify(t||ir)}, ${JSON.stringify(u)})`}})}sl.displayName="ScrollRestoration";function ea(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function xr(e){let t=f.useContext(Be);return J(t,ea(e)),t}function ul(e){let t=f.useContext(Je);return J(t,ea(e)),t}function cl(e,{target:t,replace:r,state:n,preventScrollReset:a,relative:o,viewTransition:l}={}){let u=To(),i=Le(),s=st(e,{relative:o});return f.useCallback(d=>{if(Zo(d,t)){d.preventDefault();let m=r!==void 0?r:Ae(i)===Ae(s);u(e,{replace:m,state:n,preventScrollReset:a,relative:o,viewTransition:l})}},[i,u,s,r,n,t,e,a,o,l])}var dl=0,fl=()=>`__${String(++dl)}__`;function hl(){let{router:e}=xr("useSubmit"),{basename:t}=f.useContext(Re),r=Ho();return f.useCallback(async(n,a={})=>{let{action:o,method:l,encType:u,formData:i,body:s}=ri(n,t);if(a.navigate===!1){let d=a.fetcherKey||fl();await e.fetch(d,r,a.action||o,{preventScrollReset:a.preventScrollReset,formData:i,body:s,formMethod:a.method||l,formEncType:a.encType||u,flushSync:a.flushSync})}else await e.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:i,body:s,formMethod:a.method||l,formEncType:a.encType||u,replace:a.replace,state:a.state,fromRouteId:r,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,r])}function ml(e,{relative:t}={}){let{basename:r}=f.useContext(Re),n=f.useContext(be);J(n,"useFormAction must be used inside a RouteContext");let[a]=n.matches.slice(-1),o={...st(e||".",{relative:t})},l=Le();if(e==null){o.search=l.search;let u=new URLSearchParams(o.search),i=u.getAll("index");if(i.some(d=>d==="")){u.delete("index"),i.filter(m=>m).forEach(m=>u.append("index",m));let d=u.toString();o.search=d?`?${d}`:""}}return(!e||e===".")&&a.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(o.pathname=o.pathname==="/"?r:Ce([r,o.pathname])),Ae(o)}var ir="react-router-scroll-positions",bt={};function lr(e,t,r,n){let a=null;return n&&(r!=="/"?a=n({...e,pathname:we(e.pathname,r)||e.pathname},t):a=n(e,t)),a==null&&(a=e.key),a}function pl({getKey:e,storageKey:t}={}){let{router:r}=xr("useScrollRestoration"),{restoreScrollPosition:n,preventScrollReset:a}=ul("useScrollRestoration"),{basename:o}=f.useContext(Re),l=Le(),u=yr(),i=Uo();f.useEffect(()=>(window.history.scrollRestoration="manual",()=>{window.history.scrollRestoration="auto"}),[]),yl(f.useCallback(()=>{if(i.state==="idle"){let s=lr(l,u,o,e);bt[s]=window.scrollY}try{sessionStorage.setItem(t||ir,JSON.stringify(bt))}catch(s){ae(!1,`Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${s}).`)}window.history.scrollRestoration="auto"},[i.state,e,o,l,u,t])),typeof document<"u"&&(f.useLayoutEffect(()=>{try{let s=sessionStorage.getItem(t||ir);s&&(bt=JSON.parse(s))}catch{}},[t]),f.useLayoutEffect(()=>{let s=r==null?void 0:r.enableScrollRestoration(bt,()=>window.scrollY,e?(d,m)=>lr(d,m,o,e):void 0);return()=>s&&s()},[r,o,e]),f.useLayoutEffect(()=>{if(n!==!1){if(typeof n=="number"){window.scrollTo(0,n);return}try{if(l.hash){let s=document.getElementById(decodeURIComponent(l.hash.slice(1)));if(s){s.scrollIntoView();return}}}catch{ae(!1,`"${l.hash.slice(1)}" is not a decodable element ID. The view will not scroll to it.`)}a!==!0&&window.scrollTo(0,0)}},[l,n,a]))}function yl(e,t){let{capture:r}={};f.useEffect(()=>{let n=r!=null?{capture:r}:void 0;return window.addEventListener("pagehide",e,n),()=>{window.removeEventListener("pagehide",e,n)}},[e,r])}function vl(e,{relative:t}={}){let r=f.useContext(hr);J(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:n}=xr("useViewTransitionState"),a=st(e,{relative:t});if(!r.isTransitioning)return!1;let o=we(r.currentLocation.pathname,n)||r.currentLocation.pathname,l=we(r.nextLocation.pathname,n)||r.nextLocation.pathname;return Mt(a.pathname,l)!=null||Mt(a.pathname,o)!=null}export{Ue as E,Nt as F,Ol as L,Al as M,Ci as N,Cl as O,kl as R,Ra as S,f as a,J as b,xl as c,ki as d,Er as e,Rl as f,_l as g,Tl as h,ze as i,El as j,Ml as k,bl as l,Te as m,Sl as n,gl as o,Pl as p,sl as q,Ea as r,Vi as s,nl as t,Dl as u,To as v,Ll as w,wl as x,ya as y};
