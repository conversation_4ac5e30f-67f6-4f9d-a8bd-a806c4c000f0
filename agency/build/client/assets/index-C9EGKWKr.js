import{a as p,o as g,S,x as _o}from"./chunk-C37GKA54-BAI9agUh.js";import{A as Ro,a as Eo}from"./index-BCQUowBv.js";function Ir(e,n){if(typeof e=="function")return e(n);e!=null&&(e.current=n)}function jo(...e){return n=>{let t=!1;const r=e.map(o=>{const a=Ir(o,n);return!t&&typeof a=="function"&&(t=!0),a});if(t)return()=>{for(let o=0;o<r.length;o++){const a=r[o];typeof a=="function"?a():Ir(e[o],null)}}}}function ke(...e){return p.useCallback(jo(...e),e)}function hn(e){const n=El(e),t=p.forwardRef((r,o)=>{const{children:a,...l}=r,i=p.Children.toArray(a),c=i.find(Nl);if(c){const d=c.props.children,u=i.map(s=>s===c?p.Children.count(d)>1?p.Children.only(null):p.isValidElement(d)?d.props.children:null:s);return g.jsx(n,{...l,ref:o,children:p.isValidElement(d)?p.cloneElement(d,void 0,u):null})}return g.jsx(n,{...l,ref:o,children:a})});return t.displayName=`${e}.Slot`,t}var Rl=hn("Slot");function El(e){const n=p.forwardRef((t,r)=>{const{children:o,...a}=t;if(p.isValidElement(o)){const l=Ml(o),i=kl(a,o.props);return o.type!==p.Fragment&&(i.ref=r?jo(r,l):l),p.cloneElement(o,i)}return p.Children.count(o)>1?p.Children.only(null):null});return n.displayName=`${e}.SlotClone`,n}var jl=Symbol("radix.slottable");function Nl(e){return p.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===jl}function kl(e,n){const t={...n};for(const r in n){const o=e[r],a=n[r];/^on[A-Z]/.test(r)?o&&a?t[r]=(...l)=>{const i=a(...l);return o(...l),i}:o&&(t[r]=o):r==="style"?t[r]={...o,...a}:r==="className"&&(t[r]=[o,a].filter(Boolean).join(" "))}return{...e,...t}}function Ml(e){var n,t;let r=(n=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o?e.props.ref:e.props.ref||e.ref)}function No(e){var n,t,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(n=0;n<o;n++)e[n]&&(t=No(e[n]))&&(r&&(r+=" "),r+=t)}else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function ko(){for(var e,n,t=0,r="",o=arguments.length;t<o;t++)(e=arguments[t])&&(n=No(e))&&(r&&(r+=" "),r+=n);return r}const Or=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Dr=ko,J=(e,n)=>t=>{var r;if((n==null?void 0:n.variants)==null)return Dr(e,t==null?void 0:t.class,t==null?void 0:t.className);const{variants:o,defaultVariants:a}=n,l=Object.keys(o).map(d=>{const u=t==null?void 0:t[d],s=a==null?void 0:a[d];if(u===null)return null;const f=Or(u)||Or(s);return o[d][f]}),i=t&&Object.entries(t).reduce((d,u)=>{let[s,f]=u;return f===void 0||(d[s]=f),d},{}),c=n==null||(r=n.compoundVariants)===null||r===void 0?void 0:r.reduce((d,u)=>{let{class:s,className:f,...m}=u;return Object.entries(m).every(h=>{let[b,x]=h;return Array.isArray(x)?x.includes({...a,...i}[b]):{...a,...i}[b]===x})?[...d,s,f]:d},[]);return Dr(e,l,c,t==null?void 0:t.class,t==null?void 0:t.className)};var Mo=function(e){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),S.createElement("path",{d:"M10 6a.75.75 0 0 1 .75.75v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 .75-.75Z"}),S.createElement("path",{d:"M10 13a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"}),S.createElement("path",{fillRule:"evenodd",d:"M3.5 8.25a4.75 4.75 0 0 1 4.75-4.75h3.5a4.75 4.75 0 0 1 4.75 4.75v2.5a4.75 4.75 0 0 1-4.573 4.747l-1.335 1.714a.75.75 0 0 1-1.189-.006l-1.3-1.707a4.75 4.75 0 0 1-4.603-4.748v-2.5Zm4.75-3.25a3.25 3.25 0 0 0-3.25 3.25v2.5a3.25 3.25 0 0 0 3.25 3.25h.226a.75.75 0 0 1 .597.296l.934 1.225.96-1.232a.75.75 0 0 1 .591-.289h.192a3.25 3.25 0 0 0 3.25-3.25v-2.5a3.25 3.25 0 0 0-3.25-3.25h-3.5Z"}))};Mo.displayName="AlertBubbleIcon";var Po=function(e){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),S.createElement("path",{fillRule:"evenodd",d:"M15.78 5.97a.75.75 0 0 1 0 1.06l-6.5 6.5a.75.75 0 0 1-1.06 0l-3.25-3.25a.75.75 0 1 1 1.06-1.06l2.72 2.72 5.97-5.97a.75.75 0 0 1 1.06 0Z"}))};Po.displayName="CheckIcon";var rr=function(e){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),S.createElement("path",{fillRule:"evenodd",d:"M5.72 8.47a.75.75 0 0 1 1.06 0l3.47 3.47 3.47-3.47a.75.75 0 1 1 1.06 1.06l-4 4a.75.75 0 0 1-1.06 0l-4-4a.75.75 0 0 1 0-1.06Z"}))};rr.displayName="ChevronDownIcon";var or=function(e){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),S.createElement("path",{fillRule:"evenodd",d:"M11.764 5.204a.75.75 0 0 1 .032 1.06l-3.516 3.736 3.516 3.736a.75.75 0 1 1-1.092 1.028l-4-4.25a.75.75 0 0 1 0-1.028l4-4.25a.75.75 0 0 1 1.06-.032Z"}))};or.displayName="ChevronLeftIcon";var ar=function(e){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),S.createElement("path",{fillRule:"evenodd",d:"M7.72 14.53a.75.75 0 0 1 0-1.06l3.47-3.47-3.47-3.47a.75.75 0 0 1 1.06-1.06l4 4a.75.75 0 0 1 0 1.06l-4 4a.75.75 0 0 1-1.06 0Z"}))};ar.displayName="ChevronRightIcon";var lr=function(e){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),S.createElement("path",{fillRule:"evenodd",d:"M14.53 12.28a.75.75 0 0 1-1.06 0l-3.47-3.47-3.47 3.47a.75.75 0 0 1-1.06-1.06l4-4a.75.75 0 0 1 1.06 0l4 4a.75.75 0 0 1 0 1.06Z"}))};lr.displayName="ChevronUpIcon";var Fo=function(e){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),S.createElement("path",{d:"M3 6a.75.75 0 0 1 .75-.75h12.5a.75.75 0 0 1 0 1.5h-12.5a.75.75 0 0 1-.75-.75Z"}),S.createElement("path",{d:"M6.75 14a.75.75 0 0 1 .75-.75h5a.75.75 0 0 1 0 1.5h-5a.75.75 0 0 1-.75-.75Z"}),S.createElement("path",{d:"M5.5 9.25a.75.75 0 0 0 0 1.5h9a.75.75 0 0 0 0-1.5h-9Z"}))};Fo.displayName="FilterIcon";var Ao=function(e){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),S.createElement("path",{d:"M3.5 9.25a.75.75 0 0 0 1.5 0 3 3 0 0 1 3-3h6.566l-1.123 1.248a.75.75 0 1 0 1.114 1.004l2.25-2.5a.75.75 0 0 0-.027-1.032l-2.25-2.25a.75.75 0 1 0-1.06 1.06l.97.97h-6.44a4.5 4.5 0 0 0-4.5 4.5Z"}),S.createElement("path",{d:"M16.5 10.75a.75.75 0 0 0-1.5 0 3 3 0 0 1-3 3h-6.566l1.123-1.248a.75.75 0 1 0-1.114-1.004l-2.25 2.5a.75.75 0 0 0 .027 1.032l2.25 2.25a.75.75 0 0 0 1.06-1.06l-.97-.97h6.44a4.5 4.5 0 0 0 4.5-4.5Z"}))};Ao.displayName="RefreshIcon";var Io=function(e){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),S.createElement("path",{fillRule:"evenodd",d:"M12.323 13.383a5.5 5.5 0 1 1 1.06-1.06l2.897 2.897a.75.75 0 1 1-1.06 1.06l-2.897-2.897Zm.677-4.383a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z"}))};Io.displayName="SearchIcon";var Oo=function(e){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),S.createElement("path",{fillRule:"evenodd",d:"M12.5 10a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0Zm-1.5 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"}),S.createElement("path",{fillRule:"evenodd",d:"M9.377 2.5c-.926 0-1.676.75-1.676 1.676v.688c0 .056-.043.17-.198.251-.153.08-.303.168-.448.262-.147.097-.268.076-.318.048l-.6-.346a1.676 1.676 0 0 0-2.29.613l-.622 1.08a1.676 1.676 0 0 0 .613 2.289l.648.374c.048.028.124.12.119.29a5.484 5.484 0 0 0 .005.465c.009.175-.07.27-.119.299l-.653.377a1.676 1.676 0 0 0-.613 2.29l.623 1.08a1.676 1.676 0 0 0 2.29.613l.7-.405c.048-.028.166-.048.312.043.115.071.233.139.353.202.155.08.198.195.198.251v.811c0 .926.75 1.676 1.676 1.676h1.246c.926 0 1.676-.75 1.676-1.676v-.81c0-.057.042-.171.197-.252.121-.063.239-.13.354-.202.146-.091.264-.07.312-.043l.7.405a1.676 1.676 0 0 0 2.29-.614l.623-1.08a1.676 1.676 0 0 0-.613-2.289l-.653-.377c-.05-.029-.128-.123-.119-.3a5.494 5.494 0 0 0 .005-.463c-.005-.171.07-.263.12-.291l.647-.374a1.676 1.676 0 0 0 .613-2.29l-.623-1.079a1.676 1.676 0 0 0-2.29-.613l-.6.346c-.049.028-.17.048-.318-.048a5.4 5.4 0 0 0-.448-.262c-.155-.081-.197-.195-.197-.251v-.688c0-.926-.75-1.676-1.676-1.676h-1.246Zm-.176 1.676c0-.097.078-.176.176-.176h1.246c.097 0 .176.079.176.176v.688c0 .728.462 1.298 1.003 1.58.11.058.219.122.323.19.517.337 1.25.458 1.888.09l.6-.346a.176.176 0 0 1 .24.064l.623 1.08a.176.176 0 0 1-.064.24l-.648.374c-.623.36-.888 1.034-.868 1.638a4.184 4.184 0 0 1-.004.337c-.032.615.23 1.31.867 1.677l.653.377a.176.176 0 0 1 .064.24l-.623 1.08a.176.176 0 0 1-.24.065l-.701-.405c-.624-.36-1.341-.251-1.855.069a3.91 3.91 0 0 1-.255.145c-.54.283-1.003.853-1.003 1.581v.811a.176.176 0 0 1-.176.176h-1.246a.176.176 0 0 1-.176-.176v-.81c0-.73-.462-1.3-1.003-1.582a3.873 3.873 0 0 1-.255-.146c-.514-.32-1.23-.428-1.855-.068l-.7.405a.176.176 0 0 1-.241-.065l-.623-1.08a.176.176 0 0 1 .064-.24l.653-.377c.637-.368.899-1.062.867-1.677a3.97 3.97 0 0 1-.004-.337c.02-.604-.245-1.278-.868-1.638l-.648-.374a.176.176 0 0 1-.064-.24l.623-1.08a.176.176 0 0 1 .24-.064l.6.346c.638.368 1.37.247 1.888-.09a3.85 3.85 0 0 1 .323-.19c.54-.282 1.003-.852 1.003-1.58v-.688Z"}))};Oo.displayName="SettingsIcon";var Do=function(e){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),S.createElement("path",{d:"M7.75 6.06v7.69a.75.75 0 0 1-1.5 0v-7.69l-1.72 1.72a.75.75 0 0 1-1.06-1.06l3-3a.75.75 0 0 1 1.06 0l3 3a.75.75 0 1 1-1.06 1.06l-1.72-1.72Z"}),S.createElement("path",{d:"M13.75 6.25a.75.75 0 0 0-1.5 0v7.69l-1.72-1.72a.75.75 0 1 0-1.06 1.06l3 3a.75.75 0 0 0 1.06 0l3-3a.75.75 0 1 0-1.06-1.06l-1.72 1.72v-7.69Z"}))};Do.displayName="SortIcon";var Lo=function(e){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),S.createElement("path",{d:"M13.03 6.97a.75.75 0 0 1 0 1.06l-1.97 1.97 1.97 1.97a.75.75 0 1 1-1.06 1.06l-1.97-1.97-1.97 1.97a.75.75 0 0 1-1.06-1.06l1.97-1.97-1.97-1.97a.75.75 0 0 1 1.06-1.06l1.97 1.97 1.97-1.97a.75.75 0 0 1 1.06 0Z"}),S.createElement("path",{fillRule:"evenodd",d:"M10 17a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm0-1.5a5.5 5.5 0 1 0 0-11 5.5 5.5 0 0 0 0 11Z"}))};Lo.displayName="XCircleIcon";var ir=function(e){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),S.createElement("path",{d:"M13.97 15.03a.75.75 0 1 0 1.06-1.06l-3.97-3.97 3.97-3.97a.75.75 0 0 0-1.06-1.06l-3.97 3.97-3.97-3.97a.75.75 0 0 0-1.06 1.06l3.97 3.97-3.97 3.97a.75.75 0 1 0 1.06 1.06l3.97-3.97 3.97 3.97Z"}))};ir.displayName="XIcon";const sr="-",Pl=e=>{const n=Al(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:o=>{const a=o.split(sr);return a[0]===""&&a.length!==1&&a.shift(),To(a,n)||Fl(o)},getConflictingClassGroupIds:(o,a)=>{const l=t[o]||[];return a&&r[o]?[...l,...r[o]]:l}}},To=(e,n)=>{var t;if(e.length===0)return n.classGroupId;const r=e[0],o=n.nextPart.get(r),a=o?To(e.slice(1),o):void 0;if(a)return a;if(n.validators.length===0)return;const l=e.join(sr);return(t=n.validators.find(({validator:i})=>i(l)))==null?void 0:t.classGroupId},Lr=/^\[(.+)\]$/,Fl=e=>{if(Lr.test(e)){const n=Lr.exec(e)[1],t=n==null?void 0:n.substring(0,n.indexOf(":"));if(t)return"arbitrary.."+t}},Al=e=>{const{theme:n,prefix:t}=e,r={nextPart:new Map,validators:[]};return Ol(Object.entries(e.classGroups),t).forEach(([o,a])=>{Wn(a,r,o,n)}),r},Wn=(e,n,t,r)=>{e.forEach(o=>{if(typeof o=="string"){const a=o===""?n:Tr(n,o);a.classGroupId=t;return}if(typeof o=="function"){if(Il(o)){Wn(o(r),n,t,r);return}n.validators.push({validator:o,classGroupId:t});return}Object.entries(o).forEach(([a,l])=>{Wn(l,Tr(n,a),t,r)})})},Tr=(e,n)=>{let t=e;return n.split(sr).forEach(r=>{t.nextPart.has(r)||t.nextPart.set(r,{nextPart:new Map,validators:[]}),t=t.nextPart.get(r)}),t},Il=e=>e.isThemeGetter,Ol=(e,n)=>n?e.map(([t,r])=>{const o=r.map(a=>typeof a=="string"?n+a:typeof a=="object"?Object.fromEntries(Object.entries(a).map(([l,i])=>[n+l,i])):a);return[t,o]}):e,Dl=e=>{if(e<1)return{get:()=>{},set:()=>{}};let n=0,t=new Map,r=new Map;const o=(a,l)=>{t.set(a,l),n++,n>e&&(n=0,r=t,t=new Map)};return{get(a){let l=t.get(a);if(l!==void 0)return l;if((l=r.get(a))!==void 0)return o(a,l),l},set(a,l){t.has(a)?t.set(a,l):o(a,l)}}},Vo="!",Ll=e=>{const{separator:n,experimentalParseClassName:t}=e,r=n.length===1,o=n[0],a=n.length,l=i=>{const c=[];let d=0,u=0,s;for(let x=0;x<i.length;x++){let v=i[x];if(d===0){if(v===o&&(r||i.slice(x,x+a)===n)){c.push(i.slice(u,x)),u=x+a;continue}if(v==="/"){s=x;continue}}v==="["?d++:v==="]"&&d--}const f=c.length===0?i:i.substring(u),m=f.startsWith(Vo),h=m?f.substring(1):f,b=s&&s>u?s-u:void 0;return{modifiers:c,hasImportantModifier:m,baseClassName:h,maybePostfixModifierPosition:b}};return t?i=>t({className:i,parseClassName:l}):l},Tl=e=>{if(e.length<=1)return e;const n=[];let t=[];return e.forEach(r=>{r[0]==="["?(n.push(...t.sort(),r),t=[]):t.push(r)}),n.push(...t.sort()),n},Vl=e=>({cache:Dl(e.cacheSize),parseClassName:Ll(e),...Pl(e)}),zl=/\s+/,Bl=(e,n)=>{const{parseClassName:t,getClassGroupId:r,getConflictingClassGroupIds:o}=n,a=[],l=e.trim().split(zl);let i="";for(let c=l.length-1;c>=0;c-=1){const d=l[c],{modifiers:u,hasImportantModifier:s,baseClassName:f,maybePostfixModifierPosition:m}=t(d);let h=!!m,b=r(h?f.substring(0,m):f);if(!b){if(!h){i=d+(i.length>0?" "+i:i);continue}if(b=r(f),!b){i=d+(i.length>0?" "+i:i);continue}h=!1}const x=Tl(u).join(":"),v=s?x+Vo:x,y=v+b;if(a.includes(y))continue;a.push(y);const w=o(b,h);for(let C=0;C<w.length;++C){const E=w[C];a.push(v+E)}i=d+(i.length>0?" "+i:i)}return i};function Hl(){let e=0,n,t,r="";for(;e<arguments.length;)(n=arguments[e++])&&(t=zo(n))&&(r&&(r+=" "),r+=t);return r}const zo=e=>{if(typeof e=="string")return e;let n,t="";for(let r=0;r<e.length;r++)e[r]&&(n=zo(e[r]))&&(t&&(t+=" "),t+=n);return t};function $l(e,...n){let t,r,o,a=l;function l(c){const d=n.reduce((u,s)=>s(u),e());return t=Vl(d),r=t.cache.get,o=t.cache.set,a=i,i(c)}function i(c){const d=r(c);if(d)return d;const u=Bl(c,t);return o(c,u),u}return function(){return a(Hl.apply(null,arguments))}}const te=e=>{const n=t=>t[e]||[];return n.isThemeGetter=!0,n},Bo=/^\[(?:([a-z-]+):)?(.+)\]$/i,Gl=/^\d+\/\d+$/,Wl=new Set(["px","full","screen"]),Ul=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,ql=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Xl=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Yl=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Kl=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Be=e=>xt(e)||Wl.has(e)||Gl.test(e),Xe=e=>Rt(e,"length",oi),xt=e=>!!e&&!Number.isNaN(Number(e)),Nn=e=>Rt(e,"number",xt),Ft=e=>!!e&&Number.isInteger(Number(e)),Zl=e=>e.endsWith("%")&&xt(e.slice(0,-1)),W=e=>Bo.test(e),Ye=e=>Ul.test(e),Jl=new Set(["length","size","percentage"]),Ql=e=>Rt(e,Jl,Ho),ei=e=>Rt(e,"position",Ho),ti=new Set(["image","url"]),ni=e=>Rt(e,ti,li),ri=e=>Rt(e,"",ai),At=()=>!0,Rt=(e,n,t)=>{const r=Bo.exec(e);return r?r[1]?typeof n=="string"?r[1]===n:n.has(r[1]):t(r[2]):!1},oi=e=>ql.test(e)&&!Xl.test(e),Ho=()=>!1,ai=e=>Yl.test(e),li=e=>Kl.test(e),ii=()=>{const e=te("colors"),n=te("spacing"),t=te("blur"),r=te("brightness"),o=te("borderColor"),a=te("borderRadius"),l=te("borderSpacing"),i=te("borderWidth"),c=te("contrast"),d=te("grayscale"),u=te("hueRotate"),s=te("invert"),f=te("gap"),m=te("gradientColorStops"),h=te("gradientColorStopPositions"),b=te("inset"),x=te("margin"),v=te("opacity"),y=te("padding"),w=te("saturate"),C=te("scale"),E=te("sepia"),N=te("skew"),_=te("space"),R=te("translate"),D=()=>["auto","contain","none"],P=()=>["auto","hidden","clip","visible","scroll"],G=()=>["auto",W,n],k=()=>[W,n],q=()=>["",Be,Xe],V=()=>["auto",xt,W],X=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],M=()=>["solid","dashed","dotted","double","none"],I=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],L=()=>["start","end","center","between","around","evenly","stretch"],T=()=>["","0",W],A=()=>["auto","avoid","all","avoid-page","page","left","right","column"],j=()=>[xt,W];return{cacheSize:500,separator:":",theme:{colors:[At],spacing:[Be,Xe],blur:["none","",Ye,W],brightness:j(),borderColor:[e],borderRadius:["none","","full",Ye,W],borderSpacing:k(),borderWidth:q(),contrast:j(),grayscale:T(),hueRotate:j(),invert:T(),gap:k(),gradientColorStops:[e],gradientColorStopPositions:[Zl,Xe],inset:G(),margin:G(),opacity:j(),padding:k(),saturate:j(),scale:j(),sepia:T(),skew:j(),space:k(),translate:k()},classGroups:{aspect:[{aspect:["auto","square","video",W]}],container:["container"],columns:[{columns:[Ye]}],"break-after":[{"break-after":A()}],"break-before":[{"break-before":A()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...X(),W]}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:D()}],"overscroll-x":[{"overscroll-x":D()}],"overscroll-y":[{"overscroll-y":D()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[b]}],"inset-x":[{"inset-x":[b]}],"inset-y":[{"inset-y":[b]}],start:[{start:[b]}],end:[{end:[b]}],top:[{top:[b]}],right:[{right:[b]}],bottom:[{bottom:[b]}],left:[{left:[b]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Ft,W]}],basis:[{basis:G()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",W]}],grow:[{grow:T()}],shrink:[{shrink:T()}],order:[{order:["first","last","none",Ft,W]}],"grid-cols":[{"grid-cols":[At]}],"col-start-end":[{col:["auto",{span:["full",Ft,W]},W]}],"col-start":[{"col-start":V()}],"col-end":[{"col-end":V()}],"grid-rows":[{"grid-rows":[At]}],"row-start-end":[{row:["auto",{span:[Ft,W]},W]}],"row-start":[{"row-start":V()}],"row-end":[{"row-end":V()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",W]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",W]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...L()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...L(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...L(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[_]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[_]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",W,n]}],"min-w":[{"min-w":[W,n,"min","max","fit"]}],"max-w":[{"max-w":[W,n,"none","full","min","max","fit","prose",{screen:[Ye]},Ye]}],h:[{h:[W,n,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[W,n,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[W,n,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[W,n,"auto","min","max","fit"]}],"font-size":[{text:["base",Ye,Xe]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Nn]}],"font-family":[{font:[At]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",W]}],"line-clamp":[{"line-clamp":["none",xt,Nn]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Be,W]}],"list-image":[{"list-image":["none",W]}],"list-style-type":[{list:["none","disc","decimal",W]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...M(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Be,Xe]}],"underline-offset":[{"underline-offset":["auto",Be,W]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:k()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",W]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",W]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...X(),ei]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Ql]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},ni]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[h]}],"gradient-via-pos":[{via:[h]}],"gradient-to-pos":[{to:[h]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[i]}],"border-w-x":[{"border-x":[i]}],"border-w-y":[{"border-y":[i]}],"border-w-s":[{"border-s":[i]}],"border-w-e":[{"border-e":[i]}],"border-w-t":[{"border-t":[i]}],"border-w-r":[{"border-r":[i]}],"border-w-b":[{"border-b":[i]}],"border-w-l":[{"border-l":[i]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...M(),"hidden"]}],"divide-x":[{"divide-x":[i]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[i]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:M()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...M()]}],"outline-offset":[{"outline-offset":[Be,W]}],"outline-w":[{outline:[Be,Xe]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:q()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[Be,Xe]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Ye,ri]}],"shadow-color":[{shadow:[At]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...I(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":I()}],filter:[{filter:["","none"]}],blur:[{blur:[t]}],brightness:[{brightness:[r]}],contrast:[{contrast:[c]}],"drop-shadow":[{"drop-shadow":["","none",Ye,W]}],grayscale:[{grayscale:[d]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[s]}],saturate:[{saturate:[w]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[t]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[c]}],"backdrop-grayscale":[{"backdrop-grayscale":[d]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[s]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",W]}],duration:[{duration:j()}],ease:[{ease:["linear","in","out","in-out",W]}],delay:[{delay:j()}],animate:[{animate:["none","spin","ping","pulse","bounce",W]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[C]}],"scale-x":[{"scale-x":[C]}],"scale-y":[{"scale-y":[C]}],rotate:[{rotate:[Ft,W]}],"translate-x":[{"translate-x":[R]}],"translate-y":[{"translate-y":[R]}],"skew-x":[{"skew-x":[N]}],"skew-y":[{"skew-y":[N]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",W]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",W]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":k()}],"scroll-mx":[{"scroll-mx":k()}],"scroll-my":[{"scroll-my":k()}],"scroll-ms":[{"scroll-ms":k()}],"scroll-me":[{"scroll-me":k()}],"scroll-mt":[{"scroll-mt":k()}],"scroll-mr":[{"scroll-mr":k()}],"scroll-mb":[{"scroll-mb":k()}],"scroll-ml":[{"scroll-ml":k()}],"scroll-p":[{"scroll-p":k()}],"scroll-px":[{"scroll-px":k()}],"scroll-py":[{"scroll-py":k()}],"scroll-ps":[{"scroll-ps":k()}],"scroll-pe":[{"scroll-pe":k()}],"scroll-pt":[{"scroll-pt":k()}],"scroll-pr":[{"scroll-pr":k()}],"scroll-pb":[{"scroll-pb":k()}],"scroll-pl":[{"scroll-pl":k()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",W]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Be,Xe,Nn]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},si=$l(ii);function O(...e){return si(ko(e))}const di=J("cursor-pointer inline-flex items-center justify-center whitespace-nowrap transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-hidden focus-visible:ring-3 focus-visible:ring-offset-2 relative m-0 border-0 font-sans !text-xs font-medium !leading-[20.15px] !rounded-lg",{variants:{variant:{default:"bg-white text-gray-700 hover:bg-[#f6f6f6] shadow-[inset_0px_0.5px_0px_#c6c6c6,_0px_1px_1px_#c6c6c6,_inset_0.5px_0px_0px_#c6c6c6,_inset_-0.5px_0px_0px_#c6c6c6] hover:shadow-[inset_0px_0.5px_0px_#c6c6c6,_0px_1px_0px_#c6c6c6,_inset_0.5px_0px_0px_#c6c6c6,_inset_-0.5px_0px_0px_#c6c6c6] focus-visible:ring-gray-500/20",primary:"bg-gray-900 text-white hover:bg-gray-800 focus-visible:ring-gray-900/20",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus-visible:ring-gray-500/20",tertiary:"!border !border-gray-300 bg-white text-gray-900 hover:bg-gray-50 focus-visible:ring-gray-500/20",plain:"text-blue-600 hover:text-blue-700 hover:underline focus-visible:ring-blue-600/20 bg-transparent !shadow-none",monochromePlain:"text-gray-600 hover:text-gray-700 hover:underline focus-visible:ring-gray-500/20 bg-transparent !shadow-none"},size:{micro:"!h-5 !px-2 !py-0.5 gap-1 text-xs",slim:"!h-6 !px-2.5 !py-1 gap-1.5",medium:"!h-6 !px-4 !py-2 gap-2",large:"!h-7 !px-5 !py-2.5 gap-2.5 text-sm"},tone:{default:"",success:"bg-green-700 text-white hover:bg-green-800 focus-visible:ring-green-700/20",critical:"bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-600/20"},textAlign:{start:"justify-start text-left",center:"justify-center text-center",end:"justify-end text-right",left:"justify-start text-left",right:"justify-end text-right"},fullWidth:{true:"w-full",false:""},pressed:{true:"bg-accent text-accent-foreground",false:""}},defaultVariants:{variant:"default",size:"medium",tone:"default",textAlign:"center",fullWidth:!1,pressed:!1}}),ci=e=>e?e===!0||e==="down"?g.jsx(rr,{className:"size-4"}):e==="up"?g.jsx(lr,{className:"size-4"}):e==="select"?g.jsx(Po,{className:"size-4"}):null:null,Vr=()=>g.jsx(Ao,{className:"size-4 animate-spin"}),ae=p.forwardRef(({children:e,size:n="medium",textAlign:t="center",fullWidth:r=!1,disclosure:o,removeUnderline:a,icon:l,dataPrimaryLink:i,tone:c="default",variant:d="default",id:u,url:s,external:f,target:m,download:h,submit:b,disabled:x,loading:v,pressed:y=!1,accessibilityLabel:w,role:C,ariaControls:E,ariaExpanded:N,ariaDescribedBy:_,ariaChecked:R,onClick:D,onFocus:P,onBlur:G,onKeyPress:k,onKeyUp:q,onKeyDown:V,onMouseEnter:X,onTouchStart:M,onPointerDown:I,className:L,asChild:T=!1,...A},j)=>{const F=!!s,z=T?Rl:F?"a":"button",Y=v,U=x||Y,le=ci(o),ee=O(di({variant:d,size:n,tone:c==="critical"?"critical":c==="success"?"success":"default",textAlign:t,fullWidth:r,pressed:y}),a&&(d==="plain"||d==="monochromePlain")&&"no-underline hover:no-underline",L),ne={id:u,className:ee,disabled:U,"aria-label":w,"aria-controls":E,"aria-expanded":N,"aria-describedby":_,"aria-checked":R,"aria-pressed":y,"data-primary-link":i,role:C,onClick:U?void 0:D,onFocus:P,onBlur:G,onKeyPress:k,onKeyUp:q,onKeyDown:V,onMouseEnter:X,onTouchStart:M,onPointerDown:I,ref:j,...A};return F?g.jsxs(z,{...ne,href:s,target:f?"_blank":m,rel:f?"noopener noreferrer":void 0,download:h,children:[l&&!Y&&g.jsx("span",{className:"inline-flex shrink-0",children:l}),Y&&g.jsx(Vr,{}),e&&g.jsx("span",{className:"inline-flex",children:e}),le&&!Y&&g.jsx("span",{className:"inline-flex shrink-0",children:le})]}):g.jsxs(z,{...ne,type:b?"submit":"button",children:[l&&!Y&&g.jsx("span",{className:"inline-flex shrink-0",children:l}),Y&&g.jsx(Vr,{}),e&&g.jsx("span",{className:"inline-flex",children:e}),le&&!Y&&g.jsx("span",{className:"inline-flex shrink-0",children:le})]})});ae.displayName="Button";const ui=J("flex items-center",{variants:{gap:{extraTight:"gap-0.5",tight:"gap-2",loose:"gap-4"},variant:{segmented:"gap-0 [&>button]:ml-[-10px] [&>button]:[background-color:#ffffff] [&>button:hover]:[background-color:#f6f6f6] [&>button]:!rounded-none [&>button:first-child]:!rounded-tl-md [&>button:first-child]:!rounded-bl-md [&>button:last-child]:!rounded-tr-md [&>button:last-child]:!rounded-br-md"},fullWidth:{true:"w-full [&>*]:flex-1",false:""},connectedTop:{true:"[&>*]:rounded-t-none",false:""},noWrap:{true:"flex-nowrap",false:"flex-wrap"}},defaultVariants:{gap:"tight",fullWidth:!1,connectedTop:!1,noWrap:!1}}),$o=p.forwardRef(({children:e,gap:n="tight",variant:t,fullWidth:r=!1,connectedTop:o=!1,noWrap:a=!1,className:l,...i},c)=>{const d=O(ui({gap:t==="segmented"?void 0:n,variant:t,fullWidth:r,connectedTop:o,noWrap:a}),l);return g.jsx("div",{className:d,ref:c,...i,children:e})});$o.displayName="ButtonGroup";const gi=e=>e&&{bg:"bg-[rgb(241,241,241)]","bg-inverse":"bg-[rgb(26,26,26)]","bg-surface":"bg-white","bg-surface-hover":"bg-[rgb(247,247,247)]","bg-surface-active":"bg-[rgb(243,243,243)]","bg-surface-selected":"bg-[rgb(241,241,241)]","bg-surface-disabled":"bg-black/5","bg-surface-secondary":"bg-[rgb(247,247,247)]","bg-surface-secondary-hover":"bg-[rgb(241,241,241)]","bg-surface-secondary-active":"bg-[rgb(235,235,235)]","bg-surface-secondary-selected":"bg-[rgb(235,235,235)]","bg-surface-tertiary":"bg-[rgb(243,243,243)]","bg-surface-tertiary-hover":"bg-[rgb(235,235,235)]","bg-surface-tertiary-active":"bg-[rgb(227,227,227)]","bg-surface-brand":"bg-[rgb(227,227,227)]","bg-surface-brand-hover":"bg-[rgb(235,235,235)]","bg-surface-brand-active":"bg-[rgb(241,241,241)]","bg-surface-brand-selected":"bg-[rgb(241,241,241)]","bg-surface-info":"bg-[rgb(234,244,255)]","bg-surface-info-hover":"bg-[rgb(224,240,255)]","bg-surface-info-active":"bg-[rgb(202,230,255)]","bg-surface-success":"bg-[rgb(205,254,212)]","bg-surface-success-hover":"bg-[rgb(175,254,191)]","bg-surface-success-active":"bg-[rgb(146,252,172)]","bg-surface-caution":"bg-[rgb(255,248,219)]","bg-surface-caution-hover":"bg-[rgb(255,244,191)]","bg-surface-caution-active":"bg-[rgb(255,239,157)]","bg-surface-warning":"bg-[rgb(255,241,227)]","bg-surface-warning-hover":"bg-[rgb(255,235,213)]","bg-surface-warning-active":"bg-[rgb(255,228,198)]","bg-surface-critical":"bg-[rgb(254,232,235)]","bg-surface-critical-hover":"bg-[rgb(254,225,230)]","bg-surface-critical-active":"bg-[rgb(254,217,223)]","bg-surface-emphasis":"bg-[rgb(240,242,255)]","bg-surface-emphasis-hover":"bg-[rgb(234,237,255)]","bg-surface-emphasis-active":"bg-[rgb(226,231,255)]","bg-surface-magic":"bg-[rgb(248,247,255)]","bg-surface-magic-hover":"bg-[rgb(243,241,255)]","bg-surface-magic-active":"bg-[rgb(233,229,255)]","bg-surface-inverse":"bg-[rgb(48,48,48)]","bg-surface-transparent":"bg-transparent","bg-fill":"bg-white","bg-fill-hover":"bg-[rgb(250,250,250)]","bg-fill-active":"bg-[rgb(247,247,247)]","bg-fill-selected":"bg-[rgb(204,204,204)]","bg-fill-disabled":"bg-black/5","bg-fill-secondary":"bg-[rgb(241,241,241)]","bg-fill-secondary-hover":"bg-[rgb(235,235,235)]","bg-fill-secondary-active":"bg-[rgb(227,227,227)]","bg-fill-secondary-selected":"bg-[rgb(227,227,227)]","bg-fill-tertiary":"bg-[rgb(227,227,227)]","bg-fill-tertiary-hover":"bg-[rgb(212,212,212)]","bg-fill-tertiary-active":"bg-[rgb(204,204,204)]","bg-fill-brand":"bg-[rgb(48,48,48)]","bg-fill-brand-hover":"bg-[rgb(26,26,26)]","bg-fill-brand-active":"bg-[rgb(26,26,26)]","bg-fill-brand-selected":"bg-[rgb(48,48,48)]","bg-fill-brand-disabled":"bg-black/[0.17]","bg-fill-info":"bg-[rgb(145,208,255)]","bg-fill-info-hover":"bg-[rgb(81,192,255)]","bg-fill-info-active":"bg-[rgb(0,148,213)]","bg-fill-info-secondary":"bg-[rgb(213,235,255)]","bg-fill-success":"bg-[rgb(4,123,93)]","bg-fill-success-hover":"bg-[rgb(3,94,76)]","bg-fill-success-active":"bg-[rgb(1,75,64)]","bg-fill-success-secondary":"bg-[rgb(175,254,191)]","bg-fill-warning":"bg-[rgb(255,184,0)]","bg-fill-warning-hover":"bg-[rgb(229,165,0)]","bg-fill-warning-active":"bg-[rgb(178,132,0)]","bg-fill-warning-secondary":"bg-[rgb(255,214,164)]","bg-fill-caution":"bg-[rgb(255,230,0)]","bg-fill-caution-hover":"bg-[rgb(234,211,0)]","bg-fill-caution-active":"bg-[rgb(225,203,0)]","bg-fill-caution-secondary":"bg-[rgb(255,235,120)]","bg-fill-critical":"bg-[rgb(199,10,36)]","bg-fill-critical-hover":"bg-[rgb(163,10,36)]","bg-fill-critical-active":"bg-[rgb(142,11,33)]","bg-fill-critical-selected":"bg-[rgb(142,11,33)]","bg-fill-critical-secondary":"bg-[rgb(254,209,215)]","bg-fill-emphasis":"bg-[rgb(0,91,211)]","bg-fill-emphasis-hover":"bg-[rgb(0,66,153)]","bg-fill-emphasis-active":"bg-[rgb(0,46,106)]","bg-fill-magic":"bg-[rgb(128,81,255)]","bg-fill-magic-secondary":"bg-[rgb(233,229,255)]","bg-fill-magic-secondary-hover":"bg-[rgb(228,222,255)]","bg-fill-magic-secondary-active":"bg-[rgb(223,217,255)]","bg-fill-inverse":"bg-[rgb(48,48,48)]","bg-fill-inverse-hover":"bg-[rgb(74,74,74)]","bg-fill-inverse-active":"bg-[rgb(97,97,97)]","bg-fill-transparent":"bg-black/[0.02]","bg-fill-transparent-hover":"bg-black/5","bg-fill-transparent-active":"bg-black/[0.08]","bg-fill-transparent-selected":"bg-black/[0.08]","bg-fill-transparent-secondary":"bg-black/[0.06]","bg-fill-transparent-secondary-hover":"bg-black/[0.08]","bg-fill-transparent-secondary-active":"bg-black/[0.11]"}[e]||"",zr=e=>e?e==="transparent"?"border-transparent":{border:"border-[rgb(227,227,227)]","border-hover":"border-[rgb(204,204,204)]","border-disabled":"border-[rgb(235,235,235)]","border-secondary":"border-[rgb(235,235,235)]","border-tertiary":"border-[rgb(204,204,204)]","border-focus":"border-[rgb(0,91,211)]","border-brand":"border-[rgb(227,227,227)]","border-info":"border-[rgb(168,216,255)]","border-success":"border-[rgb(146,252,172)]","border-caution":"border-[rgb(255,235,120)]","border-warning":"border-[rgb(255,200,121)]","border-critical":"border-[rgb(254,193,199)]","border-critical-secondary":"border-[rgb(142,11,33)]","border-emphasis":"border-[rgb(0,91,211)]","border-emphasis-hover":"border-[rgb(0,66,153)]","border-emphasis-active":"border-[rgb(0,46,106)]","border-magic":"border-[rgb(228,222,255)]","border-magic-secondary":"border-[rgb(148,116,255)]","border-magic-secondary-hover":"border-[rgb(128,81,255)]","border-inverse":"border-[rgb(97,97,97)]","border-inverse-hover":"border-[rgb(204,204,204)]","border-inverse-active":"border-[rgb(227,227,227)]"}[e]||"":"",fi=e=>e&&{text:"text-[rgb(48,48,48)]","text-secondary":"text-[rgb(97,97,97)]","text-disabled":"text-[rgb(181,181,181)]","text-link":"text-[rgb(0,91,211)]","text-link-hover":"text-[rgb(0,66,153)]","text-link-active":"text-[rgb(0,46,106)]","text-brand":"text-[rgb(74,74,74)]","text-brand-hover":"text-[rgb(48,48,48)]","text-brand-on-bg-fill":"text-white","text-brand-on-bg-fill-hover":"text-[rgb(227,227,227)]","text-brand-on-bg-fill-active":"text-[rgb(204,204,204)]","text-brand-on-bg-fill-disabled":"text-white","text-info":"text-[rgb(0,58,90)]","text-info-hover":"text-[rgb(0,58,90)]","text-info-active":"text-[rgb(0,33,51)]","text-info-secondary":"text-[rgb(0,124,180)]","text-info-on-bg-fill":"text-[rgb(0,33,51)]","text-success":"text-[rgb(1,75,64)]","text-success-hover":"text-[rgb(7,54,48)]","text-success-active":"text-[rgb(2,38,34)]","text-success-secondary":"text-[rgb(4,123,93)]","text-success-on-bg-fill":"text-[rgb(250,255,251)]","text-caution":"text-[rgb(79,71,0)]","text-caution-hover":"text-[rgb(51,46,0)]","text-caution-active":"text-[rgb(31,28,0)]","text-caution-secondary":"text-[rgb(130,117,0)]","text-caution-on-bg-fill":"text-[rgb(51,46,0)]","text-warning":"text-[rgb(94,66,0)]","text-warning-hover":"text-[rgb(65,45,0)]","text-warning-active":"text-[rgb(37,26,0)]","text-warning-secondary":"text-[rgb(149,111,0)]","text-warning-on-bg-fill":"text-[rgb(37,26,0)]","text-critical":"text-[rgb(142,11,33)]","text-critical-hover":"text-[rgb(95,7,22)]","text-critical-active":"text-[rgb(47,4,11)]","text-critical-secondary":"text-[rgb(199,10,36)]","text-critical-on-bg-fill":"text-[rgb(255,250,251)]","text-emphasis":"text-[rgb(0,91,211)]","text-emphasis-hover":"text-[rgb(0,66,153)]","text-emphasis-active":"text-[rgb(0,46,106)]","text-emphasis-on-bg-fill":"text-[rgb(252,253,255)]","text-emphasis-on-bg-fill-hover":"text-[rgb(226,231,255)]","text-emphasis-on-bg-fill-active":"text-[rgb(213,220,255)]","text-magic":"text-[rgb(87,0,209)]","text-magic-secondary":"text-[rgb(113,38,255)]","text-magic-on-bg-fill":"text-[rgb(253,253,255)]","text-inverse":"text-[rgb(227,227,227)]","text-inverse-secondary":"text-[rgb(181,181,181)]","text-link-inverse":"text-[rgb(197,208,255)]"}[e]||"",Br=e=>e&&{0:"0","025":"px","050":"0.5",100:"1",150:"1.5",200:"2",300:"3",400:"4",500:"5",600:"6",800:"8",1e3:"10",1200:"12",1600:"16",2e3:"20",2400:"24",2800:"28",3200:"32"}[e]||"",pi=e=>e&&{0:"rounded-none","050":"rounded-sm",100:"rounded",150:"rounded-md",200:"rounded-lg",300:"rounded-xl",400:"rounded-2xl",500:"rounded-3xl",750:"rounded-[30px]",full:"rounded-full"}[e]||"",Hr=e=>e&&{0:"border-0","0165":"border-[0.66px]","025":"border","050":"border-2",100:"border-4"}[e]||"",mi=e=>e&&{0:"shadow-none",100:"shadow-[0px_1px_0px_0px_rgba(26,26,26,0.07)]",200:"shadow-[0px_3px_1px_-1px_rgba(26,26,26,0.07)]",300:"shadow-[0px_4px_6px_-2px_rgba(26,26,26,0.20)]",400:"shadow-[0px_8px_16px_-4px_rgba(26,26,26,0.22)]",500:"shadow-[0px_12px_20px_-8px_rgba(26,26,26,0.24)]",600:"shadow-[0px_20px_20px_-8px_rgba(26,26,26,0.28)]","bevel-100":"shadow-[1px_0px_0px_0px_rgba(0,0,0,0.13)_inset,_-1px_0px_0px_0px_rgba(0,0,0,0.13)_inset,_0px_-1px_0px_0px_rgba(0,0,0,0.17)_inset,_0px_1px_0px_0px_rgba(204,204,204,0.5)_inset]","inset-100":"shadow-[0px_1px_2px_0px_rgba(26,26,26,0.15)_inset,_0px_1px_1px_0px_rgba(26,26,26,0.15)_inset]","inset-200":"shadow-[0px_2px_1px_0px_rgba(26,26,26,0.20)_inset,_1px_0px_1px_0px_rgba(26,26,26,0.12)_inset,_-1px_0px_1px_0px_rgba(26,26,26,0.12)_inset]",button:"shadow-[0px_-1px_0px_0px_#b5b5b5_inset,_0px_0px_0px_1px_rgba(0,0,0,0.1)_inset,_0px_0.5px_0px_1.5px_#FFF_inset]","button-hover":"shadow-[0px_1px_0px_0px_#EBEBEB_inset,_-1px_0px_0px_0px_#EBEBEB_inset,_1px_0px_0px_0px_#EBEBEB_inset,_0px_-1px_0px_0px_#CCC_inset]","button-inset":"shadow-[-1px_0px_1px_0px_rgba(26,26,26,0.122)_inset,_1px_0px_1px_0px_rgba(26,26,26,0.122)_inset,_0px_2px_1px_0px_rgba(26,26,26,0.2)_inset]","button-primary":"shadow-[0px_-1px_0px_1px_rgba(0,0,0,0.8)_inset,_0px_0px_0px_1px_rgba(48,48,48,1)_inset,_0px_0.5px_0px_1.5px_rgba(255,255,255,0.25)_inset]","button-primary-hover":"shadow-[0px_1px_0px_0px_rgba(255,255,255,0.24)_inset,_1px_0px_0px_0px_rgba(255,255,255,0.20)_inset,_-1px_0px_0px_0px_rgba(255,255,255,0.20)_inset,_0px_-1px_0px_0px_#000_inset,_0px_-1px_0px_1px_#1A1A1A]","button-primary-inset":"shadow-[0px_3px_0px_0px_rgb(0,0,0)_inset]","button-primary-critical":"shadow-[0px_-1px_0px_1px_rgba(142,31,11,0.8)_inset,_0px_0px_0px_1px_rgba(181,38,11,0.8)_inset,_0px_0.5px_0px_1.5px_rgba(255,255,255,0.349)_inset]","button-primary-critical-hover":"shadow-[0px_1px_0px_0px_rgba(255,255,255,0.48)_inset,_1px_0px_0px_0px_rgba(255,255,255,0.20)_inset,_-1px_0px_0px_0px_rgba(255,255,255,0.20)_inset,_0px_-1.5px_0px_0px_rgba(0,0,0,0.25)_inset]","button-primary-critical-inset":"shadow-[-1px_0px_1px_0px_rgba(0,0,0,0.2)_inset,_1px_0px_1px_0px_rgba(0,0,0,0.2)_inset,_0px_2px_0px_0px_rgba(0,0,0,0.6)_inset]","button-primary-success":"shadow-[0px_-1px_0px_1px_rgba(12,81,50,0.8)_inset,_0px_0px_0px_1px_rgba(19,111,69,0.8)_inset,_0px_0.5px_0px_1.5px_rgba(255,255,255,0.251)_inset]","button-primary-success-hover":"shadow-[0px_1px_0px_0px_rgba(255,255,255,0.48)_inset,_1px_0px_0px_0px_rgba(255,255,255,0.20)_inset,_-1px_0px_0px_0px_rgba(255,255,255,0.20)_inset,_0px_-1.5px_0px_0px_rgba(0,0,0,0.25)_inset]","button-primary-success-inset":"shadow-[-1px_0px_1px_0px_rgba(0,0,0,0.2)_inset,_1px_0px_1px_0px_rgba(0,0,0,0.2)_inset,_0px_2px_0px_0px_rgba(0,0,0,0.6)_inset]","border-inset":"shadow-[0px_0px_0px_1px_rgba(0,0,0,0.08)_inset]"}[e]||"",Re=(e,n="p")=>{if(!e)return"";if(typeof e=="string"){const r=Br(e);return r?`${n}-${r}`:""}const t=[];return Object.entries(e).forEach(([r,o])=>{const a=Br(o);if(a){const l=r==="xs"?"":`${r}:`;t.push(`${l}${n}-${a}`)}}),t.join(" ")},Yt=(e,n)=>{const t={0:"0","0165":"[0.66px]","025":"","050":"2",100:"4"}[e];return t?`border-${n}-${t}`:`border-${n}`},Kt=(e,n)=>{const t={0:"none","050":"sm",100:"",150:"md",200:"lg",300:"xl",400:"2xl",500:"3xl",750:"[30px]",full:"full"}[e];return t?`rounded-${n}-${t}`:`rounded-${n}`},bi=J("",{variants:{visuallyHidden:{true:"sr-only",false:""},printHidden:{true:"print:hidden",false:""}},defaultVariants:{visuallyHidden:!1,printHidden:!1}}),hi=p.forwardRef(({children:e,as:n="div",background:t,borderColor:r,borderStyle:o,borderRadius:a,borderEndStartRadius:l,borderEndEndRadius:i,borderStartStartRadius:c,borderStartEndRadius:d,borderWidth:u,borderBlockStartWidth:s,borderBlockEndWidth:f,borderInlineStartWidth:m,borderInlineEndWidth:h,color:b,id:x,minHeight:v,minWidth:y,maxWidth:w,overflowX:C,overflowY:E,padding:N,paddingBlock:_,paddingBlockStart:R,paddingBlockEnd:D,paddingInline:P,paddingInlineStart:G,paddingInlineEnd:k,role:q,shadow:V,tabIndex:X,width:M,position:I,insetBlockStart:L,insetBlockEnd:T,insetInlineStart:A,insetInlineEnd:j,opacity:F,outlineColor:z,outlineStyle:Y,outlineWidth:U,printHidden:le=!1,visuallyHidden:ee=!1,zIndex:ne,className:de,...Ve},Pe)=>{const Q=n,ie=O(bi({visuallyHidden:ee,printHidden:le}),gi(t),zr(r),o==="dashed"?"border-dashed":"border-solid",pi(a),Hr(u),s&&Yt(s,"t"),f&&Yt(f,"b"),m&&Yt(m,"l"),h&&Yt(h,"r"),l&&Kt(l,"bl"),i&&Kt(i,"br"),c&&Kt(c,"tl"),d&&Kt(d,"tr"),fi(b),C&&`overflow-x-${C}`,E&&`overflow-y-${E}`,Re(N,"p"),Re(_,"py"),Re(R,"pt"),Re(D,"pb"),Re(P,"px"),Re(G,"pl"),Re(k,"pr"),mi(V),I&&`${I}`,Re(L,"top"),Re(T,"bottom"),Re(A,"left"),Re(j,"right"),z&&zr(z).replace("border-","outline-"),Y==="dashed"?"outline-dashed":Y==="solid"?"outline-solid":"",U&&Hr(U).replace("border-","outline-"),de),ye={...v&&{minHeight:v},...y&&{minWidth:y},...w&&{maxWidth:w},...M&&{width:M},...F&&{opacity:F},...ne&&{zIndex:ne}};return g.jsx(Q,{ref:Pe,className:ie,style:Object.keys(ye).length>0?ye:void 0,id:x,role:q,tabIndex:X,...Ve,children:e})});hi.displayName="Box";const vi=J("",{variants:{variant:{heading3xl:"text-4xl leading-tight sm:text-5xl sm:leading-tight",heading2xl:"text-3xl leading-tight sm:text-4xl sm:leading-tight",headingXl:"text-2xl leading-tight sm:text-3xl sm:leading-tight",headingLg:"text-xl leading-normal sm:text-2xl sm:leading-normal",headingMd:"text-sm leading-normal",headingSm:"text-xs leading-normal",headingXs:"text-xs leading-tight",bodyLg:"text-sm leading-normal",bodyMd:"text-xs leading-normal",bodySm:"text-xs leading-tight",bodyXs:"text-xs leading-tight"},alignment:{start:"text-left",center:"text-center",end:"text-right",justify:"text-justify"},tone:{base:"text-gray-900",disabled:"text-gray-400",inherit:"text-inherit",success:"text-green-600",critical:"text-red-600",caution:"text-yellow-600",subdued:"text-gray-600","text-inverse":"text-white","text-inverse-secondary":"text-gray-200",magic:"text-purple-600","magic-subdued":"text-purple-400"},fontWeight:{regular:"font-normal",medium:"font-medium",semibold:"font-semibold",bold:"font-bold"},breakWord:{true:"break-words",false:""},truncate:{true:"truncate",false:""},numeric:{true:"font-mono tabular-nums",false:""},visuallyHidden:{true:"sr-only",false:""},textDecorationLine:{"line-through":"line-through",none:""}},defaultVariants:{variant:"bodyMd",alignment:"start",tone:"base",fontWeight:"regular",breakWord:!1,truncate:!1,numeric:!1,visuallyHidden:!1,textDecorationLine:"none"}}),we=p.forwardRef(({children:e,as:n="span",alignment:t,breakWord:r=!1,tone:o,fontWeight:a,id:l,numeric:i=!1,truncate:c=!1,variant:d="bodyMd",visuallyHidden:u=!1,textDecorationLine:s,className:f,...m},h)=>g.jsx(n,{ref:h,id:l,className:O(vi({variant:d,alignment:t,tone:o,fontWeight:(()=>{if(a)return a;switch(d){case"heading3xl":case"heading2xl":case"headingXl":return"bold";case"headingLg":case"headingMd":case"headingSm":case"headingXs":return"semibold";default:return"regular"}})(),breakWord:r,truncate:c,numeric:i,visuallyHidden:u,textDecorationLine:s?"line-through":"none"}),f),...m,children:e}));we.displayName="Text";const xi=J(["relative flex items-center w-full","font-sans text-[13px] leading-[20px] font-normal","text-[rgb(48,48,48)] placeholder:text-[rgb(97,97,97)]","bg-white border border-[rgb(138,143,148)] border-t-[rgb(137,143,148)]","rounded-lg","px-3 py-[6px]","h-8 min-h-8","flex-grow flex-shrink","appearance-none outline-hidden cursor-text","focus-visible:outline-hidden focus-visible:border-[rgb(0,123,255)] focus-visible:border-2","focus-visible:bg-white focus-visible:ring-3 focus-visible:ring-[rgb(0,123,255)] focus-visible:ring-offset-1","hover:border-[rgb(97,97,97)] hover:bg-[rgb(247,247,247)]","disabled:cursor-default disabled:text-[rgb(181,181,181)] disabled:bg-[rgb(247,247,247)] disabled:border-transparent","file:border-0 file:bg-transparent file:text-[13px] file:font-normal"],{variants:{size:{slim:"h-7 min-h-7 py-[2px] text-[13px] leading-[20px]",medium:"h-8 min-h-8 py-[6px] text-[13px] leading-[20px]"},variant:{inherit:"",borderless:["border-0 shadow-none min-h-8","focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-0","hover:border-0 hover:bg-transparent"]},align:{left:"text-left",center:"text-center",right:"text-right"},tone:{magic:["text-[rgb(128,81,255)] bg-[rgb(248,247,255)]","border-[rgb(153,124,255)]","focus-visible:border-[rgb(0,123,255)] focus-visible:text-[rgb(48,48,48)]","hover:bg-[rgb(243,240,255)] hover:border-[rgb(138,129,255)]"]},error:{true:["border-[rgb(253,75,146)] bg-[rgb(255,247,250)]","focus-visible:border-[rgb(0,123,255)] focus-visible:bg-white","hover:border-[rgb(253,75,146)] hover:bg-[rgb(255,247,250)]"],false:""},monospaced:{true:"font-mono",false:"font-sans"},multiline:{true:["min-h-[80px] resize-y overflow-auto","items-start py-[6px]","leading-[20px]"],false:"items-center"}},defaultVariants:{size:"medium",variant:"inherit",align:"left",error:!1,monospaced:!1,multiline:!1}}),yi=()=>{const[e]=p.useState(()=>Math.random().toString(36).substring(2,11));return e},wi=e=>{if(e)return typeof e=="number"?e:3},Ci=e=>{if(e)return e||typeof e=="number"&&e>0?{"aria-multiline":!0}:void 0},$r=e=>`${e}Label`,Gr=e=>`${e}HelpText`,Go=p.forwardRef(({prefix:e,suffix:n,verticalContent:t,placeholder:r,value:o="",helpText:a,label:l,labelAction:i,labelHidden:c=!1,disabled:d=!1,clearButton:u=!1,readOnly:s=!1,autoFocus:f=!1,focused:m,multiline:h=!1,error:b,connectedRight:x,connectedLeft:v,type:y="text",name:w,id:C,role:E,step:N,largeStep:_,autoComplete:R,max:D,maxLength:P,maxHeight:G,min:k,minLength:q,pattern:V,inputMode:X,spellCheck:M,ariaOwns:I,ariaControls:L,ariaExpanded:T,ariaActiveDescendant:A,ariaAutocomplete:j,showCharacterCount:F=!1,align:z="left",requiredIndicator:Y=!1,monospaced:U=!1,selectTextOnFocus:le=!1,suggestion:ee,variant:ne="inherit",size:de="medium",tone:Ve,autoSize:Pe=!1,loading:Q=!1,onClearButtonClick:ie,onChange:ye,onSpinnerChange:Bt,onFocus:Ge,onBlur:Ce,className:ft,...ce},at)=>{const Rn=yi(),K=C??Rn,[We,Ht]=p.useState(!!m),kt=p.useRef(null),lt=p.useRef(null),$t=p.useRef(null),it=p.useCallback(()=>h?lt.current:kt.current,[h]);p.useEffect(()=>{const Z=it();!Z||m===void 0||(m?Z.focus():Z.blur())},[m,it]),p.useEffect(()=>{const Z=kt.current;!Z||!(y==="text"||y==="tel"||y==="search"||y==="url"||y==="password")||!ee||Z.setSelectionRange(o.length,ee.length)},[We,o,y,ee]);const st=ee||o,Ue=y==="currency"?"text":y,Gt=O(xi({size:de,variant:ne,align:z,tone:Ve,error:!!b,monospaced:U,multiline:!!h}),Pe&&"w-auto min-w-0",ft),ze=p.useCallback(Z=>{ye&&ye(Z.currentTarget.value,K)},[ye,K]),En=p.useCallback(Z=>{if(Ht(!0),le&&!ee){const me=it();me==null||me.select()}Ge&&Ge(Z)},[le,ee,it,Ge]),jn=p.useCallback(Z=>{var me;Ht(!1),!((me=$t.current)!=null&&me.contains(Z==null?void 0:Z.relatedTarget))&&Ce&&Ce(Z)},[Ce]),Wt=p.useCallback(()=>{ie&&ie(K)},[ie,K]),qe=[];b&&qe.push(`${K}Error`),a&&qe.push(Gr(K)),F&&qe.push(`${K}-CharacterCounter`);const dt=[];e&&dt.push(`${K}-Prefix`),n&&dt.push(`${K}-Suffix`),t&&dt.push(`${K}-VerticalContent`),dt.unshift($r(K));let pt=null;if(F){const Z=st.length,me=P?`${Z}/${P}`:Z;pt=g.jsx("div",{id:`${K}-CharacterCounter`,className:"text-xs text-muted-foreground mt-1",children:me})}const Se=u&&st!==""?g.jsx("button",{type:"button",className:"absolute right-2 top-1/2 -translate-y-1/2 p-1 text-muted-foreground hover:text-foreground focus:outline-hidden focus:ring-3 focus:ring-ring focus:ring-offset-2 rounded-sm",onClick:Wt,tabIndex:-1,"aria-label":"Clear",children:g.jsx(Lo,{className:"h-4 w-4"})}):null,Ut=e?g.jsx("div",{id:`${K}-Prefix`,className:"flex items-center px-3 text-sm text-muted-foreground border-r border-input bg-muted/50",children:e}):null,qt=n?g.jsx("div",{id:`${K}-Suffix`,className:"flex items-center px-3 text-sm text-muted-foreground border-l border-input bg-muted/50",children:n}):null,Mt=Q?g.jsx("div",{className:"absolute right-2 top-1/2 -translate-y-1/2",children:g.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-foreground"})}):null,Xt=c?null:g.jsxs("label",{id:$r(K),htmlFor:K,className:O("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",Y&&"after:content-['*'] after:ml-1 after:text-destructive"),children:[l,i&&g.jsx("button",{type:"button",onClick:i.onAction,className:"ml-2 text-xs text-primary hover:underline focus:outline-hidden focus:underline",children:i.content})]}),$=a?g.jsx("div",{id:Gr(K),className:"text-xs text-muted-foreground mt-1",children:a}):null,re=b&&typeof b!="boolean"?g.jsx("div",{id:`${K}Error`,className:"text-xs text-destructive mt-1",role:"alert",children:b}):null,se=t?g.jsx("div",{id:`${K}-VerticalContent`,className:"text-sm text-muted-foreground mb-2",children:t}):null,ge={...G&&{maxHeight:G}},oe=p.createElement(h?"textarea":"input",{ref:p.useCallback(Z=>{h?lt.current=Z:kt.current=Z,typeof at=="function"?at(Z):at&&(at.current=Z)},[h,at]),id:K,name:w,type:Ue,value:st,placeholder:r,disabled:d,readOnly:s,autoFocus:f,role:E,min:k,max:D,step:N,minLength:q,maxLength:P,pattern:V,inputMode:X,spellCheck:M,autoComplete:R,rows:wi(h),size:Pe?1:void 0,style:Object.keys(ge).length>0?ge:void 0,className:Gt,"aria-describedby":qe.length?qe.join(" "):void 0,"aria-labelledby":dt.join(" "),"aria-invalid":!!b,"aria-owns":I,"aria-activedescendant":A,"aria-autocomplete":j,"aria-controls":L,"aria-expanded":T,"aria-required":Y,...Ci(h),onChange:ee?void 0:ze,onInput:ee?ze:void 0,onFocus:En,onBlur:jn,"data-1p-ignore":R==="off"||void 0,"data-lpignore":R==="off"||void 0,"data-form-type":R==="off"?"other":void 0,...ce});return g.jsxs("div",{ref:$t,className:"space-y-2",children:[Xt,v||x?g.jsxs("div",{className:"flex",children:[v,g.jsxs("div",{className:"flex-1 relative",children:[se,g.jsxs("div",{className:"relative flex",children:[Ut,oe,qt,Se,Mt]})]}),x]}):g.jsxs("div",{className:"relative",children:[se,g.jsxs("div",{className:"relative flex",children:[Ut,oe,qt,Se,Mt]})]}),re,$,pt]})});Go.displayName="TextField";function he(e,n,{checkForDefaultPrevented:t=!0}={}){return function(r){if(e==null||e(r),t===!1||!r.defaultPrevented)return n==null?void 0:n(r)}}function Si(e,n){const t=p.createContext(n),r=a=>{const{children:l,...i}=a,c=p.useMemo(()=>i,Object.values(i));return g.jsx(t.Provider,{value:c,children:l})};r.displayName=e+"Provider";function o(a){const l=p.useContext(t);if(l)return l;if(n!==void 0)return n;throw new Error(`\`${a}\` must be used within \`${e}\``)}return[r,o]}function dr(e,n=[]){let t=[];function r(a,l){const i=p.createContext(l),c=t.length;t=[...t,l];const d=s=>{var f;const{scope:m,children:h,...b}=s,x=((f=m==null?void 0:m[e])==null?void 0:f[c])||i,v=p.useMemo(()=>b,Object.values(b));return g.jsx(x.Provider,{value:v,children:h})};d.displayName=a+"Provider";function u(s,f){var m;const h=((m=f==null?void 0:f[e])==null?void 0:m[c])||i,b=p.useContext(h);if(b)return b;if(l!==void 0)return l;throw new Error(`\`${s}\` must be used within \`${a}\``)}return[d,u]}const o=()=>{const a=t.map(l=>p.createContext(l));return function(l){const i=(l==null?void 0:l[e])||a;return p.useMemo(()=>({[`__scope${e}`]:{...l,[e]:i}}),[l,i])}};return o.scopeName=e,[r,_i(o,...n)]}function _i(...e){const n=e[0];if(e.length===1)return n;const t=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(o){const a=r.reduce((l,{useScope:i,scopeName:c})=>{const d=i(o)[`__scope${c}`];return{...l,...d}},{});return p.useMemo(()=>({[`__scope${n.scopeName}`]:a}),[a])}};return t.scopeName=n.scopeName,t}var Ri=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],pe=Ri.reduce((e,n)=>{const t=hn(`Primitive.${n}`),r=p.forwardRef((o,a)=>{const{asChild:l,...i}=o,c=l?t:n;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),g.jsx(c,{...i,ref:a})});return r.displayName=`Primitive.${n}`,{...e,[n]:r}},{});function Ei(e,n){e&&Eo.flushSync(()=>e.dispatchEvent(n))}function Ct(e){const n=p.useRef(e);return p.useEffect(()=>{n.current=e}),p.useMemo(()=>(...t)=>{var r;return(r=n.current)==null?void 0:r.call(n,...t)},[])}function ji(e,n=globalThis==null?void 0:globalThis.document){const t=Ct(e);p.useEffect(()=>{const r=o=>{o.key==="Escape"&&t(o)};return n.addEventListener("keydown",r,{capture:!0}),()=>n.removeEventListener("keydown",r,{capture:!0})},[t,n])}var Ni="DismissableLayer",Un="dismissableLayer.update",ki="dismissableLayer.pointerDownOutside",Mi="dismissableLayer.focusOutside",Wr,Wo=p.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),cr=p.forwardRef((e,n)=>{const{disableOutsidePointerEvents:t=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:l,onDismiss:i,...c}=e,d=p.useContext(Wo),[u,s]=p.useState(null),f=(u==null?void 0:u.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,m]=p.useState({}),h=ke(n,_=>s(_)),b=Array.from(d.layers),[x]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),v=b.indexOf(x),y=u?b.indexOf(u):-1,w=d.layersWithOutsidePointerEventsDisabled.size>0,C=y>=v,E=Ai(_=>{const R=_.target,D=[...d.branches].some(P=>P.contains(R));!C||D||(o==null||o(_),l==null||l(_),_.defaultPrevented||i==null||i())},f),N=Ii(_=>{const R=_.target;[...d.branches].some(D=>D.contains(R))||(a==null||a(_),l==null||l(_),_.defaultPrevented||i==null||i())},f);return ji(_=>{y===d.layers.size-1&&(r==null||r(_),!_.defaultPrevented&&i&&(_.preventDefault(),i()))},f),p.useEffect(()=>{if(u)return t&&(d.layersWithOutsidePointerEventsDisabled.size===0&&(Wr=f.body.style.pointerEvents,f.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(u)),d.layers.add(u),Ur(),()=>{t&&d.layersWithOutsidePointerEventsDisabled.size===1&&(f.body.style.pointerEvents=Wr)}},[u,f,t,d]),p.useEffect(()=>()=>{u&&(d.layers.delete(u),d.layersWithOutsidePointerEventsDisabled.delete(u),Ur())},[u,d]),p.useEffect(()=>{const _=()=>m({});return document.addEventListener(Un,_),()=>document.removeEventListener(Un,_)},[]),g.jsx(pe.div,{...c,ref:h,style:{pointerEvents:w?C?"auto":"none":void 0,...e.style},onFocusCapture:he(e.onFocusCapture,N.onFocusCapture),onBlurCapture:he(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:he(e.onPointerDownCapture,E.onPointerDownCapture)})});cr.displayName=Ni;var Pi="DismissableLayerBranch",Fi=p.forwardRef((e,n)=>{const t=p.useContext(Wo),r=p.useRef(null),o=ke(n,r);return p.useEffect(()=>{const a=r.current;if(a)return t.branches.add(a),()=>{t.branches.delete(a)}},[t.branches]),g.jsx(pe.div,{...e,ref:o})});Fi.displayName=Pi;function Ai(e,n=globalThis==null?void 0:globalThis.document){const t=Ct(e),r=p.useRef(!1),o=p.useRef(()=>{});return p.useEffect(()=>{const a=i=>{if(i.target&&!r.current){let c=function(){Uo(ki,t,d,{discrete:!0})};const d={originalEvent:i};i.pointerType==="touch"?(n.removeEventListener("click",o.current),o.current=c,n.addEventListener("click",o.current,{once:!0})):c()}else n.removeEventListener("click",o.current);r.current=!1},l=window.setTimeout(()=>{n.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(l),n.removeEventListener("pointerdown",a),n.removeEventListener("click",o.current)}},[n,t]),{onPointerDownCapture:()=>r.current=!0}}function Ii(e,n=globalThis==null?void 0:globalThis.document){const t=Ct(e),r=p.useRef(!1);return p.useEffect(()=>{const o=a=>{a.target&&!r.current&&Uo(Mi,t,{originalEvent:a},{discrete:!1})};return n.addEventListener("focusin",o),()=>n.removeEventListener("focusin",o)},[n,t]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Ur(){const e=new CustomEvent(Un);document.dispatchEvent(e)}function Uo(e,n,t,{discrete:r}){const o=t.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:t});n&&o.addEventListener(e,n,{once:!0}),r?Ei(o,a):o.dispatchEvent(a)}var kn=0;function qo(){p.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??qr()),document.body.insertAdjacentElement("beforeend",e[1]??qr()),kn++,()=>{kn===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(n=>n.remove()),kn--}},[])}function qr(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Mn="focusScope.autoFocusOnMount",Pn="focusScope.autoFocusOnUnmount",Xr={bubbles:!1,cancelable:!0},Oi="FocusScope",ur=p.forwardRef((e,n)=>{const{loop:t=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...l}=e,[i,c]=p.useState(null),d=Ct(o),u=Ct(a),s=p.useRef(null),f=ke(n,b=>c(b)),m=p.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;p.useEffect(()=>{if(r){let b=function(w){if(m.paused||!i)return;const C=w.target;i.contains(C)?s.current=C:Ze(s.current,{select:!0})},x=function(w){if(m.paused||!i)return;const C=w.relatedTarget;C!==null&&(i.contains(C)||Ze(s.current,{select:!0}))},v=function(w){if(document.activeElement===document.body)for(const C of w)C.removedNodes.length>0&&Ze(i)};document.addEventListener("focusin",b),document.addEventListener("focusout",x);const y=new MutationObserver(v);return i&&y.observe(i,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",b),document.removeEventListener("focusout",x),y.disconnect()}}},[r,i,m.paused]),p.useEffect(()=>{if(i){Kr.add(m);const b=document.activeElement;if(!i.contains(b)){const x=new CustomEvent(Mn,Xr);i.addEventListener(Mn,d),i.dispatchEvent(x),x.defaultPrevented||(Di(Bi(Xo(i)),{select:!0}),document.activeElement===b&&Ze(i))}return()=>{i.removeEventListener(Mn,d),setTimeout(()=>{const x=new CustomEvent(Pn,Xr);i.addEventListener(Pn,u),i.dispatchEvent(x),x.defaultPrevented||Ze(b??document.body,{select:!0}),i.removeEventListener(Pn,u),Kr.remove(m)},0)}}},[i,d,u,m]);const h=p.useCallback(b=>{if(!t&&!r||m.paused)return;const x=b.key==="Tab"&&!b.altKey&&!b.ctrlKey&&!b.metaKey,v=document.activeElement;if(x&&v){const y=b.currentTarget,[w,C]=Li(y);w&&C?!b.shiftKey&&v===C?(b.preventDefault(),t&&Ze(w,{select:!0})):b.shiftKey&&v===w&&(b.preventDefault(),t&&Ze(C,{select:!0})):v===y&&b.preventDefault()}},[t,r,m.paused]);return g.jsx(pe.div,{tabIndex:-1,...l,ref:f,onKeyDown:h})});ur.displayName=Oi;function Di(e,{select:n=!1}={}){const t=document.activeElement;for(const r of e)if(Ze(r,{select:n}),document.activeElement!==t)return}function Li(e){const n=Xo(e),t=Yr(n,e),r=Yr(n.reverse(),e);return[t,r]}function Xo(e){const n=[],t=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;t.nextNode();)n.push(t.currentNode);return n}function Yr(e,n){for(const t of e)if(!Ti(t,{upTo:n}))return t}function Ti(e,{upTo:n}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(n!==void 0&&e===n)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Vi(e){return e instanceof HTMLInputElement&&"select"in e}function Ze(e,{select:n=!1}={}){if(e&&e.focus){const t=document.activeElement;e.focus({preventScroll:!0}),e!==t&&Vi(e)&&n&&e.select()}}var Kr=zi();function zi(){let e=[];return{add(n){const t=e[0];n!==t&&(t==null||t.pause()),e=Zr(e,n),e.unshift(n)},remove(n){var t;e=Zr(e,n),(t=e[0])==null||t.resume()}}}function Zr(e,n){const t=[...e],r=t.indexOf(n);return r!==-1&&t.splice(r,1),t}function Bi(e){return e.filter(n=>n.tagName!=="A")}var et=globalThis!=null&&globalThis.document?p.useLayoutEffect:()=>{},Hi=_o[" useId ".trim().toString()]||(()=>{}),$i=0;function sn(e){const[n,t]=p.useState(Hi());return et(()=>{t(r=>r??String($i++))},[e]),e||(n?`radix-${n}`:"")}const Gi=["top","right","bottom","left"],tt=Math.min,be=Math.max,gn=Math.round,Zt=Math.floor,De=e=>({x:e,y:e}),Wi={left:"right",right:"left",bottom:"top",top:"bottom"},Ui={start:"end",end:"start"};function qn(e,n,t){return be(e,tt(n,t))}function He(e,n){return typeof e=="function"?e(n):e}function $e(e){return e.split("-")[0]}function Et(e){return e.split("-")[1]}function gr(e){return e==="x"?"y":"x"}function fr(e){return e==="y"?"height":"width"}const qi=new Set(["top","bottom"]);function Oe(e){return qi.has($e(e))?"y":"x"}function pr(e){return gr(Oe(e))}function Xi(e,n,t){t===void 0&&(t=!1);const r=Et(e),o=pr(e),a=fr(o);let l=o==="x"?r===(t?"end":"start")?"right":"left":r==="start"?"bottom":"top";return n.reference[a]>n.floating[a]&&(l=fn(l)),[l,fn(l)]}function Yi(e){const n=fn(e);return[Xn(e),n,Xn(n)]}function Xn(e){return e.replace(/start|end/g,n=>Ui[n])}const Jr=["left","right"],Qr=["right","left"],Ki=["top","bottom"],Zi=["bottom","top"];function Ji(e,n,t){switch(e){case"top":case"bottom":return t?n?Qr:Jr:n?Jr:Qr;case"left":case"right":return n?Ki:Zi;default:return[]}}function Qi(e,n,t,r){const o=Et(e);let a=Ji($e(e),t==="start",r);return o&&(a=a.map(l=>l+"-"+o),n&&(a=a.concat(a.map(Xn)))),a}function fn(e){return e.replace(/left|right|bottom|top/g,n=>Wi[n])}function es(e){return{top:0,right:0,bottom:0,left:0,...e}}function Yo(e){return typeof e!="number"?es(e):{top:e,right:e,bottom:e,left:e}}function pn(e){const{x:n,y:t,width:r,height:o}=e;return{width:r,height:o,top:t,left:n,right:n+r,bottom:t+o,x:n,y:t}}function eo(e,n,t){let{reference:r,floating:o}=e;const a=Oe(n),l=pr(n),i=fr(l),c=$e(n),d=a==="y",u=r.x+r.width/2-o.width/2,s=r.y+r.height/2-o.height/2,f=r[i]/2-o[i]/2;let m;switch(c){case"top":m={x:u,y:r.y-o.height};break;case"bottom":m={x:u,y:r.y+r.height};break;case"right":m={x:r.x+r.width,y:s};break;case"left":m={x:r.x-o.width,y:s};break;default:m={x:r.x,y:r.y}}switch(Et(n)){case"start":m[l]-=f*(t&&d?-1:1);break;case"end":m[l]+=f*(t&&d?-1:1);break}return m}const ts=async(e,n,t)=>{const{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:l}=t,i=a.filter(Boolean),c=await(l.isRTL==null?void 0:l.isRTL(n));let d=await l.getElementRects({reference:e,floating:n,strategy:o}),{x:u,y:s}=eo(d,r,c),f=r,m={},h=0;for(let b=0;b<i.length;b++){const{name:x,fn:v}=i[b],{x:y,y:w,data:C,reset:E}=await v({x:u,y:s,initialPlacement:r,placement:f,strategy:o,middlewareData:m,rects:d,platform:l,elements:{reference:e,floating:n}});u=y??u,s=w??s,m={...m,[x]:{...m[x],...C}},E&&h<=50&&(h++,typeof E=="object"&&(E.placement&&(f=E.placement),E.rects&&(d=E.rects===!0?await l.getElementRects({reference:e,floating:n,strategy:o}):E.rects),{x:u,y:s}=eo(d,f,c)),b=-1)}return{x:u,y:s,placement:f,strategy:o,middlewareData:m}};async function Lt(e,n){var t;n===void 0&&(n={});const{x:r,y:o,platform:a,rects:l,elements:i,strategy:c}=e,{boundary:d="clippingAncestors",rootBoundary:u="viewport",elementContext:s="floating",altBoundary:f=!1,padding:m=0}=He(n,e),h=Yo(m),b=i[f?s==="floating"?"reference":"floating":s],x=pn(await a.getClippingRect({element:(t=await(a.isElement==null?void 0:a.isElement(b)))==null||t?b:b.contextElement||await(a.getDocumentElement==null?void 0:a.getDocumentElement(i.floating)),boundary:d,rootBoundary:u,strategy:c})),v=s==="floating"?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await(a.getOffsetParent==null?void 0:a.getOffsetParent(i.floating)),w=await(a.isElement==null?void 0:a.isElement(y))?await(a.getScale==null?void 0:a.getScale(y))||{x:1,y:1}:{x:1,y:1},C=pn(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:v,offsetParent:y,strategy:c}):v);return{top:(x.top-C.top+h.top)/w.y,bottom:(C.bottom-x.bottom+h.bottom)/w.y,left:(x.left-C.left+h.left)/w.x,right:(C.right-x.right+h.right)/w.x}}const ns=e=>({name:"arrow",options:e,async fn(n){const{x:t,y:r,placement:o,rects:a,platform:l,elements:i,middlewareData:c}=n,{element:d,padding:u=0}=He(e,n)||{};if(d==null)return{};const s=Yo(u),f={x:t,y:r},m=pr(o),h=fr(m),b=await l.getDimensions(d),x=m==="y",v=x?"top":"left",y=x?"bottom":"right",w=x?"clientHeight":"clientWidth",C=a.reference[h]+a.reference[m]-f[m]-a.floating[h],E=f[m]-a.reference[m],N=await(l.getOffsetParent==null?void 0:l.getOffsetParent(d));let _=N?N[w]:0;(!_||!await(l.isElement==null?void 0:l.isElement(N)))&&(_=i.floating[w]||a.floating[h]);const R=C/2-E/2,D=_/2-b[h]/2-1,P=tt(s[v],D),G=tt(s[y],D),k=P,q=_-b[h]-G,V=_/2-b[h]/2+R,X=qn(k,V,q),M=!c.arrow&&Et(o)!=null&&V!==X&&a.reference[h]/2-(V<k?P:G)-b[h]/2<0,I=M?V<k?V-k:V-q:0;return{[m]:f[m]+I,data:{[m]:X,centerOffset:V-X-I,...M&&{alignmentOffset:I}},reset:M}}}),rs=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(n){var t,r;const{placement:o,middlewareData:a,rects:l,initialPlacement:i,platform:c,elements:d}=n,{mainAxis:u=!0,crossAxis:s=!0,fallbackPlacements:f,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:b=!0,...x}=He(e,n);if((t=a.arrow)!=null&&t.alignmentOffset)return{};const v=$e(o),y=Oe(i),w=$e(i)===i,C=await(c.isRTL==null?void 0:c.isRTL(d.floating)),E=f||(w||!b?[fn(i)]:Yi(i)),N=h!=="none";!f&&N&&E.push(...Qi(i,b,h,C));const _=[i,...E],R=await Lt(n,x),D=[];let P=((r=a.flip)==null?void 0:r.overflows)||[];if(u&&D.push(R[v]),s){const V=Xi(o,l,C);D.push(R[V[0]],R[V[1]])}if(P=[...P,{placement:o,overflows:D}],!D.every(V=>V<=0)){var G,k;const V=(((G=a.flip)==null?void 0:G.index)||0)+1,X=_[V];if(X&&(!(s==="alignment"&&y!==Oe(X))||P.every(I=>I.overflows[0]>0&&Oe(I.placement)===y)))return{data:{index:V,overflows:P},reset:{placement:X}};let M=(k=P.filter(I=>I.overflows[0]<=0).sort((I,L)=>I.overflows[1]-L.overflows[1])[0])==null?void 0:k.placement;if(!M)switch(m){case"bestFit":{var q;const I=(q=P.filter(L=>{if(N){const T=Oe(L.placement);return T===y||T==="y"}return!0}).map(L=>[L.placement,L.overflows.filter(T=>T>0).reduce((T,A)=>T+A,0)]).sort((L,T)=>L[1]-T[1])[0])==null?void 0:q[0];I&&(M=I);break}case"initialPlacement":M=i;break}if(o!==M)return{reset:{placement:M}}}return{}}}};function to(e,n){return{top:e.top-n.height,right:e.right-n.width,bottom:e.bottom-n.height,left:e.left-n.width}}function no(e){return Gi.some(n=>e[n]>=0)}const os=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(n){const{rects:t}=n,{strategy:r="referenceHidden",...o}=He(e,n);switch(r){case"referenceHidden":{const a=await Lt(n,{...o,elementContext:"reference"}),l=to(a,t.reference);return{data:{referenceHiddenOffsets:l,referenceHidden:no(l)}}}case"escaped":{const a=await Lt(n,{...o,altBoundary:!0}),l=to(a,t.floating);return{data:{escapedOffsets:l,escaped:no(l)}}}default:return{}}}}},Ko=new Set(["left","top"]);async function as(e,n){const{placement:t,platform:r,elements:o}=e,a=await(r.isRTL==null?void 0:r.isRTL(o.floating)),l=$e(t),i=Et(t),c=Oe(t)==="y",d=Ko.has(l)?-1:1,u=a&&c?-1:1,s=He(n,e);let{mainAxis:f,crossAxis:m,alignmentAxis:h}=typeof s=="number"?{mainAxis:s,crossAxis:0,alignmentAxis:null}:{mainAxis:s.mainAxis||0,crossAxis:s.crossAxis||0,alignmentAxis:s.alignmentAxis};return i&&typeof h=="number"&&(m=i==="end"?h*-1:h),c?{x:m*u,y:f*d}:{x:f*d,y:m*u}}const ls=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(n){var t,r;const{x:o,y:a,placement:l,middlewareData:i}=n,c=await as(n,e);return l===((t=i.offset)==null?void 0:t.placement)&&(r=i.arrow)!=null&&r.alignmentOffset?{}:{x:o+c.x,y:a+c.y,data:{...c,placement:l}}}}},is=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(n){const{x:t,y:r,placement:o}=n,{mainAxis:a=!0,crossAxis:l=!1,limiter:i={fn:x=>{let{x:v,y}=x;return{x:v,y}}},...c}=He(e,n),d={x:t,y:r},u=await Lt(n,c),s=Oe($e(o)),f=gr(s);let m=d[f],h=d[s];if(a){const x=f==="y"?"top":"left",v=f==="y"?"bottom":"right",y=m+u[x],w=m-u[v];m=qn(y,m,w)}if(l){const x=s==="y"?"top":"left",v=s==="y"?"bottom":"right",y=h+u[x],w=h-u[v];h=qn(y,h,w)}const b=i.fn({...n,[f]:m,[s]:h});return{...b,data:{x:b.x-t,y:b.y-r,enabled:{[f]:a,[s]:l}}}}}},ss=function(e){return e===void 0&&(e={}),{options:e,fn(n){const{x:t,y:r,placement:o,rects:a,middlewareData:l}=n,{offset:i=0,mainAxis:c=!0,crossAxis:d=!0}=He(e,n),u={x:t,y:r},s=Oe(o),f=gr(s);let m=u[f],h=u[s];const b=He(i,n),x=typeof b=="number"?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(c){const w=f==="y"?"height":"width",C=a.reference[f]-a.floating[w]+x.mainAxis,E=a.reference[f]+a.reference[w]-x.mainAxis;m<C?m=C:m>E&&(m=E)}if(d){var v,y;const w=f==="y"?"width":"height",C=Ko.has($e(o)),E=a.reference[s]-a.floating[w]+(C&&((v=l.offset)==null?void 0:v[s])||0)+(C?0:x.crossAxis),N=a.reference[s]+a.reference[w]+(C?0:((y=l.offset)==null?void 0:y[s])||0)-(C?x.crossAxis:0);h<E?h=E:h>N&&(h=N)}return{[f]:m,[s]:h}}}},ds=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(n){var t,r;const{placement:o,rects:a,platform:l,elements:i}=n,{apply:c=()=>{},...d}=He(e,n),u=await Lt(n,d),s=$e(o),f=Et(o),m=Oe(o)==="y",{width:h,height:b}=a.floating;let x,v;s==="top"||s==="bottom"?(x=s,v=f===(await(l.isRTL==null?void 0:l.isRTL(i.floating))?"start":"end")?"left":"right"):(v=s,x=f==="end"?"top":"bottom");const y=b-u.top-u.bottom,w=h-u.left-u.right,C=tt(b-u[x],y),E=tt(h-u[v],w),N=!n.middlewareData.shift;let _=C,R=E;if((t=n.middlewareData.shift)!=null&&t.enabled.x&&(R=w),(r=n.middlewareData.shift)!=null&&r.enabled.y&&(_=y),N&&!f){const P=be(u.left,0),G=be(u.right,0),k=be(u.top,0),q=be(u.bottom,0);m?R=h-2*(P!==0||G!==0?P+G:be(u.left,u.right)):_=b-2*(k!==0||q!==0?k+q:be(u.top,u.bottom))}await c({...n,availableWidth:R,availableHeight:_});const D=await l.getDimensions(i.floating);return h!==D.width||b!==D.height?{reset:{rects:!0}}:{}}}};function vn(){return typeof window<"u"}function jt(e){return Zo(e)?(e.nodeName||"").toLowerCase():"#document"}function ve(e){var n;return(e==null||(n=e.ownerDocument)==null?void 0:n.defaultView)||window}function Te(e){var n;return(n=(Zo(e)?e.ownerDocument:e.document)||window.document)==null?void 0:n.documentElement}function Zo(e){return vn()?e instanceof Node||e instanceof ve(e).Node:!1}function je(e){return vn()?e instanceof Element||e instanceof ve(e).Element:!1}function Le(e){return vn()?e instanceof HTMLElement||e instanceof ve(e).HTMLElement:!1}function ro(e){return!vn()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof ve(e).ShadowRoot}const cs=new Set(["inline","contents"]);function Vt(e){const{overflow:n,overflowX:t,overflowY:r,display:o}=Ne(e);return/auto|scroll|overlay|hidden|clip/.test(n+r+t)&&!cs.has(o)}const us=new Set(["table","td","th"]);function gs(e){return us.has(jt(e))}const fs=[":popover-open",":modal"];function xn(e){return fs.some(n=>{try{return e.matches(n)}catch{return!1}})}const ps=["transform","translate","scale","rotate","perspective"],ms=["transform","translate","scale","rotate","perspective","filter"],bs=["paint","layout","strict","content"];function mr(e){const n=br(),t=je(e)?Ne(e):e;return ps.some(r=>t[r]?t[r]!=="none":!1)||(t.containerType?t.containerType!=="normal":!1)||!n&&(t.backdropFilter?t.backdropFilter!=="none":!1)||!n&&(t.filter?t.filter!=="none":!1)||ms.some(r=>(t.willChange||"").includes(r))||bs.some(r=>(t.contain||"").includes(r))}function hs(e){let n=nt(e);for(;Le(n)&&!St(n);){if(mr(n))return n;if(xn(n))return null;n=nt(n)}return null}function br(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const vs=new Set(["html","body","#document"]);function St(e){return vs.has(jt(e))}function Ne(e){return ve(e).getComputedStyle(e)}function yn(e){return je(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function nt(e){if(jt(e)==="html")return e;const n=e.assignedSlot||e.parentNode||ro(e)&&e.host||Te(e);return ro(n)?n.host:n}function Jo(e){const n=nt(e);return St(n)?e.ownerDocument?e.ownerDocument.body:e.body:Le(n)&&Vt(n)?n:Jo(n)}function Tt(e,n,t){var r;n===void 0&&(n=[]),t===void 0&&(t=!0);const o=Jo(e),a=o===((r=e.ownerDocument)==null?void 0:r.body),l=ve(o);if(a){const i=Yn(l);return n.concat(l,l.visualViewport||[],Vt(o)?o:[],i&&t?Tt(i):[])}return n.concat(o,Tt(o,[],t))}function Yn(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Qo(e){const n=Ne(e);let t=parseFloat(n.width)||0,r=parseFloat(n.height)||0;const o=Le(e),a=o?e.offsetWidth:t,l=o?e.offsetHeight:r,i=gn(t)!==a||gn(r)!==l;return i&&(t=a,r=l),{width:t,height:r,$:i}}function hr(e){return je(e)?e:e.contextElement}function yt(e){const n=hr(e);if(!Le(n))return De(1);const t=n.getBoundingClientRect(),{width:r,height:o,$:a}=Qo(n);let l=(a?gn(t.width):t.width)/r,i=(a?gn(t.height):t.height)/o;return(!l||!Number.isFinite(l))&&(l=1),(!i||!Number.isFinite(i))&&(i=1),{x:l,y:i}}const xs=De(0);function ea(e){const n=ve(e);return!br()||!n.visualViewport?xs:{x:n.visualViewport.offsetLeft,y:n.visualViewport.offsetTop}}function ys(e,n,t){return n===void 0&&(n=!1),!t||n&&t!==ve(e)?!1:n}function ut(e,n,t,r){n===void 0&&(n=!1),t===void 0&&(t=!1);const o=e.getBoundingClientRect(),a=hr(e);let l=De(1);n&&(r?je(r)&&(l=yt(r)):l=yt(e));const i=ys(a,t,r)?ea(a):De(0);let c=(o.left+i.x)/l.x,d=(o.top+i.y)/l.y,u=o.width/l.x,s=o.height/l.y;if(a){const f=ve(a),m=r&&je(r)?ve(r):r;let h=f,b=Yn(h);for(;b&&r&&m!==h;){const x=yt(b),v=b.getBoundingClientRect(),y=Ne(b),w=v.left+(b.clientLeft+parseFloat(y.paddingLeft))*x.x,C=v.top+(b.clientTop+parseFloat(y.paddingTop))*x.y;c*=x.x,d*=x.y,u*=x.x,s*=x.y,c+=w,d+=C,h=ve(b),b=Yn(h)}}return pn({width:u,height:s,x:c,y:d})}function vr(e,n){const t=yn(e).scrollLeft;return n?n.left+t:ut(Te(e)).left+t}function ta(e,n,t){t===void 0&&(t=!1);const r=e.getBoundingClientRect(),o=r.left+n.scrollLeft-(t?0:vr(e,r)),a=r.top+n.scrollTop;return{x:o,y:a}}function ws(e){let{elements:n,rect:t,offsetParent:r,strategy:o}=e;const a=o==="fixed",l=Te(r),i=n?xn(n.floating):!1;if(r===l||i&&a)return t;let c={scrollLeft:0,scrollTop:0},d=De(1);const u=De(0),s=Le(r);if((s||!s&&!a)&&((jt(r)!=="body"||Vt(l))&&(c=yn(r)),Le(r))){const m=ut(r);d=yt(r),u.x=m.x+r.clientLeft,u.y=m.y+r.clientTop}const f=l&&!s&&!a?ta(l,c,!0):De(0);return{width:t.width*d.x,height:t.height*d.y,x:t.x*d.x-c.scrollLeft*d.x+u.x+f.x,y:t.y*d.y-c.scrollTop*d.y+u.y+f.y}}function Cs(e){return Array.from(e.getClientRects())}function Ss(e){const n=Te(e),t=yn(e),r=e.ownerDocument.body,o=be(n.scrollWidth,n.clientWidth,r.scrollWidth,r.clientWidth),a=be(n.scrollHeight,n.clientHeight,r.scrollHeight,r.clientHeight);let l=-t.scrollLeft+vr(e);const i=-t.scrollTop;return Ne(r).direction==="rtl"&&(l+=be(n.clientWidth,r.clientWidth)-o),{width:o,height:a,x:l,y:i}}function _s(e,n){const t=ve(e),r=Te(e),o=t.visualViewport;let a=r.clientWidth,l=r.clientHeight,i=0,c=0;if(o){a=o.width,l=o.height;const d=br();(!d||d&&n==="fixed")&&(i=o.offsetLeft,c=o.offsetTop)}return{width:a,height:l,x:i,y:c}}const Rs=new Set(["absolute","fixed"]);function Es(e,n){const t=ut(e,!0,n==="fixed"),r=t.top+e.clientTop,o=t.left+e.clientLeft,a=Le(e)?yt(e):De(1),l=e.clientWidth*a.x,i=e.clientHeight*a.y,c=o*a.x,d=r*a.y;return{width:l,height:i,x:c,y:d}}function oo(e,n,t){let r;if(n==="viewport")r=_s(e,t);else if(n==="document")r=Ss(Te(e));else if(je(n))r=Es(n,t);else{const o=ea(e);r={x:n.x-o.x,y:n.y-o.y,width:n.width,height:n.height}}return pn(r)}function na(e,n){const t=nt(e);return t===n||!je(t)||St(t)?!1:Ne(t).position==="fixed"||na(t,n)}function js(e,n){const t=n.get(e);if(t)return t;let r=Tt(e,[],!1).filter(i=>je(i)&&jt(i)!=="body"),o=null;const a=Ne(e).position==="fixed";let l=a?nt(e):e;for(;je(l)&&!St(l);){const i=Ne(l),c=mr(l);!c&&i.position==="fixed"&&(o=null),(a?!c&&!o:!c&&i.position==="static"&&o&&Rs.has(o.position)||Vt(l)&&!c&&na(e,l))?r=r.filter(d=>d!==l):o=i,l=nt(l)}return n.set(e,r),r}function Ns(e){let{element:n,boundary:t,rootBoundary:r,strategy:o}=e;const a=[...t==="clippingAncestors"?xn(n)?[]:js(n,this._c):[].concat(t),r],l=a[0],i=a.reduce((c,d)=>{const u=oo(n,d,o);return c.top=be(u.top,c.top),c.right=tt(u.right,c.right),c.bottom=tt(u.bottom,c.bottom),c.left=be(u.left,c.left),c},oo(n,l,o));return{width:i.right-i.left,height:i.bottom-i.top,x:i.left,y:i.top}}function ks(e){const{width:n,height:t}=Qo(e);return{width:n,height:t}}function Ms(e,n,t){const r=Le(n),o=Te(n),a=t==="fixed",l=ut(e,!0,a,n);let i={scrollLeft:0,scrollTop:0};const c=De(0);function d(){c.x=vr(o)}if(r||!r&&!a)if((jt(n)!=="body"||Vt(o))&&(i=yn(n)),r){const m=ut(n,!0,a,n);c.x=m.x+n.clientLeft,c.y=m.y+n.clientTop}else o&&d();a&&!r&&o&&d();const u=o&&!r&&!a?ta(o,i):De(0),s=l.left+i.scrollLeft-c.x-u.x,f=l.top+i.scrollTop-c.y-u.y;return{x:s,y:f,width:l.width,height:l.height}}function Fn(e){return Ne(e).position==="static"}function ao(e,n){if(!Le(e)||Ne(e).position==="fixed")return null;if(n)return n(e);let t=e.offsetParent;return Te(e)===t&&(t=t.ownerDocument.body),t}function ra(e,n){const t=ve(e);if(xn(e))return t;if(!Le(e)){let o=nt(e);for(;o&&!St(o);){if(je(o)&&!Fn(o))return o;o=nt(o)}return t}let r=ao(e,n);for(;r&&gs(r)&&Fn(r);)r=ao(r,n);return r&&St(r)&&Fn(r)&&!mr(r)?t:r||hs(e)||t}const Ps=async function(e){const n=this.getOffsetParent||ra,t=this.getDimensions,r=await t(e.floating);return{reference:Ms(e.reference,await n(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Fs(e){return Ne(e).direction==="rtl"}const As={convertOffsetParentRelativeRectToViewportRelativeRect:ws,getDocumentElement:Te,getClippingRect:Ns,getOffsetParent:ra,getElementRects:Ps,getClientRects:Cs,getDimensions:ks,getScale:yt,isElement:je,isRTL:Fs};function oa(e,n){return e.x===n.x&&e.y===n.y&&e.width===n.width&&e.height===n.height}function Is(e,n){let t=null,r;const o=Te(e);function a(){var i;clearTimeout(r),(i=t)==null||i.disconnect(),t=null}function l(i,c){i===void 0&&(i=!1),c===void 0&&(c=1),a();const d=e.getBoundingClientRect(),{left:u,top:s,width:f,height:m}=d;if(i||n(),!f||!m)return;const h=Zt(s),b=Zt(o.clientWidth-(u+f)),x=Zt(o.clientHeight-(s+m)),v=Zt(u),y={rootMargin:-h+"px "+-b+"px "+-x+"px "+-v+"px",threshold:be(0,tt(1,c))||1};let w=!0;function C(E){const N=E[0].intersectionRatio;if(N!==c){if(!w)return l();N?l(!1,N):r=setTimeout(()=>{l(!1,1e-7)},1e3)}N===1&&!oa(d,e.getBoundingClientRect())&&l(),w=!1}try{t=new IntersectionObserver(C,{...y,root:o.ownerDocument})}catch{t=new IntersectionObserver(C,y)}t.observe(e)}return l(!0),a}function Os(e,n,t,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:l=typeof ResizeObserver=="function",layoutShift:i=typeof IntersectionObserver=="function",animationFrame:c=!1}=r,d=hr(e),u=o||a?[...d?Tt(d):[],...Tt(n)]:[];u.forEach(v=>{o&&v.addEventListener("scroll",t,{passive:!0}),a&&v.addEventListener("resize",t)});const s=d&&i?Is(d,t):null;let f=-1,m=null;l&&(m=new ResizeObserver(v=>{let[y]=v;y&&y.target===d&&m&&(m.unobserve(n),cancelAnimationFrame(f),f=requestAnimationFrame(()=>{var w;(w=m)==null||w.observe(n)})),t()}),d&&!c&&m.observe(d),m.observe(n));let h,b=c?ut(e):null;c&&x();function x(){const v=ut(e);b&&!oa(b,v)&&t(),b=v,h=requestAnimationFrame(x)}return t(),()=>{var v;u.forEach(y=>{o&&y.removeEventListener("scroll",t),a&&y.removeEventListener("resize",t)}),s==null||s(),(v=m)==null||v.disconnect(),m=null,c&&cancelAnimationFrame(h)}}const Ds=ls,Ls=is,Ts=rs,Vs=ds,zs=os,lo=ns,Bs=ss,Hs=(e,n,t)=>{const r=new Map,o={platform:As,...t},a={...o.platform,_c:r};return ts(e,n,{...o,platform:a})};var $s=typeof document<"u",Gs=function(){},dn=$s?p.useLayoutEffect:Gs;function mn(e,n){if(e===n)return!0;if(typeof e!=typeof n)return!1;if(typeof e=="function"&&e.toString()===n.toString())return!0;let t,r,o;if(e&&n&&typeof e=="object"){if(Array.isArray(e)){if(t=e.length,t!==n.length)return!1;for(r=t;r--!==0;)if(!mn(e[r],n[r]))return!1;return!0}if(o=Object.keys(e),t=o.length,t!==Object.keys(n).length)return!1;for(r=t;r--!==0;)if(!{}.hasOwnProperty.call(n,o[r]))return!1;for(r=t;r--!==0;){const a=o[r];if(!(a==="_owner"&&e.$$typeof)&&!mn(e[a],n[a]))return!1}return!0}return e!==e&&n!==n}function aa(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function io(e,n){const t=aa(e);return Math.round(n*t)/t}function An(e){const n=p.useRef(e);return dn(()=>{n.current=e}),n}function Ws(e){e===void 0&&(e={});const{placement:n="bottom",strategy:t="absolute",middleware:r=[],platform:o,elements:{reference:a,floating:l}={},transform:i=!0,whileElementsMounted:c,open:d}=e,[u,s]=p.useState({x:0,y:0,strategy:t,placement:n,middlewareData:{},isPositioned:!1}),[f,m]=p.useState(r);mn(f,r)||m(r);const[h,b]=p.useState(null),[x,v]=p.useState(null),y=p.useCallback(L=>{L!==N.current&&(N.current=L,b(L))},[]),w=p.useCallback(L=>{L!==_.current&&(_.current=L,v(L))},[]),C=a||h,E=l||x,N=p.useRef(null),_=p.useRef(null),R=p.useRef(u),D=c!=null,P=An(c),G=An(o),k=An(d),q=p.useCallback(()=>{if(!N.current||!_.current)return;const L={placement:n,strategy:t,middleware:f};G.current&&(L.platform=G.current),Hs(N.current,_.current,L).then(T=>{const A={...T,isPositioned:k.current!==!1};V.current&&!mn(R.current,A)&&(R.current=A,Eo.flushSync(()=>{s(A)}))})},[f,n,t,G,k]);dn(()=>{d===!1&&R.current.isPositioned&&(R.current.isPositioned=!1,s(L=>({...L,isPositioned:!1})))},[d]);const V=p.useRef(!1);dn(()=>(V.current=!0,()=>{V.current=!1}),[]),dn(()=>{if(C&&(N.current=C),E&&(_.current=E),C&&E){if(P.current)return P.current(C,E,q);q()}},[C,E,q,P,D]);const X=p.useMemo(()=>({reference:N,floating:_,setReference:y,setFloating:w}),[y,w]),M=p.useMemo(()=>({reference:C,floating:E}),[C,E]),I=p.useMemo(()=>{const L={position:t,left:0,top:0};if(!M.floating)return L;const T=io(M.floating,u.x),A=io(M.floating,u.y);return i?{...L,transform:"translate("+T+"px, "+A+"px)",...aa(M.floating)>=1.5&&{willChange:"transform"}}:{position:t,left:T,top:A}},[t,i,M.floating,u.x,u.y]);return p.useMemo(()=>({...u,update:q,refs:X,elements:M,floatingStyles:I}),[u,q,X,M,I])}const Us=e=>{function n(t){return{}.hasOwnProperty.call(t,"current")}return{name:"arrow",options:e,fn(t){const{element:r,padding:o}=typeof e=="function"?e(t):e;return r&&n(r)?r.current!=null?lo({element:r.current,padding:o}).fn(t):{}:r?lo({element:r,padding:o}).fn(t):{}}}},qs=(e,n)=>({...Ds(e),options:[e,n]}),Xs=(e,n)=>({...Ls(e),options:[e,n]}),Ys=(e,n)=>({...Bs(e),options:[e,n]}),Ks=(e,n)=>({...Ts(e),options:[e,n]}),Zs=(e,n)=>({...Vs(e),options:[e,n]}),Js=(e,n)=>({...zs(e),options:[e,n]}),Qs=(e,n)=>({...Us(e),options:[e,n]});var ed="Arrow",la=p.forwardRef((e,n)=>{const{children:t,width:r=10,height:o=5,...a}=e;return g.jsx(pe.svg,{...a,ref:n,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?t:g.jsx("polygon",{points:"0,0 30,0 15,10"})})});la.displayName=ed;var td=la;function nd(e){const[n,t]=p.useState(void 0);return et(()=>{if(e){t({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const a=o[0];let l,i;if("borderBoxSize"in a){const c=a.borderBoxSize,d=Array.isArray(c)?c[0]:c;l=d.inlineSize,i=d.blockSize}else l=e.offsetWidth,i=e.offsetHeight;t({width:l,height:i})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else t(void 0)},[e]),n}var xr="Popper",[ia,sa]=dr(xr),[rd,da]=ia(xr),ca=e=>{const{__scopePopper:n,children:t}=e,[r,o]=p.useState(null);return g.jsx(rd,{scope:n,anchor:r,onAnchorChange:o,children:t})};ca.displayName=xr;var ua="PopperAnchor",ga=p.forwardRef((e,n)=>{const{__scopePopper:t,virtualRef:r,...o}=e,a=da(ua,t),l=p.useRef(null),i=ke(n,l);return p.useEffect(()=>{a.onAnchorChange((r==null?void 0:r.current)||l.current)}),r?null:g.jsx(pe.div,{...o,ref:i})});ga.displayName=ua;var yr="PopperContent",[od,ad]=ia(yr),fa=p.forwardRef((e,n)=>{var t,r,o,a,l,i;const{__scopePopper:c,side:d="bottom",sideOffset:u=0,align:s="center",alignOffset:f=0,arrowPadding:m=0,avoidCollisions:h=!0,collisionBoundary:b=[],collisionPadding:x=0,sticky:v="partial",hideWhenDetached:y=!1,updatePositionStrategy:w="optimized",onPlaced:C,...E}=e,N=da(yr,c),[_,R]=p.useState(null),D=ke(n,ie=>R(ie)),[P,G]=p.useState(null),k=nd(P),q=(k==null?void 0:k.width)??0,V=(k==null?void 0:k.height)??0,X=d+(s!=="center"?"-"+s:""),M=typeof x=="number"?x:{top:0,right:0,bottom:0,left:0,...x},I=Array.isArray(b)?b:[b],L=I.length>0,T={padding:M,boundary:I.filter(id),altBoundary:L},{refs:A,floatingStyles:j,placement:F,isPositioned:z,middlewareData:Y}=Ws({strategy:"fixed",placement:X,whileElementsMounted:(...ie)=>Os(...ie,{animationFrame:w==="always"}),elements:{reference:N.anchor},middleware:[qs({mainAxis:u+V,alignmentAxis:f}),h&&Xs({mainAxis:!0,crossAxis:!1,limiter:v==="partial"?Ys():void 0,...T}),h&&Ks({...T}),Zs({...T,apply:({elements:ie,rects:ye,availableWidth:Bt,availableHeight:Ge})=>{const{width:Ce,height:ft}=ye.reference,ce=ie.floating.style;ce.setProperty("--radix-popper-available-width",`${Bt}px`),ce.setProperty("--radix-popper-available-height",`${Ge}px`),ce.setProperty("--radix-popper-anchor-width",`${Ce}px`),ce.setProperty("--radix-popper-anchor-height",`${ft}px`)}}),P&&Qs({element:P,padding:m}),sd({arrowWidth:q,arrowHeight:V}),y&&Js({strategy:"referenceHidden",...T})]}),[U,le]=ba(F),ee=Ct(C);et(()=>{z&&(ee==null||ee())},[z,ee]);const ne=(t=Y.arrow)==null?void 0:t.x,de=(r=Y.arrow)==null?void 0:r.y,Ve=((o=Y.arrow)==null?void 0:o.centerOffset)!==0,[Pe,Q]=p.useState();return et(()=>{_&&Q(window.getComputedStyle(_).zIndex)},[_]),g.jsx("div",{ref:A.setFloating,"data-radix-popper-content-wrapper":"",style:{...j,transform:z?j.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Pe,"--radix-popper-transform-origin":[(a=Y.transformOrigin)==null?void 0:a.x,(l=Y.transformOrigin)==null?void 0:l.y].join(" "),...((i=Y.hide)==null?void 0:i.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:g.jsx(od,{scope:c,placedSide:U,onArrowChange:G,arrowX:ne,arrowY:de,shouldHideArrow:Ve,children:g.jsx(pe.div,{"data-side":U,"data-align":le,...E,ref:D,style:{...E.style,animation:z?void 0:"none"}})})})});fa.displayName=yr;var pa="PopperArrow",ld={top:"bottom",right:"left",bottom:"top",left:"right"},ma=p.forwardRef(function(e,n){const{__scopePopper:t,...r}=e,o=ad(pa,t),a=ld[o.placedSide];return g.jsx("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:g.jsx(td,{...r,ref:n,style:{...r.style,display:"block"}})})});ma.displayName=pa;function id(e){return e!==null}var sd=e=>({name:"transformOrigin",options:e,fn(n){var t,r,o;const{placement:a,rects:l,middlewareData:i}=n,c=((t=i.arrow)==null?void 0:t.centerOffset)!==0,d=c?0:e.arrowWidth,u=c?0:e.arrowHeight,[s,f]=ba(a),m={start:"0%",center:"50%",end:"100%"}[f],h=(((r=i.arrow)==null?void 0:r.x)??0)+d/2,b=(((o=i.arrow)==null?void 0:o.y)??0)+u/2;let x="",v="";return s==="bottom"?(x=c?m:`${h}px`,v=`${-u}px`):s==="top"?(x=c?m:`${h}px`,v=`${l.floating.height+u}px`):s==="right"?(x=`${-u}px`,v=c?m:`${b}px`):s==="left"&&(x=`${l.floating.width+u}px`,v=c?m:`${b}px`),{data:{x,y:v}}}});function ba(e){const[n,t="center"]=e.split("-");return[n,t]}var dd=ca,ha=ga,cd=fa,ud=ma,gd="Portal",wr=p.forwardRef((e,n)=>{var t;const{container:r,...o}=e,[a,l]=p.useState(!1);et(()=>l(!0),[]);const i=r||a&&((t=globalThis==null?void 0:globalThis.document)==null?void 0:t.body);return i?Ro.createPortal(g.jsx(pe.div,{...o,ref:n}),i):null});wr.displayName=gd;function fd(e,n){return p.useReducer((t,r)=>n[t][r]??t,e)}var Nt=e=>{const{present:n,children:t}=e,r=pd(n),o=typeof t=="function"?t({present:r.isPresent}):p.Children.only(t),a=ke(r.ref,md(o));return typeof t=="function"||r.isPresent?p.cloneElement(o,{ref:a}):null};Nt.displayName="Presence";function pd(e){const[n,t]=p.useState(),r=p.useRef(null),o=p.useRef(e),a=p.useRef("none"),l=e?"mounted":"unmounted",[i,c]=fd(l,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return p.useEffect(()=>{const d=Jt(r.current);a.current=i==="mounted"?d:"none"},[i]),et(()=>{const d=r.current,u=o.current;if(u!==e){const s=a.current,f=Jt(d);e?c("MOUNT"):f==="none"||(d==null?void 0:d.display)==="none"?c("UNMOUNT"):c(u&&s!==f?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,c]),et(()=>{if(n){let d;const u=n.ownerDocument.defaultView??window,s=m=>{const h=Jt(r.current).includes(m.animationName);if(m.target===n&&h&&(c("ANIMATION_END"),!o.current)){const b=n.style.animationFillMode;n.style.animationFillMode="forwards",d=u.setTimeout(()=>{n.style.animationFillMode==="forwards"&&(n.style.animationFillMode=b)})}},f=m=>{m.target===n&&(a.current=Jt(r.current))};return n.addEventListener("animationstart",f),n.addEventListener("animationcancel",s),n.addEventListener("animationend",s),()=>{u.clearTimeout(d),n.removeEventListener("animationstart",f),n.removeEventListener("animationcancel",s),n.removeEventListener("animationend",s)}}else c("ANIMATION_END")},[n,c]),{isPresent:["mounted","unmountSuspended"].includes(i),ref:p.useCallback(d=>{r.current=d?getComputedStyle(d):null,t(d)},[])}}function Jt(e){return(e==null?void 0:e.animationName)||"none"}function md(e){var n,t;let r=(n=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o?e.props.ref:e.props.ref||e.ref)}var bd=_o[" useInsertionEffect ".trim().toString()]||et;function va({prop:e,defaultProp:n,onChange:t=()=>{},caller:r}){const[o,a,l]=hd({defaultProp:n,onChange:t}),i=e!==void 0,c=i?e:o;{const u=p.useRef(e!==void 0);p.useEffect(()=>{const s=u.current;s!==i&&console.warn(`${r} is changing from ${s?"controlled":"uncontrolled"} to ${i?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),u.current=i},[i,r])}const d=p.useCallback(u=>{var s;if(i){const f=vd(u)?u(e):u;f!==e&&((s=l.current)==null||s.call(l,f))}else a(u)},[i,e,a,l]);return[c,d]}function hd({defaultProp:e,onChange:n}){const[t,r]=p.useState(e),o=p.useRef(t),a=p.useRef(n);return bd(()=>{a.current=n},[n]),p.useEffect(()=>{var l;o.current!==t&&((l=a.current)==null||l.call(a,t),o.current=t)},[t,o]),[t,r,a]}function vd(e){return typeof e=="function"}var xd=function(e){if(typeof document>"u")return null;var n=Array.isArray(e)?e[0]:e;return n.ownerDocument.body},mt=new WeakMap,Qt=new WeakMap,en={},In=0,xa=function(e){return e&&(e.host||xa(e.parentNode))},yd=function(e,n){return n.map(function(t){if(e.contains(t))return t;var r=xa(t);return r&&e.contains(r)?r:(console.error("aria-hidden",t,"in not contained inside",e,". Doing nothing"),null)}).filter(function(t){return!!t})},wd=function(e,n,t,r){var o=yd(n,Array.isArray(e)?e:[e]);en[t]||(en[t]=new WeakMap);var a=en[t],l=[],i=new Set,c=new Set(o),d=function(s){!s||i.has(s)||(i.add(s),d(s.parentNode))};o.forEach(d);var u=function(s){!s||c.has(s)||Array.prototype.forEach.call(s.children,function(f){if(i.has(f))u(f);else try{var m=f.getAttribute(r),h=m!==null&&m!=="false",b=(mt.get(f)||0)+1,x=(a.get(f)||0)+1;mt.set(f,b),a.set(f,x),l.push(f),b===1&&h&&Qt.set(f,!0),x===1&&f.setAttribute(t,"true"),h||f.setAttribute(r,"true")}catch(v){console.error("aria-hidden: cannot operate on ",f,v)}})};return u(n),i.clear(),In++,function(){l.forEach(function(s){var f=mt.get(s)-1,m=a.get(s)-1;mt.set(s,f),a.set(s,m),f||(Qt.has(s)||s.removeAttribute(r),Qt.delete(s)),m||s.removeAttribute(t)}),In--,In||(mt=new WeakMap,mt=new WeakMap,Qt=new WeakMap,en={})}},ya=function(e,n,t){t===void 0&&(t="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=xd(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),wd(r,o,t,"aria-hidden")):function(){return null}},Ie=function(){return Ie=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},Ie.apply(this,arguments)};function wa(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)n.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(t[r[o]]=e[r[o]]);return t}function Cd(e,n,t){for(var r=0,o=n.length,a;r<o;r++)(a||!(r in n))&&(a||(a=Array.prototype.slice.call(n,0,r)),a[r]=n[r]);return e.concat(a||Array.prototype.slice.call(n))}var cn="right-scroll-bar-position",un="width-before-scroll-bar",Sd="with-scroll-bars-hidden",_d="--removed-body-scroll-bar-size";function On(e,n){return typeof e=="function"?e(n):e&&(e.current=n),e}function Rd(e,n){var t=p.useState(function(){return{value:e,callback:n,facade:{get current(){return t.value},set current(r){var o=t.value;o!==r&&(t.value=r,t.callback(r,o))}}}})[0];return t.callback=n,t.facade}var Ed=typeof window<"u"?p.useLayoutEffect:p.useEffect,so=new WeakMap;function jd(e,n){var t=Rd(null,function(r){return e.forEach(function(o){return On(o,r)})});return Ed(function(){var r=so.get(t);if(r){var o=new Set(r),a=new Set(e),l=t.current;o.forEach(function(i){a.has(i)||On(i,null)}),a.forEach(function(i){o.has(i)||On(i,l)})}so.set(t,e)},[e]),t}function Nd(e){return e}function kd(e,n){n===void 0&&(n=Nd);var t=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return t.length?t[t.length-1]:e},useMedium:function(a){var l=n(a,r);return t.push(l),function(){t=t.filter(function(i){return i!==l})}},assignSyncMedium:function(a){for(r=!0;t.length;){var l=t;t=[],l.forEach(a)}t={push:function(i){return a(i)},filter:function(){return t}}},assignMedium:function(a){r=!0;var l=[];if(t.length){var i=t;t=[],i.forEach(a),l=t}var c=function(){var u=l;l=[],u.forEach(a)},d=function(){return Promise.resolve().then(c)};d(),t={push:function(u){l.push(u),d()},filter:function(u){return l=l.filter(u),t}}}};return o}function Md(e){e===void 0&&(e={});var n=kd(null);return n.options=Ie({async:!0,ssr:!1},e),n}var Ca=function(e){var n=e.sideCar,t=wa(e,["sideCar"]);if(!n)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=n.read();if(!r)throw new Error("Sidecar medium not found");return p.createElement(r,Ie({},t))};Ca.isSideCarExport=!0;function Pd(e,n){return e.useMedium(n),Ca}var Sa=Md(),Dn=function(){},wn=p.forwardRef(function(e,n){var t=p.useRef(null),r=p.useState({onScrollCapture:Dn,onWheelCapture:Dn,onTouchMoveCapture:Dn}),o=r[0],a=r[1],l=e.forwardProps,i=e.children,c=e.className,d=e.removeScrollBar,u=e.enabled,s=e.shards,f=e.sideCar,m=e.noRelative,h=e.noIsolation,b=e.inert,x=e.allowPinchZoom,v=e.as,y=v===void 0?"div":v,w=e.gapMode,C=wa(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=f,N=jd([t,n]),_=Ie(Ie({},C),o);return p.createElement(p.Fragment,null,u&&p.createElement(E,{sideCar:Sa,removeScrollBar:d,shards:s,noRelative:m,noIsolation:h,inert:b,setCallbacks:a,allowPinchZoom:!!x,lockRef:t,gapMode:w}),l?p.cloneElement(p.Children.only(i),Ie(Ie({},_),{ref:N})):p.createElement(y,Ie({},_,{className:c,ref:N}),i))});wn.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};wn.classNames={fullWidth:un,zeroRight:cn};var Fd=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Ad(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var n=Fd();return n&&e.setAttribute("nonce",n),e}function Id(e,n){e.styleSheet?e.styleSheet.cssText=n:e.appendChild(document.createTextNode(n))}function Od(e){var n=document.head||document.getElementsByTagName("head")[0];n.appendChild(e)}var Dd=function(){var e=0,n=null;return{add:function(t){e==0&&(n=Ad())&&(Id(n,t),Od(n)),e++},remove:function(){e--,!e&&n&&(n.parentNode&&n.parentNode.removeChild(n),n=null)}}},Ld=function(){var e=Dd();return function(n,t){p.useEffect(function(){return e.add(n),function(){e.remove()}},[n&&t])}},_a=function(){var e=Ld(),n=function(t){var r=t.styles,o=t.dynamic;return e(r,o),null};return n},Td={left:0,top:0,right:0,gap:0},Ln=function(e){return parseInt(e||"",10)||0},Vd=function(e){var n=window.getComputedStyle(document.body),t=n[e==="padding"?"paddingLeft":"marginLeft"],r=n[e==="padding"?"paddingTop":"marginTop"],o=n[e==="padding"?"paddingRight":"marginRight"];return[Ln(t),Ln(r),Ln(o)]},zd=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Td;var n=Vd(e),t=document.documentElement.clientWidth,r=window.innerWidth;return{left:n[0],top:n[1],right:n[2],gap:Math.max(0,r-t+n[2]-n[0])}},Bd=_a(),wt="data-scroll-locked",Hd=function(e,n,t,r){var o=e.left,a=e.top,l=e.right,i=e.gap;return t===void 0&&(t="margin"),`
  .`.concat(Sd,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(i,"px ").concat(r,`;
  }
  body[`).concat(wt,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([n&&"position: relative ".concat(r,";"),t==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(l,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(i,"px ").concat(r,`;
    `),t==="padding"&&"padding-right: ".concat(i,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(cn,` {
    right: `).concat(i,"px ").concat(r,`;
  }
  
  .`).concat(un,` {
    margin-right: `).concat(i,"px ").concat(r,`;
  }
  
  .`).concat(cn," .").concat(cn,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(un," .").concat(un,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(wt,`] {
    `).concat(_d,": ").concat(i,`px;
  }
`)},co=function(){var e=parseInt(document.body.getAttribute(wt)||"0",10);return isFinite(e)?e:0},$d=function(){p.useEffect(function(){return document.body.setAttribute(wt,(co()+1).toString()),function(){var e=co()-1;e<=0?document.body.removeAttribute(wt):document.body.setAttribute(wt,e.toString())}},[])},Gd=function(e){var n=e.noRelative,t=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;$d();var a=p.useMemo(function(){return zd(o)},[o]);return p.createElement(Bd,{styles:Hd(a,!n,o,t?"":"!important")})},Kn=!1;if(typeof window<"u")try{var tn=Object.defineProperty({},"passive",{get:function(){return Kn=!0,!0}});window.addEventListener("test",tn,tn),window.removeEventListener("test",tn,tn)}catch{Kn=!1}var bt=Kn?{passive:!1}:!1,Wd=function(e){return e.tagName==="TEXTAREA"},Ra=function(e,n){if(!(e instanceof Element))return!1;var t=window.getComputedStyle(e);return t[n]!=="hidden"&&!(t.overflowY===t.overflowX&&!Wd(e)&&t[n]==="visible")},Ud=function(e){return Ra(e,"overflowY")},qd=function(e){return Ra(e,"overflowX")},uo=function(e,n){var t=n.ownerDocument,r=n;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Ea(e,r);if(o){var a=ja(e,r),l=a[1],i=a[2];if(l>i)return!0}r=r.parentNode}while(r&&r!==t.body);return!1},Xd=function(e){var n=e.scrollTop,t=e.scrollHeight,r=e.clientHeight;return[n,t,r]},Yd=function(e){var n=e.scrollLeft,t=e.scrollWidth,r=e.clientWidth;return[n,t,r]},Ea=function(e,n){return e==="v"?Ud(n):qd(n)},ja=function(e,n){return e==="v"?Xd(n):Yd(n)},Kd=function(e,n){return e==="h"&&n==="rtl"?-1:1},Zd=function(e,n,t,r,o){var a=Kd(e,window.getComputedStyle(n).direction),l=a*r,i=t.target,c=n.contains(i),d=!1,u=l>0,s=0,f=0;do{if(!i)break;var m=ja(e,i),h=m[0],b=m[1],x=m[2],v=b-x-a*h;(h||v)&&Ea(e,i)&&(s+=v,f+=h);var y=i.parentNode;i=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!c&&i!==document.body||c&&(n.contains(i)||n===i));return(u&&Math.abs(s)<1||!u&&Math.abs(f)<1)&&(d=!0),d},nn=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},go=function(e){return[e.deltaX,e.deltaY]},fo=function(e){return e&&"current"in e?e.current:e},Jd=function(e,n){return e[0]===n[0]&&e[1]===n[1]},Qd=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},ec=0,ht=[];function tc(e){var n=p.useRef([]),t=p.useRef([0,0]),r=p.useRef(),o=p.useState(ec++)[0],a=p.useState(_a)[0],l=p.useRef(e);p.useEffect(function(){l.current=e},[e]),p.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var b=Cd([e.lockRef.current],(e.shards||[]).map(fo)).filter(Boolean);return b.forEach(function(x){return x.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),b.forEach(function(x){return x.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var i=p.useCallback(function(b,x){if("touches"in b&&b.touches.length===2||b.type==="wheel"&&b.ctrlKey)return!l.current.allowPinchZoom;var v=nn(b),y=t.current,w="deltaX"in b?b.deltaX:y[0]-v[0],C="deltaY"in b?b.deltaY:y[1]-v[1],E,N=b.target,_=Math.abs(w)>Math.abs(C)?"h":"v";if("touches"in b&&_==="h"&&N.type==="range")return!1;var R=uo(_,N);if(!R)return!0;if(R?E=_:(E=_==="v"?"h":"v",R=uo(_,N)),!R)return!1;if(!r.current&&"changedTouches"in b&&(w||C)&&(r.current=E),!E)return!0;var D=r.current||E;return Zd(D,x,b,D==="h"?w:C)},[]),c=p.useCallback(function(b){var x=b;if(!(!ht.length||ht[ht.length-1]!==a)){var v="deltaY"in x?go(x):nn(x),y=n.current.filter(function(E){return E.name===x.type&&(E.target===x.target||x.target===E.shadowParent)&&Jd(E.delta,v)})[0];if(y&&y.should){x.cancelable&&x.preventDefault();return}if(!y){var w=(l.current.shards||[]).map(fo).filter(Boolean).filter(function(E){return E.contains(x.target)}),C=w.length>0?i(x,w[0]):!l.current.noIsolation;C&&x.cancelable&&x.preventDefault()}}},[]),d=p.useCallback(function(b,x,v,y){var w={name:b,delta:x,target:v,should:y,shadowParent:nc(v)};n.current.push(w),setTimeout(function(){n.current=n.current.filter(function(C){return C!==w})},1)},[]),u=p.useCallback(function(b){t.current=nn(b),r.current=void 0},[]),s=p.useCallback(function(b){d(b.type,go(b),b.target,i(b,e.lockRef.current))},[]),f=p.useCallback(function(b){d(b.type,nn(b),b.target,i(b,e.lockRef.current))},[]);p.useEffect(function(){return ht.push(a),e.setCallbacks({onScrollCapture:s,onWheelCapture:s,onTouchMoveCapture:f}),document.addEventListener("wheel",c,bt),document.addEventListener("touchmove",c,bt),document.addEventListener("touchstart",u,bt),function(){ht=ht.filter(function(b){return b!==a}),document.removeEventListener("wheel",c,bt),document.removeEventListener("touchmove",c,bt),document.removeEventListener("touchstart",u,bt)}},[]);var m=e.removeScrollBar,h=e.inert;return p.createElement(p.Fragment,null,h?p.createElement(a,{styles:Qd(o)}):null,m?p.createElement(Gd,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function nc(e){for(var n=null;e!==null;)e instanceof ShadowRoot&&(n=e.host,e=e.host),e=e.parentNode;return n}const rc=Pd(Sa,tc);var Cr=p.forwardRef(function(e,n){return p.createElement(wn,Ie({},e,{ref:n,sideCar:rc}))});Cr.classNames=wn.classNames;var Cn="Popover",[Na,Tg]=dr(Cn,[sa]),zt=sa(),[oc,ot]=Na(Cn),ka=e=>{const{__scopePopover:n,children:t,open:r,defaultOpen:o,onOpenChange:a,modal:l=!1}=e,i=zt(n),c=p.useRef(null),[d,u]=p.useState(!1),[s,f]=va({prop:r,defaultProp:o??!1,onChange:a,caller:Cn});return g.jsx(dd,{...i,children:g.jsx(oc,{scope:n,contentId:sn(),triggerRef:c,open:s,onOpenChange:f,onOpenToggle:p.useCallback(()=>f(m=>!m),[f]),hasCustomAnchor:d,onCustomAnchorAdd:p.useCallback(()=>u(!0),[]),onCustomAnchorRemove:p.useCallback(()=>u(!1),[]),modal:l,children:t})})};ka.displayName=Cn;var Ma="PopoverAnchor",ac=p.forwardRef((e,n)=>{const{__scopePopover:t,...r}=e,o=ot(Ma,t),a=zt(t),{onCustomAnchorAdd:l,onCustomAnchorRemove:i}=o;return p.useEffect(()=>(l(),()=>i()),[l,i]),g.jsx(ha,{...a,...r,ref:n})});ac.displayName=Ma;var Pa="PopoverTrigger",Fa=p.forwardRef((e,n)=>{const{__scopePopover:t,...r}=e,o=ot(Pa,t),a=zt(t),l=ke(n,o.triggerRef),i=g.jsx(pe.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":La(o.open),...r,ref:l,onClick:he(e.onClick,o.onOpenToggle)});return o.hasCustomAnchor?i:g.jsx(ha,{asChild:!0,...a,children:i})});Fa.displayName=Pa;var Sr="PopoverPortal",[lc,ic]=Na(Sr,{forceMount:void 0}),Aa=e=>{const{__scopePopover:n,forceMount:t,children:r,container:o}=e,a=ot(Sr,n);return g.jsx(lc,{scope:n,forceMount:t,children:g.jsx(Nt,{present:t||a.open,children:g.jsx(wr,{asChild:!0,container:o,children:r})})})};Aa.displayName=Sr;var _t="PopoverContent",Ia=p.forwardRef((e,n)=>{const t=ic(_t,e.__scopePopover),{forceMount:r=t.forceMount,...o}=e,a=ot(_t,e.__scopePopover);return g.jsx(Nt,{present:r||a.open,children:a.modal?g.jsx(dc,{...o,ref:n}):g.jsx(cc,{...o,ref:n})})});Ia.displayName=_t;var sc=hn("PopoverContent.RemoveScroll"),dc=p.forwardRef((e,n)=>{const t=ot(_t,e.__scopePopover),r=p.useRef(null),o=ke(n,r),a=p.useRef(!1);return p.useEffect(()=>{const l=r.current;if(l)return ya(l)},[]),g.jsx(Cr,{as:sc,allowPinchZoom:!0,children:g.jsx(Oa,{...e,ref:o,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:he(e.onCloseAutoFocus,l=>{var i;l.preventDefault(),a.current||(i=t.triggerRef.current)==null||i.focus()}),onPointerDownOutside:he(e.onPointerDownOutside,l=>{const i=l.detail.originalEvent,c=i.button===0&&i.ctrlKey===!0,d=i.button===2||c;a.current=d},{checkForDefaultPrevented:!1}),onFocusOutside:he(e.onFocusOutside,l=>l.preventDefault(),{checkForDefaultPrevented:!1})})})}),cc=p.forwardRef((e,n)=>{const t=ot(_t,e.__scopePopover),r=p.useRef(!1),o=p.useRef(!1);return g.jsx(Oa,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{var l,i;(l=e.onCloseAutoFocus)==null||l.call(e,a),a.defaultPrevented||(r.current||(i=t.triggerRef.current)==null||i.focus(),a.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:a=>{var l,i;(l=e.onInteractOutside)==null||l.call(e,a),a.defaultPrevented||(r.current=!0,a.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const c=a.target;(i=t.triggerRef.current)!=null&&i.contains(c)&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&o.current&&a.preventDefault()}})}),Oa=p.forwardRef((e,n)=>{const{__scopePopover:t,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:l,onEscapeKeyDown:i,onPointerDownOutside:c,onFocusOutside:d,onInteractOutside:u,...s}=e,f=ot(_t,t),m=zt(t);return qo(),g.jsx(ur,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:g.jsx(cr,{asChild:!0,disableOutsidePointerEvents:l,onInteractOutside:u,onEscapeKeyDown:i,onPointerDownOutside:c,onFocusOutside:d,onDismiss:()=>f.onOpenChange(!1),children:g.jsx(cd,{"data-state":La(f.open),role:"dialog",id:f.contentId,...m,...s,ref:n,style:{...s.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),Da="PopoverClose",uc=p.forwardRef((e,n)=>{const{__scopePopover:t,...r}=e,o=ot(Da,t);return g.jsx(pe.button,{type:"button",...r,ref:n,onClick:he(e.onClick,()=>o.onOpenChange(!1))})});uc.displayName=Da;var gc="PopoverArrow",fc=p.forwardRef((e,n)=>{const{__scopePopover:t,...r}=e,o=zt(t);return g.jsx(ud,{...o,...r,ref:n})});fc.displayName=gc;function La(e){return e?"open":"closed"}var pc=ka,mc=Fa,bc=Aa,hc=Ia;const vc=J("z-50 w-auto rounded-md border bg-white mt-2 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",{variants:{sectioned:{true:"p-0",false:"p-0"},fullWidth:{true:"w-full",false:"w-48"},fullHeight:{true:"h-full",false:""},fluidContent:{true:"w-auto h-auto",false:""},hideOnPrint:{true:"print:hidden",false:""}},defaultVariants:{sectioned:!1,fullWidth:!1,fullHeight:!1,fluidContent:!1,hideOnPrint:!1}}),xc=e=>{switch(e){case"above":return"top";case"below":return"bottom";case"cover":case"mostSpace":default:return"bottom"}},yc=e=>{switch(e){case"left":return"start";case"center":return"center";case"right":return"end";default:return"center"}},_r=p.forwardRef(({children:e,preferredPosition:n="below",preferredAlignment:t="center",active:r,activator:o,preferInputActivator:a=!0,activatorWrapper:l="div",zIndexOverride:i=400,preventFocusOnClose:c=!1,sectioned:d=!1,fullWidth:u=!1,fullHeight:s=!1,fluidContent:f=!1,fixed:m=!1,ariaHaspopup:h,hideOnPrint:b=!1,onClose:x,autofocusTarget:v="container",preventCloseOnChildOverlayClick:y=!1,captureOverscroll:w=!1,className:C,...E},N)=>{const _=q=>{q||x("click-outside")},R=()=>{x("escape")},D=q=>{y||x("click-outside")},P=O(vc({sectioned:d,fullWidth:u,fullHeight:s,fluidContent:f,hideOnPrint:b}),C),G=xc(n),k=yc(t);return g.jsxs(pc,{open:r,onOpenChange:_,children:[g.jsx(mc,{asChild:!0,children:g.jsx(l,{children:o})}),g.jsx(bc,{children:g.jsx(hc,{ref:N,side:G,align:k,className:P,style:{zIndex:i},onEscapeKeyDown:R,onInteractOutside:D,...E,children:d?g.jsx("div",{className:"p-4",children:e}):e})})]})});_r.displayName="Popover";const wc=J("space-y-1",{variants:{tone:{magic:"border-l-4 border-purple-500 pl-4",default:""},disabled:{true:"opacity-50 pointer-events-none",false:""}},defaultVariants:{tone:"default",disabled:!1}}),Cc=J("flex items-center justify-center gap-2 rounded-lg transition-colors",{variants:{disabled:{true:"opacity-50 cursor-not-allowed",false:"cursor-pointer"},selected:{true:"border-black",false:""}},defaultVariants:{disabled:!1,selected:!1}}),Ta=p.forwardRef(({title:e,choices:n,selected:t,name:r="choiceList",allowMultiple:o=!1,titleHidden:a=!1,error:l,disabled:i=!1,onChange:c,tone:d,className:u,asChild:s=!1,...f},m)=>{const h=(v,y)=>{if(i)return;let w;o?y?w=[...t,v]:w=t.filter(C=>C!==v):w=y?[v]:[],c&&c(w,r)},b=()=>{i||c&&c([],r)},x=O(wc({tone:d==="magic"?"magic":"default",disabled:i}),u);return g.jsxs("fieldset",{ref:m,className:x,disabled:i,...f,children:[!a&&g.jsx("legend",{className:"text-sm font-medium text-gray-900 mb-3",children:e}),g.jsx(g.Fragment,{children:n.map((v,y)=>{const w=t.includes(v.value),C=`${r}-${v.value}-${y}`,E=o?"checkbox":"radio";return g.jsxs("label",{className:O(Cc({disabled:i||v.disabled,selected:w})),children:[g.jsx("input",{type:E,id:C,name:o?`${r}[]`:r,value:v.value,checked:w,disabled:i||v.disabled,onChange:N=>h(v.value,N.target.checked),className:"mt-0.5 h-4 w-4 text-black rounded accent-black cursor-pointer"}),g.jsxs("div",{className:"flex-1",children:[g.jsx(we,{variant:"bodyMd",as:"span",children:v.label}),v.helpText&&g.jsx(we,{variant:"bodySm",as:"p",tone:"subdued",children:v.helpText})]})]},v.value)})}),t.length>0&&g.jsx(ae,{variant:"plain",onClick:b,disabled:i,children:"Clear"}),l&&g.jsx(we,{variant:"bodySm",tone:"critical",as:"p",className:"mt-2",children:l})]})});Ta.displayName="ChoiceList";const Sc=e=>e&&{bg:"bg-gray-50","bg-inverse":"bg-gray-900","bg-surface":"bg-white","bg-surface-hover":"bg-gray-50","bg-surface-active":"bg-gray-100","bg-surface-selected":"bg-blue-50","bg-surface-disabled":"bg-gray-100","bg-surface-secondary":"bg-gray-25","bg-surface-secondary-hover":"bg-gray-50","bg-surface-secondary-active":"bg-gray-100","bg-surface-secondary-selected":"bg-blue-50","bg-surface-tertiary":"bg-gray-100","bg-surface-tertiary-hover":"bg-gray-200","bg-surface-tertiary-active":"bg-gray-300","bg-surface-brand":"bg-gray-900","bg-surface-brand-hover":"bg-gray-800","bg-surface-brand-active":"bg-gray-700","bg-surface-brand-selected":"bg-gray-800","bg-surface-info":"bg-blue-50","bg-surface-info-hover":"bg-blue-100","bg-surface-info-active":"bg-blue-200","bg-surface-success":"bg-green-50","bg-surface-success-hover":"bg-green-100","bg-surface-success-active":"bg-green-200","bg-surface-caution":"bg-yellow-50","bg-surface-caution-hover":"bg-yellow-100","bg-surface-caution-active":"bg-yellow-200","bg-surface-warning":"bg-orange-50","bg-surface-warning-hover":"bg-orange-100","bg-surface-warning-active":"bg-orange-200","bg-surface-critical":"bg-red-50","bg-surface-critical-hover":"bg-red-100","bg-surface-critical-active":"bg-red-200","bg-surface-emphasis":"bg-blue-600","bg-surface-emphasis-hover":"bg-blue-700","bg-surface-emphasis-active":"bg-blue-800","bg-surface-magic":"bg-purple-50","bg-surface-magic-hover":"bg-purple-100","bg-surface-magic-active":"bg-purple-200","bg-surface-inverse":"bg-gray-900","bg-surface-transparent":"bg-transparent","bg-surface-subdued":"bg-gray-25","bg-surface-neutral":"bg-gray-50","bg-surface-neutral-subdued":"bg-gray-100","bg-surface-caution-subdued":"bg-yellow-25","bg-surface-warning-subdued":"bg-orange-25","bg-surface-critical-subdued":"bg-red-25","bg-surface-success-subdued":"bg-green-25","bg-surface-highlight":"bg-blue-50","bg-surface-highlight-subdued":"bg-blue-25","bg-surface-magic-subdued":"bg-purple-25","bg-surface-info-subdued":"bg-blue-25","bg-surface-brand-subdued":"bg-gray-800"}[e]||"bg-white",_c=J("rounded-lg border text-gray-900",{variants:{roundedAbove:{xs:"rounded-lg",sm:"sm:rounded-lg",md:"md:rounded-lg",lg:"lg:rounded-lg",xl:"xl:rounded-lg"}},defaultVariants:{roundedAbove:"sm"}}),Rc=e=>{if(!e)return"p-4 sm:p-5";if(typeof e=="string")return{0:"p-0","025":"p-0.5","050":"p-1",100:"p-2",150:"p-3",200:"p-4",300:"p-6",400:"p-4",500:"p-5",600:"p-6",800:"p-8",1e3:"p-10",1200:"p-12",1600:"p-16",2e3:"p-20",2400:"p-24",2800:"p-28",3200:"p-32"}[e]||"p-4";const n=[],t={xs:"",sm:"sm:",md:"md:",lg:"lg:",xl:"xl:"};return Object.entries(e).forEach(([r,o])=>{var a;const l=t[r],i={0:"p-0","025":"p-0.5","050":"p-1",100:"p-2",150:"p-3",200:"p-4",300:"p-6",400:"p-4",500:"p-5",600:"p-6",800:"p-8",1e3:"p-10",1200:"p-12",1600:"p-16",2e3:"p-20",2400:"p-24",2800:"p-28",3200:"p-32"};o&&n.push(`${l}${((a=i[o])==null?void 0:a.replace("p-","p-"))||"p-4"}`)}),n.join(" ")||"p-4 sm:p-5"},Ec=p.forwardRef(({children:e,background:n="bg-surface",padding:t,roundedAbove:r="sm",className:o,...a},l)=>{const i=O(_c({roundedAbove:r}),Sc(n),Rc(t),"[border-color:rgba(227,227,227,1)]","[box-shadow:0px_4px_6px_-2px_rgba(26,26,26,0.20)]","[color:rgba(48,48,48,1)]",o);return g.jsx("div",{ref:l,className:i,...a,children:e})});Ec.displayName="Card";var Sn="Dialog",[Va,Vg]=dr(Sn),[jc,Me]=Va(Sn),za=e=>{const{__scopeDialog:n,children:t,open:r,defaultOpen:o,onOpenChange:a,modal:l=!0}=e,i=p.useRef(null),c=p.useRef(null),[d,u]=va({prop:r,defaultProp:o??!1,onChange:a,caller:Sn});return g.jsx(jc,{scope:n,triggerRef:i,contentRef:c,contentId:sn(),titleId:sn(),descriptionId:sn(),open:d,onOpenChange:u,onOpenToggle:p.useCallback(()=>u(s=>!s),[u]),modal:l,children:t})};za.displayName=Sn;var Ba="DialogTrigger",Nc=p.forwardRef((e,n)=>{const{__scopeDialog:t,...r}=e,o=Me(Ba,t),a=ke(n,o.triggerRef);return g.jsx(pe.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":jr(o.open),...r,ref:a,onClick:he(e.onClick,o.onOpenToggle)})});Nc.displayName=Ba;var Rr="DialogPortal",[kc,Ha]=Va(Rr,{forceMount:void 0}),$a=e=>{const{__scopeDialog:n,forceMount:t,children:r,container:o}=e,a=Me(Rr,n);return g.jsx(kc,{scope:n,forceMount:t,children:p.Children.map(r,l=>g.jsx(Nt,{present:t||a.open,children:g.jsx(wr,{asChild:!0,container:o,children:l})}))})};$a.displayName=Rr;var bn="DialogOverlay",Ga=p.forwardRef((e,n)=>{const t=Ha(bn,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,a=Me(bn,e.__scopeDialog);return a.modal?g.jsx(Nt,{present:r||a.open,children:g.jsx(Pc,{...o,ref:n})}):null});Ga.displayName=bn;var Mc=hn("DialogOverlay.RemoveScroll"),Pc=p.forwardRef((e,n)=>{const{__scopeDialog:t,...r}=e,o=Me(bn,t);return g.jsx(Cr,{as:Mc,allowPinchZoom:!0,shards:[o.contentRef],children:g.jsx(pe.div,{"data-state":jr(o.open),...r,ref:n,style:{pointerEvents:"auto",...r.style}})})}),gt="DialogContent",Wa=p.forwardRef((e,n)=>{const t=Ha(gt,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,a=Me(gt,e.__scopeDialog);return g.jsx(Nt,{present:r||a.open,children:a.modal?g.jsx(Fc,{...o,ref:n}):g.jsx(Ac,{...o,ref:n})})});Wa.displayName=gt;var Fc=p.forwardRef((e,n)=>{const t=Me(gt,e.__scopeDialog),r=p.useRef(null),o=ke(n,t.contentRef,r);return p.useEffect(()=>{const a=r.current;if(a)return ya(a)},[]),g.jsx(Ua,{...e,ref:o,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:he(e.onCloseAutoFocus,a=>{var l;a.preventDefault(),(l=t.triggerRef.current)==null||l.focus()}),onPointerDownOutside:he(e.onPointerDownOutside,a=>{const l=a.detail.originalEvent,i=l.button===0&&l.ctrlKey===!0;(l.button===2||i)&&a.preventDefault()}),onFocusOutside:he(e.onFocusOutside,a=>a.preventDefault())})}),Ac=p.forwardRef((e,n)=>{const t=Me(gt,e.__scopeDialog),r=p.useRef(!1),o=p.useRef(!1);return g.jsx(Ua,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{var l,i;(l=e.onCloseAutoFocus)==null||l.call(e,a),a.defaultPrevented||(r.current||(i=t.triggerRef.current)==null||i.focus(),a.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:a=>{var l,i;(l=e.onInteractOutside)==null||l.call(e,a),a.defaultPrevented||(r.current=!0,a.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const c=a.target;(i=t.triggerRef.current)!=null&&i.contains(c)&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&o.current&&a.preventDefault()}})}),Ua=p.forwardRef((e,n)=>{const{__scopeDialog:t,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...l}=e,i=Me(gt,t),c=p.useRef(null),d=ke(n,c);return qo(),g.jsxs(g.Fragment,{children:[g.jsx(ur,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:g.jsx(cr,{role:"dialog",id:i.contentId,"aria-describedby":i.descriptionId,"aria-labelledby":i.titleId,"data-state":jr(i.open),...l,ref:d,onDismiss:()=>i.onOpenChange(!1)})}),g.jsxs(g.Fragment,{children:[g.jsx(Ic,{titleId:i.titleId}),g.jsx(Dc,{contentRef:c,descriptionId:i.descriptionId})]})]})}),Er="DialogTitle",qa=p.forwardRef((e,n)=>{const{__scopeDialog:t,...r}=e,o=Me(Er,t);return g.jsx(pe.h2,{id:o.titleId,...r,ref:n})});qa.displayName=Er;var Xa="DialogDescription",Ya=p.forwardRef((e,n)=>{const{__scopeDialog:t,...r}=e,o=Me(Xa,t);return g.jsx(pe.p,{id:o.descriptionId,...r,ref:n})});Ya.displayName=Xa;var Ka="DialogClose",Za=p.forwardRef((e,n)=>{const{__scopeDialog:t,...r}=e,o=Me(Ka,t);return g.jsx(pe.button,{type:"button",...r,ref:n,onClick:he(e.onClick,()=>o.onOpenChange(!1))})});Za.displayName=Ka;function jr(e){return e?"open":"closed"}var Ja="DialogTitleWarning",[zg,Qa]=Si(Ja,{contentName:gt,titleName:Er,docsSlug:"dialog"}),Ic=({titleId:e})=>{const n=Qa(Ja),t=`\`${n.contentName}\` requires a \`${n.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${n.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${n.docsSlug}`;return p.useEffect(()=>{e&&(document.getElementById(e)||console.error(t))},[t,e]),null},Oc="DialogDescriptionWarning",Dc=({contentRef:e,descriptionId:n})=>{const t=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Qa(Oc).contentName}}.`;return p.useEffect(()=>{var r;const o=(r=e.current)==null?void 0:r.getAttribute("aria-describedby");n&&o&&(document.getElementById(n)||console.warn(t))},[t,e,n]),null},el=za,tl=$a,Nr=Ga,kr=Wa,nl=qa,rl=Ya,Lc=Za;function Tc({...e}){return g.jsx(el,{"data-slot":"dialog",...e})}function ol({...e}){return g.jsx(tl,{"data-slot":"dialog-portal",...e})}function al({className:e,...n}){return g.jsx(Nr,{"data-slot":"dialog-overlay",className:O("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...n})}function Vc({className:e,children:n,showCloseButton:t=!0,...r}){return g.jsxs(ol,{"data-slot":"dialog-portal",children:[g.jsx(al,{}),g.jsxs(kr,{"data-slot":"dialog-content",className:O("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r,children:[n,t&&g.jsxs(Lc,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[g.jsx(ir,{}),g.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}const zc=J("bg-white data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full translate-x-[-50%] translate-y-[-50%] gap-0 rounded-lg border border-gray-200 shadow-lg duration-200 font-inter",{variants:{variant:{small:"!max-w-sm",base:"!max-w-lg",large:"!max-w-2xl",max:"max-w-[90vw] max-h-[90vh]"}},defaultVariants:{variant:"base"}}),Bc=p.forwardRef(({children:e,src:n,variant:t="base",open:r,onShow:o,onHide:a,onOpenChange:l,className:i,showCloseButton:c=!0,actions:d,...u},s)=>{const f=p.useCallback(h=>{h&&o?o():!h&&a&&a(),l&&l(h)},[o,a,l]),m=()=>n?g.jsxs("div",{className:"flex flex-col h-full",children:[g.jsx("iframe",{src:n,className:"w-full flex-1 min-h-[400px] border-0",title:`Modal content from ${n}`,...u}),d&&g.jsx("div",{className:"border-t border-gray-200 px-6 py-4 bg-white flex justify-end gap-2",children:d})]}):g.jsxs("div",{className:"flex flex-col h-full",children:[g.jsx("div",{className:"flex-1",children:e}),d&&g.jsx("div",{className:"border-t border-gray-200 px-6 py-4 bg-white flex justify-end gap-2",children:d})]});return g.jsx(Tc,{open:r,onOpenChange:f,children:g.jsxs(ol,{children:[g.jsx(al,{className:"bg-black/50"}),g.jsx(Vc,{ref:s,className:O(zc({variant:t}),"p-0 overflow-hidden",i),showCloseButton:c,...u,children:m()})]})})});Bc.displayName="Modal";const Hc=J("flex items-center justify-between px-6 py-4 bg-gray-100 font-inter",{variants:{showBorder:{true:"border-b border-gray-200",false:""}},defaultVariants:{showBorder:!0}}),$c=J("text-lg font-semibold text-gray-900 leading-6"),Gc=J("flex items-center gap-2 cursor-pointer"),Wc=p.forwardRef(({title:e,children:n,className:t,showBorder:r=!0,...o},a)=>g.jsxs("div",{ref:a,className:O(Hc({showBorder:r}),t),...o,children:[e&&g.jsx("h2",{className:O($c()),children:e}),n&&g.jsx("div",{className:O(Gc()),children:n}),!e&&n&&g.jsx("div",{className:"flex-1"})]}));Wc.displayName="TitleBar";const po=e=>e&&{0:"0","025":"px","050":"0.5",100:"1",150:"1.5",200:"2",300:"3",400:"4",500:"5",600:"6",800:"8",1e3:"10",1200:"12",1600:"16",2e3:"20",2400:"24",2800:"28",3200:"32"}[e]||"",vt=(e,n="m")=>{if(!e)return"";if(typeof e=="string"){const r=po(e);return r?`-m${n}-${r}`:""}const t=[];return Object.entries(e).forEach(([r,o])=>{const a=po(o);if(a){const l=r==="xs"?"":`${r}:`;t.push(`${l}-m${n}-${a}`)}}),t.join(" ")},Uc=J("",{variants:{},defaultVariants:{}}),qc=p.forwardRef(({children:e,marginInline:n,marginBlock:t,marginBlockStart:r,marginBlockEnd:o,marginInlineStart:a,marginInlineEnd:l,className:i,...c},d)=>{const u=O(Uc(),vt(n,"x"),vt(t,"y"),vt(r,"t"),vt(o,"b"),vt(a,"l"),vt(l,"r"),i);return g.jsx("div",{ref:d,className:u,...c,children:e})});qc.displayName="Bleed";const Xc=J("min-h-screen",{variants:{width:{default:"max-w-[1200px] mx-auto",full:"w-full",narrow:"max-w-[760px] mx-auto"}},defaultVariants:{width:"default"}}),Yc=J("bg-white border-b border-[rgb(227,227,227)] px-6 py-4",{variants:{compact:{true:"pb-2",false:"pb-4"}},defaultVariants:{compact:!1}}),Kc=J("px-6 py-6",{variants:{},defaultVariants:{}}),Zc=p.forwardRef(({children:e,title:n,subtitle:t,titleMetadata:r,compactTitle:o=!1,titleHidden:a=!1,fullWidth:l=!1,narrowWidth:i=!1,primaryAction:c,secondaryActions:d=[],actionGroups:u=[],backAction:s,pagination:f,pageReadyAccessibilityLabel:m,filterActions:h=!1,additionalMetadata:b,onActionRollup:x,hasSubtitleMaxWidth:v=!1,className:y,...w},C)=>{const E=l?"full":i?"narrow":"default",N=(R,D="secondary")=>{var P;const G="url"in R&&R.url;return g.jsx(ae,{variant:D==="primary"?"primary":"secondary",onClick:R.onAction,url:G?R.url:void 0,external:"external"in R?R.external:void 0,target:"target"in R?R.target:void 0,download:"download"in R?R.download:void 0,disabled:"disabled"in R?R.disabled:void 0,loading:"loading"in R?R.loading:void 0,icon:"icon"in R?R.icon:void 0,accessibilityLabel:R.accessibilityLabel,children:R.content},R.id||((P=R.content)==null?void 0:P.toString()))},_=()=>{var R,D;return f?g.jsxs("div",{className:"flex items-center justify-between px-6 py-4 bg-white border-t border-[rgb(227,227,227)]",children:[g.jsx(ae,{variant:"secondary",disabled:!f.hasPrevious,onClick:f.onPrevious,url:f.previousURL,icon:g.jsx(or,{}),accessibilityLabel:((R=f.accessibilityLabels)==null?void 0:R.previous)||"Previous",children:"Previous"}),g.jsx(ae,{variant:"secondary",disabled:!f.hasNext,onClick:f.onNext,url:f.nextURL,icon:g.jsx(ar,{}),accessibilityLabel:((D=f.accessibilityLabels)==null?void 0:D.next)||"Next",children:"Next"})]}):null};return g.jsxs("div",{ref:C,className:O(Xc({width:E}),y),"aria-label":m,...w,children:[(n||s||c||d.length>0||u.length>0)&&g.jsxs("header",{className:O(Yc({compact:o})),children:[s&&g.jsx("div",{className:"mb-4",children:N(s,"secondary")}),(n||c||d.length>0||u.length>0)&&g.jsxs("div",{className:"flex items-start justify-between",children:[g.jsxs("div",{className:"flex-1 min-w-0",children:[n&&!a&&g.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[g.jsx(we,{as:"h1",variant:"headingLg",fontWeight:"semibold",children:n}),r&&g.jsx("div",{className:"flex items-center",children:r})]}),t&&g.jsx("div",{className:O("mt-1",v&&"max-w-[640px]"),children:g.jsx(we,{variant:"bodyMd",tone:"subdued",children:t})}),b&&g.jsx("div",{className:"mt-2",children:b})]}),(c||d.length>0||u.length>0)&&g.jsx("div",{className:"flex items-center gap-2 ml-4",children:g.jsxs($o,{children:[d.map(R=>N(R,"secondary")),c&&N(c,"primary")]})})]})]}),g.jsx("main",{className:O(Kc()),children:e}),_()]})});Zc.displayName="Page";const Jc=J("inline-flex items-center justify-center whitespace-nowrap font-sans text-xs font-medium leading-none rounded-lg border transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{tone:{default:"bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200",success:"bg-green-100 text-green-800 border-green-200 hover:bg-green-200",critical:"bg-red-100 text-red-800 border-red-200 hover:bg-red-200",warning:"bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200",attention:"bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-200",info:"bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200",new:"bg-purple-100 text-purple-800 border-purple-200 hover:bg-purple-200","read-only":"bg-gray-50 text-gray-600 border-gray-100 hover:bg-gray-100",enabled:"bg-emerald-100 text-emerald-800 border-emerald-200 hover:bg-emerald-200",subdued:"bg-gray-50 text-gray-500 border-gray-100 hover:bg-gray-100"},size:{small:"h-5 px-2 text-xs",medium:"h-6 px-2.5 text-xs"},progress:{incomplete:"opacity-60",partiallyComplete:"opacity-80",complete:"opacity-100"}},defaultVariants:{tone:"default",size:"medium",progress:"complete"}}),Qc=p.forwardRef(({children:e,tone:n="default",progress:t="complete",icon:r,size:o="medium",className:a,asChild:l=!1,...i},c)=>{const d=O(Jc({tone:n,size:o,progress:t}),a),u=p.useMemo(()=>{var s;return r?typeof r=="string"?r==="placeholder"?g.jsx("span",{className:"w-3 h-3 bg-current rounded-full opacity-50 mr-1"}):g.jsx("span",{className:"mr-1",children:r}):p.isValidElement(r)?p.cloneElement(r,{className:O("w-3 h-3 mr-1",(s=r.props)==null?void 0:s.className)}):typeof r=="function"?g.jsx(r,{className:"w-3 h-3 mr-1"}):null:null},[r]);return g.jsxs("span",{ref:c,className:d,...i,children:[u,e]})});Qc.displayName="Badge";const mo=e=>e&&{0:"0","025":"px","050":"0.5",100:"1",150:"1.5",200:"2",300:"3",400:"4",500:"5",600:"6",800:"8",1e3:"10",1200:"12",1600:"16",2e3:"20",2400:"24",2800:"28",3200:"32"}[e]||"",eu=e=>{if(!e)return"";if(typeof e=="string"){const t=mo(e);return t?`gap-${t}`:""}const n=[];return Object.entries(e).forEach(([t,r])=>{const o=mo(r);if(o){const a=t==="xs"?"":`${t}:`;n.push(`${a}gap-${o}`)}}),n.join(" ")},tu=J("flex",{variants:{align:{start:"justify-start",center:"justify-center",end:"justify-end","space-around":"justify-around","space-between":"justify-between","space-evenly":"justify-evenly"},direction:{row:"flex-row","row-reverse":"flex-row-reverse"},blockAlign:{start:"items-start",center:"items-center",end:"items-end",baseline:"items-baseline",stretch:"items-stretch"},wrap:{true:"flex-wrap",false:"flex-nowrap"}},defaultVariants:{align:"start",direction:"row",blockAlign:"start",wrap:!0}}),nu=p.forwardRef(({children:e,as:n="div",align:t="start",direction:r="row",blockAlign:o="start",gap:a,wrap:l=!0,className:i,...c},d)=>{const u=n,s=eu(a),f=O(tu({align:t,direction:r,blockAlign:o,wrap:l}),s,i);return g.jsx(u,{ref:d,className:f,...c,children:e})});nu.displayName="InlineStack";const bo=e=>e&&{0:"0","025":"px","050":"0.5",100:"1",150:"1.5",200:"2",300:"3",400:"4",500:"5",600:"6",800:"8",1e3:"10",1200:"12",1600:"16",2e3:"20",2400:"24",2800:"28",3200:"32"}[e]||"",ru=e=>{if(!e)return"";if(typeof e=="string"){const t=bo(e);return t?`gap-${t}`:""}const n=[];return Object.entries(e).forEach(([t,r])=>{const o=bo(r);if(o){const a=t==="xs"?"":`${t}:`;n.push(`${a}gap-${o}`)}}),n.join(" ")},ou=J("flex flex-col",{variants:{align:{start:"justify-start",center:"justify-center",end:"justify-end","space-around":"justify-around","space-between":"justify-between","space-evenly":"justify-evenly"},inlineAlign:{start:"items-start",center:"items-center",end:"items-end",baseline:"items-baseline",stretch:"items-stretch"},reverseOrder:{true:"flex-col-reverse",false:"flex-col"}},defaultVariants:{align:"start",inlineAlign:"start",reverseOrder:!1}}),au=p.forwardRef(({children:e,as:n="div",align:t="start",inlineAlign:r="start",gap:o,id:a,reverseOrder:l=!1,role:i,className:c,...d},u)=>{const s=n,f=ru(o),m=O(ou({align:t,inlineAlign:r,reverseOrder:l}),f,c);return g.jsx(s,{ref:u,className:m,id:a,role:i,...d,children:e})});au.displayName="BlockStack";const lu=J("flex items-center",{variants:{type:{page:"w-full",table:"w-auto"},hasLabel:{true:"justify-between",false:"justify-center"}},defaultVariants:{type:"page",hasLabel:!1}}),ho=J("inline-flex items-center h-6 justify-center cursor-pointer gap-1 p-1 text-sm font-medium bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white first:rounded-l-md last:rounded-r-md",{variants:{position:{previous:"rounded-l-md border-r-0",next:"rounded-r-md",single:""},disabled:{true:"text-gray-400 bg-gray-50",false:""}},defaultVariants:{position:"single",disabled:!1}}),ll=p.forwardRef(({nextKeys:e,previousKeys:n,nextTooltip:t,previousTooltip:r,nextURL:o,previousURL:a,hasNext:l=!1,hasPrevious:i=!1,accessibilityLabel:c="Pagination",accessibilityLabels:d,onNext:u,onPrevious:s,label:f,type:m="page",className:h,asChild:b=!1,...x},v)=>{p.useEffect(()=>{const N=_=>{n!=null&&n.includes(_.key)&&i&&s&&(_.preventDefault(),s()),e!=null&&e.includes(_.key)&&l&&u&&(_.preventDefault(),u())};return document.addEventListener("keydown",N),()=>document.removeEventListener("keydown",N)},[n,e,i,l,s,u]);const y=d==null?void 0:d.previous,w=d==null?void 0:d.next,C=()=>g.jsx("button",{type:"button",disabled:!i,onClick:s,"aria-label":y||"Previous",className:O(ho({position:f?"previous":"single",disabled:!i})),children:g.jsx(or,{className:"size-5"})}),E=()=>g.jsx("button",{type:"button",disabled:!l,onClick:u,"aria-label":w||"Next",className:O(ho({position:f?"next":"single",disabled:!l})),children:g.jsx(ar,{className:"size-5"})});return g.jsx("nav",{ref:v,"aria-label":c,className:O(lu({type:m,hasLabel:!!f}),h),...x,children:f?g.jsxs(g.Fragment,{children:[g.jsx(C,{}),g.jsx("div",{className:"flex-1 flex justify-center gap-2",children:typeof f=="string"?g.jsx(we,{className:"mx-1",variant:"bodySm",tone:"subdued",children:f}):f}),g.jsx(E,{})]}):g.jsxs("div",{className:"inline-flex",children:[g.jsx(C,{}),g.jsx(E,{})]})})});ll.displayName="Pagination";const iu=J("w-full",{variants:{mode:{DEFAULT:"",FILTERING:""},disabled:{true:"opacity-50 pointer-events-none",false:""}},defaultVariants:{mode:"DEFAULT",disabled:!1}}),su=({filter:e,disabled:n=!1,isOpen:t,onToggle:r,onClose:o})=>{const a=l=>{e.onChange(l,e.key)};return g.jsx(_r,{active:t,activator:g.jsxs(ae,{size:"micro",variant:"tertiary",className:"border-dashed ml-2",onClick:r,disabled:n,pressed:t,children:[e.label,e.selected.length>0&&g.jsx("span",{className:"ml-1 px-1.5 py-0.5 text-xs bg-blue-100 text-black rounded-full",children:e.selected.length})]}),onClose:()=>{},preferredPosition:"below",sectioned:!0,children:g.jsx(Ta,{title:e.label,titleHidden:!0,choices:e.choices,selected:e.selected,onChange:a,allowMultiple:e.allowMultiple})})},vo=({sortOptions:e,onSort:n,disabled:t,isOpen:r,onToggle:o,onClose:a})=>g.jsx(_r,{active:r,activator:g.jsx(ae,{variant:"tertiary",icon:g.jsx(Do,{className:"size-4"}),disclosure:!0,disabled:t,onClick:o,children:"Sort"}),onClose:a,children:g.jsx("div",{className:"p-2 min-w-48",children:e.map(l=>g.jsxs("button",{className:"w-full text-left px-3 py-2 hover:bg-[#f6f6f6] rounded text-sm",onClick:()=>{n&&n([l.value]),a()},children:[l.label," (",l.directionLabel,")"]},l.value))})}),du=p.forwardRef(({sortOptions:e,sortSelected:n,onSort:t,onSortKeyChange:r,onSortDirectionChange:o,onAddFilterClick:a,primaryAction:l,cancelAction:i,onEditStart:c,mode:d,disclosureZIndexOverride:u,setMode:s,disabled:f=!1,disableQueryField:m=!1,disableStickyMode:h,isFlushWhenSticky:b,canCreateNewView:x,onCreateNewView:v,filteringAccessibilityLabel:y="Filter",filteringAccessibilityTooltip:w,closeOnChildOverlayClick:C,disableKeyboardShortcuts:E,showEditColumnsButton:N=!1,autoFocusSearchField:_=!1,tabs:R,selected:D=0,onSelect:P,filters:G,appliedFilters:k,onClearAll:q,pinnedFilters:V,queryValue:X="",queryPlaceholder:M="Searching in all..",onQueryChange:I,onQueryClear:L,className:T,asChild:A=!1,...j},F)=>{const[z,Y]=p.useState(d==="FILTERING"),[U,le]=p.useState(null);p.useEffect(()=>{Y(d==="FILTERING")},[d]);const ee=Q=>{le(ie=>ie===Q?null:Q)},ne=()=>{le(null)},de=()=>{s(z?"DEFAULT":"FILTERING"),Y(!z),!z&&c&&c("FILTERING")},Ve=Q=>{I&&I(Q)},Pe=()=>{L&&L()};return g.jsx("div",{ref:F,className:O(iu({mode:d,disabled:f}),T),...j,children:g.jsx("div",{className:"p-1 border border-[#e3e3e3] rounded-md border-b-0 rounded-b-none",children:z?g.jsxs("div",{className:"space-y-2",children:[g.jsxs("div",{className:"flex items-center gap-2",children:[g.jsx("div",{className:"flex items-center gap-2",children:e&&e.length>0&&g.jsx(vo,{sortOptions:e,onSort:t,disabled:f,isOpen:U==="sort",onToggle:()=>ee("sort"),onClose:ne})}),g.jsx("div",{className:"flex-1",children:g.jsx(Go,{label:"",labelHidden:!0,value:X,onChange:Ve,placeholder:M,clearButton:X.length>0,onClearButtonClick:Pe,disabled:m||f,autoFocus:_,autoComplete:"off",size:"slim",className:"border border-[#e3e3e3] hover:border-[#e3e3e3] bg-white"})}),g.jsx(ae,{onClick:de,disabled:f,size:"medium",children:"Cancel"})]}),V&&V.length>0&&g.jsx("div",{className:"border-t border-gray-200 pt-0.5",children:g.jsx("div",{className:"flex flex-wrap gap-2",children:V.map(Q=>g.jsx(su,{filter:Q,disabled:f,isOpen:U===`filter-${Q.key}`,onToggle:()=>ee(`filter-${Q.key}`),onClose:ne},Q.key))})}),k&&k.length>0&&g.jsxs("div",{className:"flex flex-wrap gap-2",children:[k.map(Q=>g.jsxs("div",{className:"inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-black rounded-md text-sm",children:[g.jsx(we,{variant:"bodySm",children:Q.label}),g.jsx("button",{onClick:Q.onRemove,className:"p-0.5 hover:bg-black rounded",children:g.jsx(ir,{className:"size-3"})})]},Q.key)),q&&g.jsx(ae,{variant:"plain",size:"micro",onClick:q,children:"Clear all"})]}),(l||i)&&g.jsxs("div",{className:"flex justify-end gap-2",children:[i&&g.jsx(ae,{variant:"secondary",onClick:i.onAction,disabled:i.disabled,loading:i.loading,children:"Cancel"}),l&&g.jsx(ae,{variant:"primary",onClick:()=>{if(l.type==="save-as"&&v){const Q=prompt("Enter view name:");Q&&l.onAction(Q)}else l.onAction()},disabled:l.disabled,loading:l.loading,children:l.type==="save"?"Save":"Save as"})]})]}):g.jsxs("div",{className:"flex items-center justify-between",children:[g.jsxs("div",{className:"flex items-center gap-2",children:[e&&e.length>0&&g.jsx(vo,{sortOptions:e,onSort:t,disabled:f,isOpen:U==="sort",onToggle:()=>ee("sort"),onClose:ne}),R&&R.length>0&&g.jsx(g.Fragment,{children:R.map((Q,ie)=>g.jsx(ae,{size:"micro",variant:"tertiary",pressed:ie===D,onClick:()=>{Q.onAction(),P&&P(ie)},disabled:f,children:Q.content},Q.id))})]}),g.jsxs("div",{className:"flex items-center gap-2",children:[N&&g.jsx(ae,{variant:"tertiary",icon:g.jsx(Oo,{className:"size-4"}),accessibilityLabel:"Edit columns",disabled:f,children:"Edit columns"}),g.jsx(ae,{variant:"tertiary",className:"flex w-12 justify-items-end",icon:g.jsxs(g.Fragment,{children:[g.jsx(Io,{className:"size-5"}),g.jsx(Fo,{className:"size-5"})]}),onClick:de,pressed:!1,accessibilityLabel:y,disabled:f})]})]})})})});du.displayName="IndexFilters";/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function Qe(e,n){return typeof e=="function"?e(n):e}function xe(e,n){return t=>{n.setState(r=>({...r,[e]:Qe(t,r[e])}))}}function _n(e){return e instanceof Function}function cu(e){return Array.isArray(e)&&e.every(n=>typeof n=="number")}function uu(e,n){const t=[],r=o=>{o.forEach(a=>{t.push(a);const l=n(a);l!=null&&l.length&&r(l)})};return r(e),t}function B(e,n,t){let r=[],o;return a=>{let l;t.key&&t.debug&&(l=Date.now());const i=e(a);if(!(i.length!==r.length||i.some((d,u)=>r[u]!==d)))return o;r=i;let c;if(t.key&&t.debug&&(c=Date.now()),o=n(...i),t==null||t.onChange==null||t.onChange(o),t.key&&t.debug&&t!=null&&t.debug()){const d=Math.round((Date.now()-l)*100)/100,u=Math.round((Date.now()-c)*100)/100,s=u/16,f=(m,h)=>{for(m=String(m);m.length<h;)m=" "+m;return m};console.info(`%c⏱ ${f(u,5)} /${f(d,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*s,120))}deg 100% 31%);`,t==null?void 0:t.key)}return o}}function H(e,n,t,r){return{debug:()=>{var o;return(o=e==null?void 0:e.debugAll)!=null?o:e[n]},key:!1,onChange:r}}function gu(e,n,t,r){const o=()=>{var l;return(l=a.getValue())!=null?l:e.options.renderFallbackValue},a={id:`${n.id}_${t.id}`,row:n,column:t,getValue:()=>n.getValue(r),renderValue:o,getContext:B(()=>[e,t,n,a],(l,i,c,d)=>({table:l,column:i,row:c,cell:d,getValue:d.getValue,renderValue:d.renderValue}),H(e.options,"debugCells"))};return e._features.forEach(l=>{l.createCell==null||l.createCell(a,t,n,e)},{}),a}function fu(e,n,t,r){var o,a;const l={...e._getDefaultColumnDef(),...n},i=l.accessorKey;let c=(o=(a=l.id)!=null?a:i?typeof String.prototype.replaceAll=="function"?i.replaceAll(".","_"):i.replace(/\./g,"_"):void 0)!=null?o:typeof l.header=="string"?l.header:void 0,d;if(l.accessorFn?d=l.accessorFn:i&&(i.includes(".")?d=s=>{let f=s;for(const h of i.split(".")){var m;f=(m=f)==null?void 0:m[h]}return f}:d=s=>s[l.accessorKey]),!c)throw new Error;let u={id:`${String(c)}`,accessorFn:d,parent:r,depth:t,columnDef:l,columns:[],getFlatColumns:B(()=>[!0],()=>{var s;return[u,...(s=u.columns)==null?void 0:s.flatMap(f=>f.getFlatColumns())]},H(e.options,"debugColumns")),getLeafColumns:B(()=>[e._getOrderColumnsFn()],s=>{var f;if((f=u.columns)!=null&&f.length){let m=u.columns.flatMap(h=>h.getLeafColumns());return s(m)}return[u]},H(e.options,"debugColumns"))};for(const s of e._features)s.createColumn==null||s.createColumn(u,e);return u}const ue="debugHeaders";function xo(e,n,t){var r;let o={id:(r=t.id)!=null?r:n.id,column:n,index:t.index,isPlaceholder:!!t.isPlaceholder,placeholderId:t.placeholderId,depth:t.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const a=[],l=i=>{i.subHeaders&&i.subHeaders.length&&i.subHeaders.map(l),a.push(i)};return l(o),a},getContext:()=>({table:e,header:o,column:n})};return e._features.forEach(a=>{a.createHeader==null||a.createHeader(o,e)}),o}const pu={createTable:e=>{e.getHeaderGroups=B(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(n,t,r,o)=>{var a,l;const i=(a=r==null?void 0:r.map(u=>t.find(s=>s.id===u)).filter(Boolean))!=null?a:[],c=(l=o==null?void 0:o.map(u=>t.find(s=>s.id===u)).filter(Boolean))!=null?l:[],d=t.filter(u=>!(r!=null&&r.includes(u.id))&&!(o!=null&&o.includes(u.id)));return rn(n,[...i,...d,...c],e)},H(e.options,ue)),e.getCenterHeaderGroups=B(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(n,t,r,o)=>(t=t.filter(a=>!(r!=null&&r.includes(a.id))&&!(o!=null&&o.includes(a.id))),rn(n,t,e,"center")),H(e.options,ue)),e.getLeftHeaderGroups=B(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(n,t,r)=>{var o;const a=(o=r==null?void 0:r.map(l=>t.find(i=>i.id===l)).filter(Boolean))!=null?o:[];return rn(n,a,e,"left")},H(e.options,ue)),e.getRightHeaderGroups=B(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(n,t,r)=>{var o;const a=(o=r==null?void 0:r.map(l=>t.find(i=>i.id===l)).filter(Boolean))!=null?o:[];return rn(n,a,e,"right")},H(e.options,ue)),e.getFooterGroups=B(()=>[e.getHeaderGroups()],n=>[...n].reverse(),H(e.options,ue)),e.getLeftFooterGroups=B(()=>[e.getLeftHeaderGroups()],n=>[...n].reverse(),H(e.options,ue)),e.getCenterFooterGroups=B(()=>[e.getCenterHeaderGroups()],n=>[...n].reverse(),H(e.options,ue)),e.getRightFooterGroups=B(()=>[e.getRightHeaderGroups()],n=>[...n].reverse(),H(e.options,ue)),e.getFlatHeaders=B(()=>[e.getHeaderGroups()],n=>n.map(t=>t.headers).flat(),H(e.options,ue)),e.getLeftFlatHeaders=B(()=>[e.getLeftHeaderGroups()],n=>n.map(t=>t.headers).flat(),H(e.options,ue)),e.getCenterFlatHeaders=B(()=>[e.getCenterHeaderGroups()],n=>n.map(t=>t.headers).flat(),H(e.options,ue)),e.getRightFlatHeaders=B(()=>[e.getRightHeaderGroups()],n=>n.map(t=>t.headers).flat(),H(e.options,ue)),e.getCenterLeafHeaders=B(()=>[e.getCenterFlatHeaders()],n=>n.filter(t=>{var r;return!((r=t.subHeaders)!=null&&r.length)}),H(e.options,ue)),e.getLeftLeafHeaders=B(()=>[e.getLeftFlatHeaders()],n=>n.filter(t=>{var r;return!((r=t.subHeaders)!=null&&r.length)}),H(e.options,ue)),e.getRightLeafHeaders=B(()=>[e.getRightFlatHeaders()],n=>n.filter(t=>{var r;return!((r=t.subHeaders)!=null&&r.length)}),H(e.options,ue)),e.getLeafHeaders=B(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(n,t,r)=>{var o,a,l,i,c,d;return[...(o=(a=n[0])==null?void 0:a.headers)!=null?o:[],...(l=(i=t[0])==null?void 0:i.headers)!=null?l:[],...(c=(d=r[0])==null?void 0:d.headers)!=null?c:[]].map(u=>u.getLeafHeaders()).flat()},H(e.options,ue))}};function rn(e,n,t,r){var o,a;let l=0;const i=function(f,m){m===void 0&&(m=1),l=Math.max(l,m),f.filter(h=>h.getIsVisible()).forEach(h=>{var b;(b=h.columns)!=null&&b.length&&i(h.columns,m+1)},0)};i(e);let c=[];const d=(f,m)=>{const h={depth:m,id:[r,`${m}`].filter(Boolean).join("_"),headers:[]},b=[];f.forEach(x=>{const v=[...b].reverse()[0],y=x.column.depth===h.depth;let w,C=!1;if(y&&x.column.parent?w=x.column.parent:(w=x.column,C=!0),v&&(v==null?void 0:v.column)===w)v.subHeaders.push(x);else{const E=xo(t,w,{id:[r,m,w.id,x==null?void 0:x.id].filter(Boolean).join("_"),isPlaceholder:C,placeholderId:C?`${b.filter(N=>N.column===w).length}`:void 0,depth:m,index:b.length});E.subHeaders.push(x),b.push(E)}h.headers.push(x),x.headerGroup=h}),c.push(h),m>0&&d(b,m-1)},u=n.map((f,m)=>xo(t,f,{depth:l,index:m}));d(u,l-1),c.reverse();const s=f=>f.filter(m=>m.column.getIsVisible()).map(m=>{let h=0,b=0,x=[0];m.subHeaders&&m.subHeaders.length?(x=[],s(m.subHeaders).forEach(y=>{let{colSpan:w,rowSpan:C}=y;h+=w,x.push(C)})):h=1;const v=Math.min(...x);return b=b+v,m.colSpan=h,m.rowSpan=b,{colSpan:h,rowSpan:b}});return s((o=(a=c[0])==null?void 0:a.headers)!=null?o:[]),c}const mu=(e,n,t,r,o,a,l)=>{let i={id:n,index:r,original:t,depth:o,parentId:l,_valuesCache:{},_uniqueValuesCache:{},getValue:c=>{if(i._valuesCache.hasOwnProperty(c))return i._valuesCache[c];const d=e.getColumn(c);if(d!=null&&d.accessorFn)return i._valuesCache[c]=d.accessorFn(i.original,r),i._valuesCache[c]},getUniqueValues:c=>{if(i._uniqueValuesCache.hasOwnProperty(c))return i._uniqueValuesCache[c];const d=e.getColumn(c);if(d!=null&&d.accessorFn)return d.columnDef.getUniqueValues?(i._uniqueValuesCache[c]=d.columnDef.getUniqueValues(i.original,r),i._uniqueValuesCache[c]):(i._uniqueValuesCache[c]=[i.getValue(c)],i._uniqueValuesCache[c])},renderValue:c=>{var d;return(d=i.getValue(c))!=null?d:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>uu(i.subRows,c=>c.subRows),getParentRow:()=>i.parentId?e.getRow(i.parentId,!0):void 0,getParentRows:()=>{let c=[],d=i;for(;;){const u=d.getParentRow();if(!u)break;c.push(u),d=u}return c.reverse()},getAllCells:B(()=>[e.getAllLeafColumns()],c=>c.map(d=>gu(e,i,d,d.id)),H(e.options,"debugRows")),_getAllCellsByColumnId:B(()=>[i.getAllCells()],c=>c.reduce((d,u)=>(d[u.column.id]=u,d),{}),H(e.options,"debugRows"))};for(let c=0;c<e._features.length;c++){const d=e._features[c];d==null||d.createRow==null||d.createRow(i,e)}return i},bu={createColumn:(e,n)=>{e._getFacetedRowModel=n.options.getFacetedRowModel&&n.options.getFacetedRowModel(n,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():n.getPreFilteredRowModel(),e._getFacetedUniqueValues=n.options.getFacetedUniqueValues&&n.options.getFacetedUniqueValues(n,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=n.options.getFacetedMinMaxValues&&n.options.getFacetedMinMaxValues(n,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},il=(e,n,t)=>{var r,o;const a=t==null||(r=t.toString())==null?void 0:r.toLowerCase();return!!(!((o=e.getValue(n))==null||(o=o.toString())==null||(o=o.toLowerCase())==null)&&o.includes(a))};il.autoRemove=e=>Ee(e);const sl=(e,n,t)=>{var r;return!!(!((r=e.getValue(n))==null||(r=r.toString())==null)&&r.includes(t))};sl.autoRemove=e=>Ee(e);const dl=(e,n,t)=>{var r;return((r=e.getValue(n))==null||(r=r.toString())==null?void 0:r.toLowerCase())===(t==null?void 0:t.toLowerCase())};dl.autoRemove=e=>Ee(e);const cl=(e,n,t)=>{var r;return(r=e.getValue(n))==null?void 0:r.includes(t)};cl.autoRemove=e=>Ee(e);const ul=(e,n,t)=>!t.some(r=>{var o;return!((o=e.getValue(n))!=null&&o.includes(r))});ul.autoRemove=e=>Ee(e)||!(e!=null&&e.length);const gl=(e,n,t)=>t.some(r=>{var o;return(o=e.getValue(n))==null?void 0:o.includes(r)});gl.autoRemove=e=>Ee(e)||!(e!=null&&e.length);const fl=(e,n,t)=>e.getValue(n)===t;fl.autoRemove=e=>Ee(e);const pl=(e,n,t)=>e.getValue(n)==t;pl.autoRemove=e=>Ee(e);const Mr=(e,n,t)=>{let[r,o]=t;const a=e.getValue(n);return a>=r&&a<=o};Mr.resolveFilterValue=e=>{let[n,t]=e,r=typeof n!="number"?parseFloat(n):n,o=typeof t!="number"?parseFloat(t):t,a=n===null||Number.isNaN(r)?-1/0:r,l=t===null||Number.isNaN(o)?1/0:o;if(a>l){const i=a;a=l,l=i}return[a,l]};Mr.autoRemove=e=>Ee(e)||Ee(e[0])&&Ee(e[1]);const Je={includesString:il,includesStringSensitive:sl,equalsString:dl,arrIncludes:cl,arrIncludesAll:ul,arrIncludesSome:gl,equals:fl,weakEquals:pl,inNumberRange:Mr};function Ee(e){return e==null||e===""}const hu={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:xe("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,n)=>{e.getAutoFilterFn=()=>{const t=n.getCoreRowModel().flatRows[0],r=t==null?void 0:t.getValue(e.id);return typeof r=="string"?Je.includesString:typeof r=="number"?Je.inNumberRange:typeof r=="boolean"||r!==null&&typeof r=="object"?Je.equals:Array.isArray(r)?Je.arrIncludes:Je.weakEquals},e.getFilterFn=()=>{var t,r;return _n(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(t=(r=n.options.filterFns)==null?void 0:r[e.columnDef.filterFn])!=null?t:Je[e.columnDef.filterFn]},e.getCanFilter=()=>{var t,r,o;return((t=e.columnDef.enableColumnFilter)!=null?t:!0)&&((r=n.options.enableColumnFilters)!=null?r:!0)&&((o=n.options.enableFilters)!=null?o:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var t;return(t=n.getState().columnFilters)==null||(t=t.find(r=>r.id===e.id))==null?void 0:t.value},e.getFilterIndex=()=>{var t,r;return(t=(r=n.getState().columnFilters)==null?void 0:r.findIndex(o=>o.id===e.id))!=null?t:-1},e.setFilterValue=t=>{n.setColumnFilters(r=>{const o=e.getFilterFn(),a=r==null?void 0:r.find(u=>u.id===e.id),l=Qe(t,a?a.value:void 0);if(yo(o,l,e)){var i;return(i=r==null?void 0:r.filter(u=>u.id!==e.id))!=null?i:[]}const c={id:e.id,value:l};if(a){var d;return(d=r==null?void 0:r.map(u=>u.id===e.id?c:u))!=null?d:[]}return r!=null&&r.length?[...r,c]:[c]})}},createRow:(e,n)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=n=>{const t=e.getAllLeafColumns(),r=o=>{var a;return(a=Qe(n,o))==null?void 0:a.filter(l=>{const i=t.find(c=>c.id===l.id);if(i){const c=i.getFilterFn();if(yo(c,l.value,i))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(r)},e.resetColumnFilters=n=>{var t,r;e.setColumnFilters(n?[]:(t=(r=e.initialState)==null?void 0:r.columnFilters)!=null?t:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function yo(e,n,t){return(e&&e.autoRemove?e.autoRemove(n,t):!1)||typeof n>"u"||typeof n=="string"&&!n}const vu=(e,n,t)=>t.reduce((r,o)=>{const a=o.getValue(e);return r+(typeof a=="number"?a:0)},0),xu=(e,n,t)=>{let r;return t.forEach(o=>{const a=o.getValue(e);a!=null&&(r>a||r===void 0&&a>=a)&&(r=a)}),r},yu=(e,n,t)=>{let r;return t.forEach(o=>{const a=o.getValue(e);a!=null&&(r<a||r===void 0&&a>=a)&&(r=a)}),r},wu=(e,n,t)=>{let r,o;return t.forEach(a=>{const l=a.getValue(e);l!=null&&(r===void 0?l>=l&&(r=o=l):(r>l&&(r=l),o<l&&(o=l)))}),[r,o]},Cu=(e,n)=>{let t=0,r=0;if(n.forEach(o=>{let a=o.getValue(e);a!=null&&(a=+a)>=a&&(++t,r+=a)}),t)return r/t},Su=(e,n)=>{if(!n.length)return;const t=n.map(a=>a.getValue(e));if(!cu(t))return;if(t.length===1)return t[0];const r=Math.floor(t.length/2),o=t.sort((a,l)=>a-l);return t.length%2!==0?o[r]:(o[r-1]+o[r])/2},_u=(e,n)=>Array.from(new Set(n.map(t=>t.getValue(e))).values()),Ru=(e,n)=>new Set(n.map(t=>t.getValue(e))).size,Eu=(e,n)=>n.length,Tn={sum:vu,min:xu,max:yu,extent:wu,mean:Cu,median:Su,unique:_u,uniqueCount:Ru,count:Eu},ju={getDefaultColumnDef:()=>({aggregatedCell:e=>{var n,t;return(n=(t=e.getValue())==null||t.toString==null?void 0:t.toString())!=null?n:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:xe("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,n)=>{e.toggleGrouping=()=>{n.setGrouping(t=>t!=null&&t.includes(e.id)?t.filter(r=>r!==e.id):[...t??[],e.id])},e.getCanGroup=()=>{var t,r;return((t=e.columnDef.enableGrouping)!=null?t:!0)&&((r=n.options.enableGrouping)!=null?r:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var t;return(t=n.getState().grouping)==null?void 0:t.includes(e.id)},e.getGroupedIndex=()=>{var t;return(t=n.getState().grouping)==null?void 0:t.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const t=n.getCoreRowModel().flatRows[0],r=t==null?void 0:t.getValue(e.id);if(typeof r=="number")return Tn.sum;if(Object.prototype.toString.call(r)==="[object Date]")return Tn.extent},e.getAggregationFn=()=>{var t,r;if(!e)throw new Error;return _n(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(t=(r=n.options.aggregationFns)==null?void 0:r[e.columnDef.aggregationFn])!=null?t:Tn[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=n=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(n),e.resetGrouping=n=>{var t,r;e.setGrouping(n?[]:(t=(r=e.initialState)==null?void 0:r.grouping)!=null?t:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,n)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=t=>{if(e._groupingValuesCache.hasOwnProperty(t))return e._groupingValuesCache[t];const r=n.getColumn(t);return r!=null&&r.columnDef.getGroupingValue?(e._groupingValuesCache[t]=r.columnDef.getGroupingValue(e.original),e._groupingValuesCache[t]):e.getValue(t)},e._groupingValuesCache={}},createCell:(e,n,t,r)=>{e.getIsGrouped=()=>n.getIsGrouped()&&n.id===t.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&n.getIsGrouped(),e.getIsAggregated=()=>{var o;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((o=t.subRows)!=null&&o.length)}}};function Nu(e,n,t){if(!(n!=null&&n.length)||!t)return e;const r=e.filter(o=>!n.includes(o.id));return t==="remove"?r:[...n.map(o=>e.find(a=>a.id===o)).filter(Boolean),...r]}const ku={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:xe("columnOrder",e)}),createColumn:(e,n)=>{e.getIndex=B(t=>[Ot(n,t)],t=>t.findIndex(r=>r.id===e.id),H(n.options,"debugColumns")),e.getIsFirstColumn=t=>{var r;return((r=Ot(n,t)[0])==null?void 0:r.id)===e.id},e.getIsLastColumn=t=>{var r;const o=Ot(n,t);return((r=o[o.length-1])==null?void 0:r.id)===e.id}},createTable:e=>{e.setColumnOrder=n=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(n),e.resetColumnOrder=n=>{var t;e.setColumnOrder(n?[]:(t=e.initialState.columnOrder)!=null?t:[])},e._getOrderColumnsFn=B(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(n,t,r)=>o=>{let a=[];if(!(n!=null&&n.length))a=o;else{const l=[...n],i=[...o];for(;i.length&&l.length;){const c=l.shift(),d=i.findIndex(u=>u.id===c);d>-1&&a.push(i.splice(d,1)[0])}a=[...a,...i]}return Nu(a,t,r)},H(e.options,"debugTable"))}},Vn=()=>({left:[],right:[]}),Mu={getInitialState:e=>({columnPinning:Vn(),...e}),getDefaultOptions:e=>({onColumnPinningChange:xe("columnPinning",e)}),createColumn:(e,n)=>{e.pin=t=>{const r=e.getLeafColumns().map(o=>o.id).filter(Boolean);n.setColumnPinning(o=>{var a,l;if(t==="right"){var i,c;return{left:((i=o==null?void 0:o.left)!=null?i:[]).filter(s=>!(r!=null&&r.includes(s))),right:[...((c=o==null?void 0:o.right)!=null?c:[]).filter(s=>!(r!=null&&r.includes(s))),...r]}}if(t==="left"){var d,u;return{left:[...((d=o==null?void 0:o.left)!=null?d:[]).filter(s=>!(r!=null&&r.includes(s))),...r],right:((u=o==null?void 0:o.right)!=null?u:[]).filter(s=>!(r!=null&&r.includes(s)))}}return{left:((a=o==null?void 0:o.left)!=null?a:[]).filter(s=>!(r!=null&&r.includes(s))),right:((l=o==null?void 0:o.right)!=null?l:[]).filter(s=>!(r!=null&&r.includes(s)))}})},e.getCanPin=()=>e.getLeafColumns().some(t=>{var r,o,a;return((r=t.columnDef.enablePinning)!=null?r:!0)&&((o=(a=n.options.enableColumnPinning)!=null?a:n.options.enablePinning)!=null?o:!0)}),e.getIsPinned=()=>{const t=e.getLeafColumns().map(i=>i.id),{left:r,right:o}=n.getState().columnPinning,a=t.some(i=>r==null?void 0:r.includes(i)),l=t.some(i=>o==null?void 0:o.includes(i));return a?"left":l?"right":!1},e.getPinnedIndex=()=>{var t,r;const o=e.getIsPinned();return o?(t=(r=n.getState().columnPinning)==null||(r=r[o])==null?void 0:r.indexOf(e.id))!=null?t:-1:0}},createRow:(e,n)=>{e.getCenterVisibleCells=B(()=>[e._getAllVisibleCells(),n.getState().columnPinning.left,n.getState().columnPinning.right],(t,r,o)=>{const a=[...r??[],...o??[]];return t.filter(l=>!a.includes(l.column.id))},H(n.options,"debugRows")),e.getLeftVisibleCells=B(()=>[e._getAllVisibleCells(),n.getState().columnPinning.left],(t,r)=>(r??[]).map(o=>t.find(a=>a.column.id===o)).filter(Boolean).map(o=>({...o,position:"left"})),H(n.options,"debugRows")),e.getRightVisibleCells=B(()=>[e._getAllVisibleCells(),n.getState().columnPinning.right],(t,r)=>(r??[]).map(o=>t.find(a=>a.column.id===o)).filter(Boolean).map(o=>({...o,position:"right"})),H(n.options,"debugRows"))},createTable:e=>{e.setColumnPinning=n=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(n),e.resetColumnPinning=n=>{var t,r;return e.setColumnPinning(n?Vn():(t=(r=e.initialState)==null?void 0:r.columnPinning)!=null?t:Vn())},e.getIsSomeColumnsPinned=n=>{var t;const r=e.getState().columnPinning;if(!n){var o,a;return!!((o=r.left)!=null&&o.length||(a=r.right)!=null&&a.length)}return!!((t=r[n])!=null&&t.length)},e.getLeftLeafColumns=B(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(n,t)=>(t??[]).map(r=>n.find(o=>o.id===r)).filter(Boolean),H(e.options,"debugColumns")),e.getRightLeafColumns=B(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(n,t)=>(t??[]).map(r=>n.find(o=>o.id===r)).filter(Boolean),H(e.options,"debugColumns")),e.getCenterLeafColumns=B(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(n,t,r)=>{const o=[...t??[],...r??[]];return n.filter(a=>!o.includes(a.id))},H(e.options,"debugColumns"))}};function Pu(e){return e||(typeof document<"u"?document:null)}const on={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},zn=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),Fu={getDefaultColumnDef:()=>on,getInitialState:e=>({columnSizing:{},columnSizingInfo:zn(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:xe("columnSizing",e),onColumnSizingInfoChange:xe("columnSizingInfo",e)}),createColumn:(e,n)=>{e.getSize=()=>{var t,r,o;const a=n.getState().columnSizing[e.id];return Math.min(Math.max((t=e.columnDef.minSize)!=null?t:on.minSize,(r=a??e.columnDef.size)!=null?r:on.size),(o=e.columnDef.maxSize)!=null?o:on.maxSize)},e.getStart=B(t=>[t,Ot(n,t),n.getState().columnSizing],(t,r)=>r.slice(0,e.getIndex(t)).reduce((o,a)=>o+a.getSize(),0),H(n.options,"debugColumns")),e.getAfter=B(t=>[t,Ot(n,t),n.getState().columnSizing],(t,r)=>r.slice(e.getIndex(t)+1).reduce((o,a)=>o+a.getSize(),0),H(n.options,"debugColumns")),e.resetSize=()=>{n.setColumnSizing(t=>{let{[e.id]:r,...o}=t;return o})},e.getCanResize=()=>{var t,r;return((t=e.columnDef.enableResizing)!=null?t:!0)&&((r=n.options.enableColumnResizing)!=null?r:!0)},e.getIsResizing=()=>n.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,n)=>{e.getSize=()=>{let t=0;const r=o=>{if(o.subHeaders.length)o.subHeaders.forEach(r);else{var a;t+=(a=o.column.getSize())!=null?a:0}};return r(e),t},e.getStart=()=>{if(e.index>0){const t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=t=>{const r=n.getColumn(e.column.id),o=r==null?void 0:r.getCanResize();return a=>{if(!r||!o||(a.persist==null||a.persist(),Bn(a)&&a.touches&&a.touches.length>1))return;const l=e.getSize(),i=e?e.getLeafHeaders().map(v=>[v.column.id,v.column.getSize()]):[[r.id,r.getSize()]],c=Bn(a)?Math.round(a.touches[0].clientX):a.clientX,d={},u=(v,y)=>{typeof y=="number"&&(n.setColumnSizingInfo(w=>{var C,E;const N=n.options.columnResizeDirection==="rtl"?-1:1,_=(y-((C=w==null?void 0:w.startOffset)!=null?C:0))*N,R=Math.max(_/((E=w==null?void 0:w.startSize)!=null?E:0),-.999999);return w.columnSizingStart.forEach(D=>{let[P,G]=D;d[P]=Math.round(Math.max(G+G*R,0)*100)/100}),{...w,deltaOffset:_,deltaPercentage:R}}),(n.options.columnResizeMode==="onChange"||v==="end")&&n.setColumnSizing(w=>({...w,...d})))},s=v=>u("move",v),f=v=>{u("end",v),n.setColumnSizingInfo(y=>({...y,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},m=Pu(t),h={moveHandler:v=>s(v.clientX),upHandler:v=>{m==null||m.removeEventListener("mousemove",h.moveHandler),m==null||m.removeEventListener("mouseup",h.upHandler),f(v.clientX)}},b={moveHandler:v=>(v.cancelable&&(v.preventDefault(),v.stopPropagation()),s(v.touches[0].clientX),!1),upHandler:v=>{var y;m==null||m.removeEventListener("touchmove",b.moveHandler),m==null||m.removeEventListener("touchend",b.upHandler),v.cancelable&&(v.preventDefault(),v.stopPropagation()),f((y=v.touches[0])==null?void 0:y.clientX)}},x=Au()?{passive:!1}:!1;Bn(a)?(m==null||m.addEventListener("touchmove",b.moveHandler,x),m==null||m.addEventListener("touchend",b.upHandler,x)):(m==null||m.addEventListener("mousemove",h.moveHandler,x),m==null||m.addEventListener("mouseup",h.upHandler,x)),n.setColumnSizingInfo(v=>({...v,startOffset:c,startSize:l,deltaOffset:0,deltaPercentage:0,columnSizingStart:i,isResizingColumn:r.id}))}}},createTable:e=>{e.setColumnSizing=n=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(n),e.setColumnSizingInfo=n=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(n),e.resetColumnSizing=n=>{var t;e.setColumnSizing(n?{}:(t=e.initialState.columnSizing)!=null?t:{})},e.resetHeaderSizeInfo=n=>{var t;e.setColumnSizingInfo(n?zn():(t=e.initialState.columnSizingInfo)!=null?t:zn())},e.getTotalSize=()=>{var n,t;return(n=(t=e.getHeaderGroups()[0])==null?void 0:t.headers.reduce((r,o)=>r+o.getSize(),0))!=null?n:0},e.getLeftTotalSize=()=>{var n,t;return(n=(t=e.getLeftHeaderGroups()[0])==null?void 0:t.headers.reduce((r,o)=>r+o.getSize(),0))!=null?n:0},e.getCenterTotalSize=()=>{var n,t;return(n=(t=e.getCenterHeaderGroups()[0])==null?void 0:t.headers.reduce((r,o)=>r+o.getSize(),0))!=null?n:0},e.getRightTotalSize=()=>{var n,t;return(n=(t=e.getRightHeaderGroups()[0])==null?void 0:t.headers.reduce((r,o)=>r+o.getSize(),0))!=null?n:0}}};let an=null;function Au(){if(typeof an=="boolean")return an;let e=!1;try{const n={get passive(){return e=!0,!1}},t=()=>{};window.addEventListener("test",t,n),window.removeEventListener("test",t)}catch{e=!1}return an=e,an}function Bn(e){return e.type==="touchstart"}const Iu={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:xe("columnVisibility",e)}),createColumn:(e,n)=>{e.toggleVisibility=t=>{e.getCanHide()&&n.setColumnVisibility(r=>({...r,[e.id]:t??!e.getIsVisible()}))},e.getIsVisible=()=>{var t,r;const o=e.columns;return(t=o.length?o.some(a=>a.getIsVisible()):(r=n.getState().columnVisibility)==null?void 0:r[e.id])!=null?t:!0},e.getCanHide=()=>{var t,r;return((t=e.columnDef.enableHiding)!=null?t:!0)&&((r=n.options.enableHiding)!=null?r:!0)},e.getToggleVisibilityHandler=()=>t=>{e.toggleVisibility==null||e.toggleVisibility(t.target.checked)}},createRow:(e,n)=>{e._getAllVisibleCells=B(()=>[e.getAllCells(),n.getState().columnVisibility],t=>t.filter(r=>r.column.getIsVisible()),H(n.options,"debugRows")),e.getVisibleCells=B(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(t,r,o)=>[...t,...r,...o],H(n.options,"debugRows"))},createTable:e=>{const n=(t,r)=>B(()=>[r(),r().filter(o=>o.getIsVisible()).map(o=>o.id).join("_")],o=>o.filter(a=>a.getIsVisible==null?void 0:a.getIsVisible()),H(e.options,"debugColumns"));e.getVisibleFlatColumns=n("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=n("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=n("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=n("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=n("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var r;e.setColumnVisibility(t?{}:(r=e.initialState.columnVisibility)!=null?r:{})},e.toggleAllColumnsVisible=t=>{var r;t=(r=t)!=null?r:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((o,a)=>({...o,[a.id]:t||!(a.getCanHide!=null&&a.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(t=>!(t.getIsVisible!=null&&t.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(t=>t.getIsVisible==null?void 0:t.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var r;e.toggleAllColumnsVisible((r=t.target)==null?void 0:r.checked)}}};function Ot(e,n){return n?n==="center"?e.getCenterVisibleLeafColumns():n==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const Ou={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},Du={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:xe("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:n=>{var t;const r=(t=e.getCoreRowModel().flatRows[0])==null||(t=t._getAllCellsByColumnId()[n.id])==null?void 0:t.getValue();return typeof r=="string"||typeof r=="number"}}),createColumn:(e,n)=>{e.getCanGlobalFilter=()=>{var t,r,o,a;return((t=e.columnDef.enableGlobalFilter)!=null?t:!0)&&((r=n.options.enableGlobalFilter)!=null?r:!0)&&((o=n.options.enableFilters)!=null?o:!0)&&((a=n.options.getColumnCanGlobalFilter==null?void 0:n.options.getColumnCanGlobalFilter(e))!=null?a:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>Je.includesString,e.getGlobalFilterFn=()=>{var n,t;const{globalFilterFn:r}=e.options;return _n(r)?r:r==="auto"?e.getGlobalAutoFilterFn():(n=(t=e.options.filterFns)==null?void 0:t[r])!=null?n:Je[r]},e.setGlobalFilter=n=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(n)},e.resetGlobalFilter=n=>{e.setGlobalFilter(n?void 0:e.initialState.globalFilter)}}},Lu={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:xe("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let n=!1,t=!1;e._autoResetExpanded=()=>{var r,o;if(!n){e._queue(()=>{n=!0});return}if((r=(o=e.options.autoResetAll)!=null?o:e.options.autoResetExpanded)!=null?r:!e.options.manualExpanding){if(t)return;t=!0,e._queue(()=>{e.resetExpanded(),t=!1})}},e.setExpanded=r=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(r),e.toggleAllRowsExpanded=r=>{r??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=r=>{var o,a;e.setExpanded(r?{}:(o=(a=e.initialState)==null?void 0:a.expanded)!=null?o:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(r=>r.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>r=>{r.persist==null||r.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const r=e.getState().expanded;return r===!0||Object.values(r).some(Boolean)},e.getIsAllRowsExpanded=()=>{const r=e.getState().expanded;return typeof r=="boolean"?r===!0:!(!Object.keys(r).length||e.getRowModel().flatRows.some(o=>!o.getIsExpanded()))},e.getExpandedDepth=()=>{let r=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(o=>{const a=o.split(".");r=Math.max(r,a.length)}),r},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,n)=>{e.toggleExpanded=t=>{n.setExpanded(r=>{var o;const a=r===!0?!0:!!(r!=null&&r[e.id]);let l={};if(r===!0?Object.keys(n.getRowModel().rowsById).forEach(i=>{l[i]=!0}):l=r,t=(o=t)!=null?o:!a,!a&&t)return{...l,[e.id]:!0};if(a&&!t){const{[e.id]:i,...c}=l;return c}return r})},e.getIsExpanded=()=>{var t;const r=n.getState().expanded;return!!((t=n.options.getIsRowExpanded==null?void 0:n.options.getIsRowExpanded(e))!=null?t:r===!0||r!=null&&r[e.id])},e.getCanExpand=()=>{var t,r,o;return(t=n.options.getRowCanExpand==null?void 0:n.options.getRowCanExpand(e))!=null?t:((r=n.options.enableExpanding)!=null?r:!0)&&!!((o=e.subRows)!=null&&o.length)},e.getIsAllParentsExpanded=()=>{let t=!0,r=e;for(;t&&r.parentId;)r=n.getRow(r.parentId,!0),t=r.getIsExpanded();return t},e.getToggleExpandedHandler=()=>{const t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},Zn=0,Jn=10,Hn=()=>({pageIndex:Zn,pageSize:Jn}),Tu={getInitialState:e=>({...e,pagination:{...Hn(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:xe("pagination",e)}),createTable:e=>{let n=!1,t=!1;e._autoResetPageIndex=()=>{var r,o;if(!n){e._queue(()=>{n=!0});return}if((r=(o=e.options.autoResetAll)!=null?o:e.options.autoResetPageIndex)!=null?r:!e.options.manualPagination){if(t)return;t=!0,e._queue(()=>{e.resetPageIndex(),t=!1})}},e.setPagination=r=>{const o=a=>Qe(r,a);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(o)},e.resetPagination=r=>{var o;e.setPagination(r?Hn():(o=e.initialState.pagination)!=null?o:Hn())},e.setPageIndex=r=>{e.setPagination(o=>{let a=Qe(r,o.pageIndex);const l=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return a=Math.max(0,Math.min(a,l)),{...o,pageIndex:a}})},e.resetPageIndex=r=>{var o,a;e.setPageIndex(r?Zn:(o=(a=e.initialState)==null||(a=a.pagination)==null?void 0:a.pageIndex)!=null?o:Zn)},e.resetPageSize=r=>{var o,a;e.setPageSize(r?Jn:(o=(a=e.initialState)==null||(a=a.pagination)==null?void 0:a.pageSize)!=null?o:Jn)},e.setPageSize=r=>{e.setPagination(o=>{const a=Math.max(1,Qe(r,o.pageSize)),l=o.pageSize*o.pageIndex,i=Math.floor(l/a);return{...o,pageIndex:i,pageSize:a}})},e.setPageCount=r=>e.setPagination(o=>{var a;let l=Qe(r,(a=e.options.pageCount)!=null?a:-1);return typeof l=="number"&&(l=Math.max(-1,l)),{...o,pageCount:l}}),e.getPageOptions=B(()=>[e.getPageCount()],r=>{let o=[];return r&&r>0&&(o=[...new Array(r)].fill(null).map((a,l)=>l)),o},H(e.options,"debugTable")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:r}=e.getState().pagination,o=e.getPageCount();return o===-1?!0:o===0?!1:r<o-1},e.previousPage=()=>e.setPageIndex(r=>r-1),e.nextPage=()=>e.setPageIndex(r=>r+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var r;return(r=e.options.pageCount)!=null?r:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var r;return(r=e.options.rowCount)!=null?r:e.getPrePaginationRowModel().rows.length}}},$n=()=>({top:[],bottom:[]}),Vu={getInitialState:e=>({rowPinning:$n(),...e}),getDefaultOptions:e=>({onRowPinningChange:xe("rowPinning",e)}),createRow:(e,n)=>{e.pin=(t,r,o)=>{const a=r?e.getLeafRows().map(c=>{let{id:d}=c;return d}):[],l=o?e.getParentRows().map(c=>{let{id:d}=c;return d}):[],i=new Set([...l,e.id,...a]);n.setRowPinning(c=>{var d,u;if(t==="bottom"){var s,f;return{top:((s=c==null?void 0:c.top)!=null?s:[]).filter(b=>!(i!=null&&i.has(b))),bottom:[...((f=c==null?void 0:c.bottom)!=null?f:[]).filter(b=>!(i!=null&&i.has(b))),...Array.from(i)]}}if(t==="top"){var m,h;return{top:[...((m=c==null?void 0:c.top)!=null?m:[]).filter(b=>!(i!=null&&i.has(b))),...Array.from(i)],bottom:((h=c==null?void 0:c.bottom)!=null?h:[]).filter(b=>!(i!=null&&i.has(b)))}}return{top:((d=c==null?void 0:c.top)!=null?d:[]).filter(b=>!(i!=null&&i.has(b))),bottom:((u=c==null?void 0:c.bottom)!=null?u:[]).filter(b=>!(i!=null&&i.has(b)))}})},e.getCanPin=()=>{var t;const{enableRowPinning:r,enablePinning:o}=n.options;return typeof r=="function"?r(e):(t=r??o)!=null?t:!0},e.getIsPinned=()=>{const t=[e.id],{top:r,bottom:o}=n.getState().rowPinning,a=t.some(i=>r==null?void 0:r.includes(i)),l=t.some(i=>o==null?void 0:o.includes(i));return a?"top":l?"bottom":!1},e.getPinnedIndex=()=>{var t,r;const o=e.getIsPinned();if(!o)return-1;const a=(t=o==="top"?n.getTopRows():n.getBottomRows())==null?void 0:t.map(l=>{let{id:i}=l;return i});return(r=a==null?void 0:a.indexOf(e.id))!=null?r:-1}},createTable:e=>{e.setRowPinning=n=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(n),e.resetRowPinning=n=>{var t,r;return e.setRowPinning(n?$n():(t=(r=e.initialState)==null?void 0:r.rowPinning)!=null?t:$n())},e.getIsSomeRowsPinned=n=>{var t;const r=e.getState().rowPinning;if(!n){var o,a;return!!((o=r.top)!=null&&o.length||(a=r.bottom)!=null&&a.length)}return!!((t=r[n])!=null&&t.length)},e._getPinnedRows=(n,t,r)=>{var o;return((o=e.options.keepPinnedRows)==null||o?(t??[]).map(a=>{const l=e.getRow(a,!0);return l.getIsAllParentsExpanded()?l:null}):(t??[]).map(a=>n.find(l=>l.id===a))).filter(Boolean).map(a=>({...a,position:r}))},e.getTopRows=B(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(n,t)=>e._getPinnedRows(n,t,"top"),H(e.options,"debugRows")),e.getBottomRows=B(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(n,t)=>e._getPinnedRows(n,t,"bottom"),H(e.options,"debugRows")),e.getCenterRows=B(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(n,t,r)=>{const o=new Set([...t??[],...r??[]]);return n.filter(a=>!o.has(a.id))},H(e.options,"debugRows"))}},zu={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:xe("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=n=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(n),e.resetRowSelection=n=>{var t;return e.setRowSelection(n?{}:(t=e.initialState.rowSelection)!=null?t:{})},e.toggleAllRowsSelected=n=>{e.setRowSelection(t=>{n=typeof n<"u"?n:!e.getIsAllRowsSelected();const r={...t},o=e.getPreGroupedRowModel().flatRows;return n?o.forEach(a=>{a.getCanSelect()&&(r[a.id]=!0)}):o.forEach(a=>{delete r[a.id]}),r})},e.toggleAllPageRowsSelected=n=>e.setRowSelection(t=>{const r=typeof n<"u"?n:!e.getIsAllPageRowsSelected(),o={...t};return e.getRowModel().rows.forEach(a=>{Qn(o,a.id,r,!0,e)}),o}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=B(()=>[e.getState().rowSelection,e.getCoreRowModel()],(n,t)=>Object.keys(n).length?Gn(e,t):{rows:[],flatRows:[],rowsById:{}},H(e.options,"debugTable")),e.getFilteredSelectedRowModel=B(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(n,t)=>Object.keys(n).length?Gn(e,t):{rows:[],flatRows:[],rowsById:{}},H(e.options,"debugTable")),e.getGroupedSelectedRowModel=B(()=>[e.getState().rowSelection,e.getSortedRowModel()],(n,t)=>Object.keys(n).length?Gn(e,t):{rows:[],flatRows:[],rowsById:{}},H(e.options,"debugTable")),e.getIsAllRowsSelected=()=>{const n=e.getFilteredRowModel().flatRows,{rowSelection:t}=e.getState();let r=!!(n.length&&Object.keys(t).length);return r&&n.some(o=>o.getCanSelect()&&!t[o.id])&&(r=!1),r},e.getIsAllPageRowsSelected=()=>{const n=e.getPaginationRowModel().flatRows.filter(o=>o.getCanSelect()),{rowSelection:t}=e.getState();let r=!!n.length;return r&&n.some(o=>!t[o.id])&&(r=!1),r},e.getIsSomeRowsSelected=()=>{var n;const t=Object.keys((n=e.getState().rowSelection)!=null?n:{}).length;return t>0&&t<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const n=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:n.filter(t=>t.getCanSelect()).some(t=>t.getIsSelected()||t.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>n=>{e.toggleAllRowsSelected(n.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>n=>{e.toggleAllPageRowsSelected(n.target.checked)}},createRow:(e,n)=>{e.toggleSelected=(t,r)=>{const o=e.getIsSelected();n.setRowSelection(a=>{var l;if(t=typeof t<"u"?t:!o,e.getCanSelect()&&o===t)return a;const i={...a};return Qn(i,e.id,t,(l=r==null?void 0:r.selectChildren)!=null?l:!0,n),i})},e.getIsSelected=()=>{const{rowSelection:t}=n.getState();return Pr(e,t)},e.getIsSomeSelected=()=>{const{rowSelection:t}=n.getState();return er(e,t)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:t}=n.getState();return er(e,t)==="all"},e.getCanSelect=()=>{var t;return typeof n.options.enableRowSelection=="function"?n.options.enableRowSelection(e):(t=n.options.enableRowSelection)!=null?t:!0},e.getCanSelectSubRows=()=>{var t;return typeof n.options.enableSubRowSelection=="function"?n.options.enableSubRowSelection(e):(t=n.options.enableSubRowSelection)!=null?t:!0},e.getCanMultiSelect=()=>{var t;return typeof n.options.enableMultiRowSelection=="function"?n.options.enableMultiRowSelection(e):(t=n.options.enableMultiRowSelection)!=null?t:!0},e.getToggleSelectedHandler=()=>{const t=e.getCanSelect();return r=>{var o;t&&e.toggleSelected((o=r.target)==null?void 0:o.checked)}}}},Qn=(e,n,t,r,o)=>{var a;const l=o.getRow(n,!0);t?(l.getCanMultiSelect()||Object.keys(e).forEach(i=>delete e[i]),l.getCanSelect()&&(e[n]=!0)):delete e[n],r&&(a=l.subRows)!=null&&a.length&&l.getCanSelectSubRows()&&l.subRows.forEach(i=>Qn(e,i.id,t,r,o))};function Gn(e,n){const t=e.getState().rowSelection,r=[],o={},a=function(l,i){return l.map(c=>{var d;const u=Pr(c,t);if(u&&(r.push(c),o[c.id]=c),(d=c.subRows)!=null&&d.length&&(c={...c,subRows:a(c.subRows)}),u)return c}).filter(Boolean)};return{rows:a(n.rows),flatRows:r,rowsById:o}}function Pr(e,n){var t;return(t=n[e.id])!=null?t:!1}function er(e,n,t){var r;if(!((r=e.subRows)!=null&&r.length))return!1;let o=!0,a=!1;return e.subRows.forEach(l=>{if(!(a&&!o)&&(l.getCanSelect()&&(Pr(l,n)?a=!0:o=!1),l.subRows&&l.subRows.length)){const i=er(l,n);i==="all"?a=!0:(i==="some"&&(a=!0),o=!1)}}),o?"all":a?"some":!1}const tr=/([0-9]+)/gm,Bu=(e,n,t)=>ml(rt(e.getValue(t)).toLowerCase(),rt(n.getValue(t)).toLowerCase()),Hu=(e,n,t)=>ml(rt(e.getValue(t)),rt(n.getValue(t))),$u=(e,n,t)=>Fr(rt(e.getValue(t)).toLowerCase(),rt(n.getValue(t)).toLowerCase()),Gu=(e,n,t)=>Fr(rt(e.getValue(t)),rt(n.getValue(t))),Wu=(e,n,t)=>{const r=e.getValue(t),o=n.getValue(t);return r>o?1:r<o?-1:0},Uu=(e,n,t)=>Fr(e.getValue(t),n.getValue(t));function Fr(e,n){return e===n?0:e>n?1:-1}function rt(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function ml(e,n){const t=e.split(tr).filter(Boolean),r=n.split(tr).filter(Boolean);for(;t.length&&r.length;){const o=t.shift(),a=r.shift(),l=parseInt(o,10),i=parseInt(a,10),c=[l,i].sort();if(isNaN(c[0])){if(o>a)return 1;if(a>o)return-1;continue}if(isNaN(c[1]))return isNaN(l)?-1:1;if(l>i)return 1;if(i>l)return-1}return t.length-r.length}const It={alphanumeric:Bu,alphanumericCaseSensitive:Hu,text:$u,textCaseSensitive:Gu,datetime:Wu,basic:Uu},qu={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:xe("sorting",e),isMultiSortEvent:n=>n.shiftKey}),createColumn:(e,n)=>{e.getAutoSortingFn=()=>{const t=n.getFilteredRowModel().flatRows.slice(10);let r=!1;for(const o of t){const a=o==null?void 0:o.getValue(e.id);if(Object.prototype.toString.call(a)==="[object Date]")return It.datetime;if(typeof a=="string"&&(r=!0,a.split(tr).length>1))return It.alphanumeric}return r?It.text:It.basic},e.getAutoSortDir=()=>{const t=n.getFilteredRowModel().flatRows[0];return typeof(t==null?void 0:t.getValue(e.id))=="string"?"asc":"desc"},e.getSortingFn=()=>{var t,r;if(!e)throw new Error;return _n(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(t=(r=n.options.sortingFns)==null?void 0:r[e.columnDef.sortingFn])!=null?t:It[e.columnDef.sortingFn]},e.toggleSorting=(t,r)=>{const o=e.getNextSortingOrder(),a=typeof t<"u"&&t!==null;n.setSorting(l=>{const i=l==null?void 0:l.find(m=>m.id===e.id),c=l==null?void 0:l.findIndex(m=>m.id===e.id);let d=[],u,s=a?t:o==="desc";if(l!=null&&l.length&&e.getCanMultiSort()&&r?i?u="toggle":u="add":l!=null&&l.length&&c!==l.length-1?u="replace":i?u="toggle":u="replace",u==="toggle"&&(a||o||(u="remove")),u==="add"){var f;d=[...l,{id:e.id,desc:s}],d.splice(0,d.length-((f=n.options.maxMultiSortColCount)!=null?f:Number.MAX_SAFE_INTEGER))}else u==="toggle"?d=l.map(m=>m.id===e.id?{...m,desc:s}:m):u==="remove"?d=l.filter(m=>m.id!==e.id):d=[{id:e.id,desc:s}];return d})},e.getFirstSortDir=()=>{var t,r;return((t=(r=e.columnDef.sortDescFirst)!=null?r:n.options.sortDescFirst)!=null?t:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=t=>{var r,o;const a=e.getFirstSortDir(),l=e.getIsSorted();return l?l!==a&&((r=n.options.enableSortingRemoval)==null||r)&&(!(t&&(o=n.options.enableMultiRemove)!=null)||o)?!1:l==="desc"?"asc":"desc":a},e.getCanSort=()=>{var t,r;return((t=e.columnDef.enableSorting)!=null?t:!0)&&((r=n.options.enableSorting)!=null?r:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var t,r;return(t=(r=e.columnDef.enableMultiSort)!=null?r:n.options.enableMultiSort)!=null?t:!!e.accessorFn},e.getIsSorted=()=>{var t;const r=(t=n.getState().sorting)==null?void 0:t.find(o=>o.id===e.id);return r?r.desc?"desc":"asc":!1},e.getSortIndex=()=>{var t,r;return(t=(r=n.getState().sorting)==null?void 0:r.findIndex(o=>o.id===e.id))!=null?t:-1},e.clearSorting=()=>{n.setSorting(t=>t!=null&&t.length?t.filter(r=>r.id!==e.id):[])},e.getToggleSortingHandler=()=>{const t=e.getCanSort();return r=>{t&&(r.persist==null||r.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?n.options.isMultiSortEvent==null?void 0:n.options.isMultiSortEvent(r):!1))}}},createTable:e=>{e.setSorting=n=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(n),e.resetSorting=n=>{var t,r;e.setSorting(n?[]:(t=(r=e.initialState)==null?void 0:r.sorting)!=null?t:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},Xu=[pu,Iu,ku,Mu,bu,hu,Ou,Du,qu,ju,Lu,Tu,Vu,zu,Fu];function Yu(e){var n,t;const r=[...Xu,...(n=e._features)!=null?n:[]];let o={_features:r};const a=o._features.reduce((s,f)=>Object.assign(s,f.getDefaultOptions==null?void 0:f.getDefaultOptions(o)),{}),l=s=>o.options.mergeOptions?o.options.mergeOptions(a,s):{...a,...s};let i={...(t=e.initialState)!=null?t:{}};o._features.forEach(s=>{var f;i=(f=s.getInitialState==null?void 0:s.getInitialState(i))!=null?f:i});const c=[];let d=!1;const u={_features:r,options:{...a,...e},initialState:i,_queue:s=>{c.push(s),d||(d=!0,Promise.resolve().then(()=>{for(;c.length;)c.shift()();d=!1}).catch(f=>setTimeout(()=>{throw f})))},reset:()=>{o.setState(o.initialState)},setOptions:s=>{const f=Qe(s,o.options);o.options=l(f)},getState:()=>o.options.state,setState:s=>{o.options.onStateChange==null||o.options.onStateChange(s)},_getRowId:(s,f,m)=>{var h;return(h=o.options.getRowId==null?void 0:o.options.getRowId(s,f,m))!=null?h:`${m?[m.id,f].join("."):f}`},getCoreRowModel:()=>(o._getCoreRowModel||(o._getCoreRowModel=o.options.getCoreRowModel(o)),o._getCoreRowModel()),getRowModel:()=>o.getPaginationRowModel(),getRow:(s,f)=>{let m=(f?o.getPrePaginationRowModel():o.getRowModel()).rowsById[s];if(!m&&(m=o.getCoreRowModel().rowsById[s],!m))throw new Error;return m},_getDefaultColumnDef:B(()=>[o.options.defaultColumn],s=>{var f;return s=(f=s)!=null?f:{},{header:m=>{const h=m.header.column.columnDef;return h.accessorKey?h.accessorKey:h.accessorFn?h.id:null},cell:m=>{var h,b;return(h=(b=m.renderValue())==null||b.toString==null?void 0:b.toString())!=null?h:null},...o._features.reduce((m,h)=>Object.assign(m,h.getDefaultColumnDef==null?void 0:h.getDefaultColumnDef()),{}),...s}},H(e,"debugColumns")),_getColumnDefs:()=>o.options.columns,getAllColumns:B(()=>[o._getColumnDefs()],s=>{const f=function(m,h,b){return b===void 0&&(b=0),m.map(x=>{const v=fu(o,x,b,h),y=x;return v.columns=y.columns?f(y.columns,v,b+1):[],v})};return f(s)},H(e,"debugColumns")),getAllFlatColumns:B(()=>[o.getAllColumns()],s=>s.flatMap(f=>f.getFlatColumns()),H(e,"debugColumns")),_getAllFlatColumnsById:B(()=>[o.getAllFlatColumns()],s=>s.reduce((f,m)=>(f[m.id]=m,f),{}),H(e,"debugColumns")),getAllLeafColumns:B(()=>[o.getAllColumns(),o._getOrderColumnsFn()],(s,f)=>{let m=s.flatMap(h=>h.getLeafColumns());return f(m)},H(e,"debugColumns")),getColumn:s=>o._getAllFlatColumnsById()[s]};Object.assign(o,u);for(let s=0;s<o._features.length;s++){const f=o._features[s];f==null||f.createTable==null||f.createTable(o)}return o}function Ku(){return e=>B(()=>[e.options.data],n=>{const t={rows:[],flatRows:[],rowsById:{}},r=function(o,a,l){a===void 0&&(a=0);const i=[];for(let d=0;d<o.length;d++){const u=mu(e,e._getRowId(o[d],d,l),o[d],d,a,void 0,l==null?void 0:l.id);if(t.flatRows.push(u),t.rowsById[u.id]=u,i.push(u),e.options.getSubRows){var c;u.originalSubRows=e.options.getSubRows(o[d],d),(c=u.originalSubRows)!=null&&c.length&&(u.subRows=r(u.originalSubRows,a+1,u))}}return i};return t.rows=r(n),t},H(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function wo(e,n){return e?Zu(e)?p.createElement(e,n):e:null}function Zu(e){return Ju(e)||typeof e=="function"||Qu(e)}function Ju(e){return typeof e=="function"&&(()=>{const n=Object.getPrototypeOf(e);return n.prototype&&n.prototype.isReactComponent})()}function Qu(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function eg(e){const n={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[t]=p.useState(()=>({current:Yu(n)})),[r,o]=p.useState(()=>t.current.initialState);return t.current.setOptions(a=>({...a,...e,state:{...r,...e.state},onStateChange:l=>{o(l),e.onStateChange==null||e.onStateChange(l)}})),t.current}const Dt=p.forwardRef(({checked:e,onChange:n,disabled:t,"aria-label":r,className:o,...a},l)=>{const i=p.useRef(null);return p.useImperativeHandle(l,()=>i.current),p.useEffect(()=>{i.current&&(i.current.indeterminate=e==="indeterminate")},[e]),g.jsx("input",{ref:i,type:"checkbox",checked:e===!0,onChange:c=>n==null?void 0:n(c.target.checked),disabled:t,"aria-label":r,className:O("h-4 w-4 rounded border border-gray-300 focus:ring-2 focus:ring-black focus:ring-offset-2","disabled:cursor-not-allowed disabled:opacity-50","accent-black [&:checked]:bg-black [&:checked]:border-black","checked:bg-black checked:border-black",o),...a})});Dt.displayName="Checkbox";const tg=J("w-full border-collapse bg-white overflow-hidden relative before:content-[''] before:absolute before:inset-0 before:z-[101] before:pointer-events-none before:border before:border-[#e3e3e3] before:mix-blend-luminosity before:shadow-[var(--p-shadow-bevel-100)]",{variants:{condensed:{true:"text-sm",false:""},hasZebraStriping:{true:"[&_tbody_tr:nth-child(even)]:bg-gray-50",false:""},loading:{true:"opacity-50 pointer-events-none",false:""},hasIndexFilters:{true:"before:rounded-b-[8px]",false:"before:rounded-[8px]"}},defaultVariants:{condensed:!1,hasZebraStriping:!1,loading:!1,hasIndexFilters:!1}}),Ke=J("border-b border-gray-100 overflow-hidden bg-[#f7fafc] p-2 text-left text-xs font-medium text-gray-500 capitalize",{variants:{alignment:{start:"text-left",center:"text-center",end:"text-right"},sortable:{true:"cursor-pointer hover:bg-gray-100",false:""},sticky:{true:"sticky right-0 bg-gray-50",false:""},isSelectionColumn:{true:"w-12",false:""}},defaultVariants:{alignment:"start",sortable:!1,sticky:!1,isSelectionColumn:!1}}),bl=J("p-2 whitespace-nowrap text-sm text-gray-900",{variants:{alignment:{start:"text-left",center:"text-center",end:"text-right"},sticky:{true:"sticky right-0 bg-white",false:""},isSelectionColumn:{true:"w-12",false:""}},defaultVariants:{alignment:"start",sticky:!1,isSelectionColumn:!1}}),hl=p.forwardRef(({headings:e,promotedBulkActions:n,bulkActions:t,children:r,emptyState:o,sort:a,paginatedSelectAllActionText:l,paginatedSelectAllText:i,lastColumnSticky:c=!1,selectable:d=!0,sortable:u,defaultSortDirection:s="descending",sortDirection:f,sortColumnIndex:m,onSort:h,sortToggleLabels:b,hasZebraStriping:x=!1,pagination:v,itemCount:y,selectedItemsCount:w,resourceName:C,loading:E=!1,hasMoreItems:N,condensed:_=!1,onSelectionChange:R,className:D,asChild:P=!1,data:G,columns:k,hasIndexFilters:q=!1,...V},X)=>{const M=p.useMemo(()=>G&&k?eg({data:G,columns:k,getCoreRowModel:Ku()}):null,[G,k]),I=()=>{const T=typeof w=="number"&&w>0||w==="All",A=()=>!t||t.length===0||!T?null:g.jsx("div",{className:"flex items-center gap-2 ml-auto",children:t.map((j,F)=>"actions"in j?j.actions.map((z,Y)=>g.jsx(ae,{variant:"primary",size:"slim",onClick:z.onAction,disabled:z.disabled,tone:z.destructive?"critical":void 0,children:z.content},`${F}-${Y}`)):g.jsx(ae,{variant:"primary",size:"slim",onClick:j.onAction,disabled:j.disabled,tone:j.destructive?"critical":void 0,children:j.content},F))});return M?g.jsx("thead",{children:M.getHeaderGroups().map(j=>g.jsxs("tr",{children:[d&&g.jsx("th",{className:O(Ke({isSelectionColumn:!0})),children:g.jsx(Dt,{checked:w==="All"||w===y&&y>0?!0:typeof w=="number"&&w>0?"indeterminate":!1,onChange:F=>{R&&R("page",F,void 0,void 0)},"aria-label":`Select all ${(C==null?void 0:C.plural)||"items"}`})}),T?g.jsx("th",{colSpan:j.headers.length,className:O(Ke({alignment:"start"}),"text-left"),children:g.jsxs("div",{className:"flex items-center justify-between w-full",children:[g.jsx(we,{variant:"bodyMd",as:"span",children:w==="All"?`All ${(C==null?void 0:C.plural)||"items"} selected`:`${w} ${w===1?(C==null?void 0:C.singular)||"item":(C==null?void 0:C.plural)||"items"} selected`}),A()]})}):j.headers.map((F,z)=>g.jsx("th",{className:O(Ke({alignment:"start",sortable:F.column.getCanSort(),sticky:c&&z===j.headers.length-1})),children:F.isPlaceholder?null:wo(F.column.columnDef.header,F.getContext())},F.id))]},j.id))}):g.jsxs("thead",{children:[T&&g.jsxs("tr",{children:[d&&g.jsx("th",{className:O(Ke({isSelectionColumn:!0})),children:g.jsx(Dt,{checked:w==="All"||w===y&&y>0?!0:typeof w=="number"&&w>0?"indeterminate":!1,onChange:j=>{R&&R("page",j,void 0,void 0)},"aria-label":`Select all ${(C==null?void 0:C.plural)||"items"}`})}),g.jsx("th",{colSpan:e.length,className:O(Ke({alignment:"start"}),"text-left"),children:g.jsxs("div",{className:"flex items-center justify-between w-full",children:[g.jsx(we,{variant:"bodyMd",as:"span",children:w==="All"?`All ${(C==null?void 0:C.plural)||"items"} selected`:`${w} ${w===1?(C==null?void 0:C.singular)||"item":(C==null?void 0:C.plural)||"items"} selected`}),A()]})})]}),g.jsxs("tr",{children:[d&&!T&&g.jsx("th",{className:O(Ke({isSelectionColumn:!0})),children:g.jsx(Dt,{checked:w==="All"||w===y&&y>0?!0:typeof w=="number"&&w>0?"indeterminate":!1,onChange:j=>{R&&R("page",j,void 0,void 0)},"aria-label":`Select all ${(C==null?void 0:C.plural)||"items"}`})}),T&&d&&g.jsx("th",{className:O(Ke({isSelectionColumn:!0}))}),e.map((j,F)=>{const z=(u==null?void 0:u[F])||!1,Y=m===F,U=Y?f:void 0,le=()=>Y?U==="ascending"?"descending":"ascending":"descending",ee=()=>!z||!Y||!U?null:U==="descending"?g.jsx(lr,{className:"ml-1 h-4 w-4"}):g.jsx(rr,{className:"ml-1 h-4 w-4"}),ne=()=>{if(!z||!h)return;const de=le();h(F,de)};return g.jsx("th",{className:O(Ke({alignment:j.alignment||"start",sortable:z,sticky:c&&F===e.length-1}),z&&"cursor-pointer hover:bg-gray-50"),onClick:ne,children:g.jsxs("div",{className:"flex items-center",children:[g.jsx(we,{variant:"bodyMd",fontWeight:"semibold",children:(j.title,j.title)}),ee()]})},F)})]})]})},L=()=>{var T;if(M)return g.jsx("tbody",{children:(T=M.getRowModel().rows)!=null&&T.length?M.getRowModel().rows.map(j=>g.jsx("tr",{className:"hover:bg-gray-50",children:j.getVisibleCells().map((F,z)=>g.jsx("td",{className:O(bl({alignment:"start",sticky:c&&z===j.getVisibleCells().length-1})),children:wo(F.column.columnDef.cell,F.getContext())},F.id))},j.id)):g.jsx("tr",{children:g.jsx("td",{colSpan:(k==null?void 0:k.length)||e.length,className:"h-24 text-center",children:o||g.jsx(we,{variant:"bodyMd",tone:"subdued",children:"No results."})})})});const A=p.Children.map(r,j=>p.isValidElement(j)&&j.type===Ar?p.cloneElement(j,{selectable:d,onSelectionChange:R,resourceName:C,...j.props}):j);return g.jsx("tbody",{children:A})};return y===0&&o?g.jsx("div",{className:"flex items-center justify-center p-8",children:o}):g.jsxs("div",{className:"overflow-x-auto",children:[g.jsxs("table",{ref:X,className:O(tg({condensed:_,hasZebraStriping:x,loading:E,hasIndexFilters:q}),D),...V,children:[I(),L()]}),v&&g.jsx("div",{className:"flex justify-center py-4",children:g.jsx(ll,{type:"table",...v})})]})});hl.displayName="IndexTable";const Ar=p.forwardRef(({children:e,id:n,selected:t=!1,position:r,tone:o,disabled:a=!1,selectionRange:l,rowType:i="data",accessibilityLabel:c,onClick:d,onNavigation:u,className:s,selectable:f=!0,onSelectionChange:m,resourceName:h,...b},x)=>{const v=J("hover:bg-[#f7fafc] transition-colors border-b border-[#e3e3e3] last:border-b-0 ",{variants:{selected:{true:"bg-blue-50",indeterminate:"bg-blue-25",false:""},tone:{subdued:"opacity-60",success:"bg-green-50",warning:"bg-yellow-50",critical:"bg-red-50"},disabled:{true:"opacity-50 pointer-events-none",false:""},rowType:{data:"",subheader:"bg-gray-100 font-semibold",child:"pl-8"}},defaultVariants:{selected:!1,disabled:!1,rowType:"data"}}),y=w=>{const C=w.target;C.type==="checkbox"||C.closest('input[type="checkbox"]')||C.closest("button")||C.closest("a")||(f&&m&&!a&&m("single",!0,n,r),d&&d())};return g.jsxs("tr",{ref:x,className:O(v({selected:t===!0?!0:t==="indeterminate"?"indeterminate":!1,tone:o,disabled:a,rowType:i}),f&&!a&&"cursor-pointer",s),onClick:y,...b,children:[f&&g.jsx("td",{className:O(bl({isSelectionColumn:!0})),children:g.jsx(Dt,{checked:t===!0,onChange:w=>{m&&m("single",w,n,r)},disabled:a,"aria-label":c||`Select ${(h==null?void 0:h.singular)||"item"}`})}),e]})});Ar.displayName="IndexTable.Row";const vl=p.forwardRef(({as:e="td",id:n,children:t,className:r,flush:o=!1,colSpan:a,scope:l,headers:i,...c},d)=>{const u=J("border-gray-200 text-xs",{variants:{flush:{true:"",false:"p-2"}},defaultVariants:{flush:!1}});return g.jsx(e,{ref:d,id:n,className:O(u({flush:o}),r),colSpan:a,scope:l,headers:i,...c,children:t})});vl.displayName="IndexTable.Cell";const xl=hl;xl.Row=Ar;xl.Cell=vl;const ng=J("inline-block shrink-0 w-5 h-5",{variants:{tone:{base:"!text-white",inherit:"!text-inherit",subdued:"!text-muted-foreground",caution:"!text-amber-600",warning:"text-orange-600",critical:"text-destructive",interactive:"text-primary",info:"text-blue-600",success:"text-green-600",primary:"text-primary",emphasis:"text-foreground font-semibold",magic:"text-purple-600",textCaution:"text-amber-700",textWarning:"text-orange-700",textCritical:"text-red-700",textInfo:"text-blue-700",textSuccess:"text-green-700",textPrimary:"text-primary",textMagic:"text-purple-700"}},defaultVariants:{tone:"base"}}),yl=p.forwardRef(({source:e,tone:n="base",accessibilityLabel:t,className:r,...o},a)=>{const l=O(ng({tone:n}),r),i=p.useMemo(()=>{var c;return e?typeof e=="function"?g.jsx(e,{className:l}):p.isValidElement(e)?p.cloneElement(e,{className:O(l,(c=e.props)==null?void 0:c.className)}):typeof e=="string"?g.jsx("div",{className:l,dangerouslySetInnerHTML:{__html:e}}):e:null},[e,l]);return g.jsx("span",{ref:a,className:"inline-block shrink-0 w-5 h-5",...t?{"aria-label":t}:{"aria-hidden":!0},...o,children:i})});yl.displayName="Icon";const rg=J("fixed top-0 left-1/2 transform -translate-x-1/2 z-50 w-1/2 bg-[#303030] text-white shadow-lg border border-[#2c2c2c] font-inter transition-transform duration-200 ease-in-out rounded-[40px] p-0",{variants:{open:{true:"translate-y-0",false:"-translate-y-full"}},defaultVariants:{open:!1}}),og=el,ag=tl,wl=p.forwardRef(({className:e,...n},t)=>g.jsx(Nr,{ref:t,className:O("fixed inset-0 z-50 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...n}));wl.displayName=Nr.displayName;const Cl=p.forwardRef(({className:e,children:n,...t},r)=>g.jsxs(ag,{children:[g.jsx(wl,{}),g.jsx(kr,{ref:r,className:O("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 sm:rounded-lg font-inter rounded-[40px] border border-[#e3e3e3] bg-[#303030] text-white",e),...t,children:n})]}));Cl.displayName=kr.displayName;const Sl=p.forwardRef(({className:e,...n},t)=>g.jsx(nl,{ref:t,className:O("text-lg font-semibold leading-none tracking-tight",e),...n}));Sl.displayName=nl.displayName;const _l=p.forwardRef(({className:e,...n},t)=>g.jsx(rl,{ref:t,className:O("text-sm text-gray-600",e),...n}));_l.displayName=rl.displayName;const lg=p.forwardRef(({id:e,children:n,discardConfirmation:t=!1,open:r=!1,className:o,onSave:a,onDiscard:l,saveText:i="Save",discardText:c="Discard",saveLoading:d=!1,discardLoading:u=!1,...s},f)=>{const[m,h]=p.useState(!1),[b,x]=p.useState(null),v=!!(a||l),y=p.useCallback(_=>{t?(x(()=>_),h(!0)):_()},[t]),w=p.useMemo(()=>v||!n?null:p.Children.map(n,_=>{if(p.isValidElement(_)&&(_.type==="button"||_.type===ae)){const R=_.props;if(R.variant==="primary"&&t&&typeof R.onClick=="function")return p.cloneElement(_,{...R,onClick:D=>{D.preventDefault(),y(R.onClick)}})}return _}),[n,t,v,y]),C=p.useMemo(()=>v?g.jsxs(g.Fragment,{children:[l&&g.jsx(ae,{variant:"primary",loading:u,onClick:()=>y(l),children:c}),a&&g.jsx(ae,{loading:d,onClick:a,children:i})]}):null,[v,l,a,u,d,c,i,y]),E=p.useCallback(()=>{h(!1),b&&(b(),x(null))},[b]),N=p.useCallback(()=>{h(!1)},[]);return g.jsxs(g.Fragment,{children:[g.jsx("div",{ref:f,id:e,className:O(rg({open:r}),o),...s,children:g.jsxs("div",{className:"flex items-center justify-between p-2",children:[g.jsxs("div",{className:"flex items-center !justify-center gap-1",children:[g.jsx(yl,{source:Mo,tone:"base"}),g.jsx("span",{className:"text-sm font-medium",children:"Unsaved changes"})]}),g.jsx("div",{className:"flex items-center gap-3",children:v?C:w})]})}),g.jsx(og,{open:m,onOpenChange:h,children:g.jsxs(Cl,{className:"p-0 overflow-hidden bg-white",children:[g.jsx("div",{className:"px-6 py-4 bg-[#e3e3e3] border-b border-gray-200",children:g.jsx(Sl,{className:"text-lg font-semibold text-gray-900 leading-6",children:"Discard changes"})}),g.jsx("div",{className:"px-6 py-4",children:g.jsx(_l,{className:"text-sm text-gray-600",children:"Are you sure you want to discard your changes? This action cannot be undone."})}),g.jsxs("div",{className:"border-t border-gray-200 px-6 py-4 bg-white flex justify-end gap-3",children:[g.jsx(ae,{onClick:N,children:"Cancel"}),g.jsx(ae,{variant:"primary",tone:"critical",onClick:E,children:"Discard changes"})]})]})})]})});lg.displayName="ContextualSaveBar";function ig(e){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],t=document.createElement("style");t.type="text/css",n.appendChild(t),t.styleSheet?t.styleSheet.cssText=e:t.appendChild(document.createTextNode(e))}const sg=e=>{switch(e){case"success":return ug;case"info":return fg;case"warning":return gg;case"error":return pg;default:return null}},dg=Array(12).fill(0),cg=({visible:e,className:n})=>S.createElement("div",{className:["sonner-loading-wrapper",n].filter(Boolean).join(" "),"data-visible":e},S.createElement("div",{className:"sonner-spinner"},dg.map((t,r)=>S.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${r}`})))),ug=S.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},S.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),gg=S.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},S.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),fg=S.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},S.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),pg=S.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},S.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),mg=S.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},S.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),S.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),bg=()=>{const[e,n]=S.useState(document.hidden);return S.useEffect(()=>{const t=()=>{n(document.hidden)};return document.addEventListener("visibilitychange",t),()=>window.removeEventListener("visibilitychange",t)},[]),e};let nr=1;class hg{constructor(){this.subscribe=n=>(this.subscribers.push(n),()=>{const t=this.subscribers.indexOf(n);this.subscribers.splice(t,1)}),this.publish=n=>{this.subscribers.forEach(t=>t(n))},this.addToast=n=>{this.publish(n),this.toasts=[...this.toasts,n]},this.create=n=>{var t;const{message:r,...o}=n,a=typeof(n==null?void 0:n.id)=="number"||((t=n.id)==null?void 0:t.length)>0?n.id:nr++,l=this.toasts.find(c=>c.id===a),i=n.dismissible===void 0?!0:n.dismissible;return this.dismissedToasts.has(a)&&this.dismissedToasts.delete(a),l?this.toasts=this.toasts.map(c=>c.id===a?(this.publish({...c,...n,id:a,title:r}),{...c,...n,id:a,dismissible:i,title:r}):c):this.addToast({title:r,...o,dismissible:i,id:a}),a},this.dismiss=n=>(n?(this.dismissedToasts.add(n),requestAnimationFrame(()=>this.subscribers.forEach(t=>t({id:n,dismiss:!0})))):this.toasts.forEach(t=>{this.subscribers.forEach(r=>r({id:t.id,dismiss:!0}))}),n),this.message=(n,t)=>this.create({...t,message:n}),this.error=(n,t)=>this.create({...t,message:n,type:"error"}),this.success=(n,t)=>this.create({...t,type:"success",message:n}),this.info=(n,t)=>this.create({...t,type:"info",message:n}),this.warning=(n,t)=>this.create({...t,type:"warning",message:n}),this.loading=(n,t)=>this.create({...t,type:"loading",message:n}),this.promise=(n,t)=>{if(!t)return;let r;t.loading!==void 0&&(r=this.create({...t,promise:n,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));const o=Promise.resolve(n instanceof Function?n():n);let a=r!==void 0,l;const i=o.then(async d=>{if(l=["resolve",d],S.isValidElement(d))a=!1,this.create({id:r,type:"default",message:d});else if(xg(d)&&!d.ok){a=!1;const u=typeof t.error=="function"?await t.error(`HTTP error! status: ${d.status}`):t.error,s=typeof t.description=="function"?await t.description(`HTTP error! status: ${d.status}`):t.description,f=typeof u=="object"&&!S.isValidElement(u)?u:{message:u};this.create({id:r,type:"error",description:s,...f})}else if(d instanceof Error){a=!1;const u=typeof t.error=="function"?await t.error(d):t.error,s=typeof t.description=="function"?await t.description(d):t.description,f=typeof u=="object"&&!S.isValidElement(u)?u:{message:u};this.create({id:r,type:"error",description:s,...f})}else if(t.success!==void 0){a=!1;const u=typeof t.success=="function"?await t.success(d):t.success,s=typeof t.description=="function"?await t.description(d):t.description,f=typeof u=="object"&&!S.isValidElement(u)?u:{message:u};this.create({id:r,type:"success",description:s,...f})}}).catch(async d=>{if(l=["reject",d],t.error!==void 0){a=!1;const u=typeof t.error=="function"?await t.error(d):t.error,s=typeof t.description=="function"?await t.description(d):t.description,f=typeof u=="object"&&!S.isValidElement(u)?u:{message:u};this.create({id:r,type:"error",description:s,...f})}}).finally(()=>{a&&(this.dismiss(r),r=void 0),t.finally==null||t.finally.call(t)}),c=()=>new Promise((d,u)=>i.then(()=>l[0]==="reject"?u(l[1]):d(l[1])).catch(u));return typeof r!="string"&&typeof r!="number"?{unwrap:c}:Object.assign(r,{unwrap:c})},this.custom=(n,t)=>{const r=(t==null?void 0:t.id)||nr++;return this.create({jsx:n(r),id:r,...t}),r},this.getActiveToasts=()=>this.toasts.filter(n=>!this.dismissedToasts.has(n.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}const fe=new hg,vg=(e,n)=>{const t=(n==null?void 0:n.id)||nr++;return fe.addToast({title:e,...n,id:t}),t},xg=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",yg=vg,wg=()=>fe.toasts,Cg=()=>fe.getActiveToasts();Object.assign(yg,{success:fe.success,info:fe.info,warning:fe.warning,error:fe.error,custom:fe.custom,message:fe.message,promise:fe.promise,dismiss:fe.dismiss,loading:fe.loading},{getHistory:wg,getToasts:Cg});ig("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");function ln(e){return e.label!==void 0}const Sg=3,_g="24px",Rg="16px",Co=4e3,Eg=356,jg=14,Ng=45,kg=200;function Ae(...e){return e.filter(Boolean).join(" ")}function Mg(e){const[n,t]=e.split("-"),r=[];return n&&r.push(n),t&&r.push(t),r}const Pg=e=>{var n,t,r,o,a,l,i,c,d;const{invert:u,toast:s,unstyled:f,interacting:m,setHeights:h,visibleToasts:b,heights:x,index:v,toasts:y,expanded:w,removeToast:C,defaultRichColors:E,closeButton:N,style:_,cancelButtonStyle:R,actionButtonStyle:D,className:P="",descriptionClassName:G="",duration:k,position:q,gap:V,expandByDefault:X,classNames:M,icons:I,closeButtonAriaLabel:L="Close toast"}=e,[T,A]=S.useState(null),[j,F]=S.useState(null),[z,Y]=S.useState(!1),[U,le]=S.useState(!1),[ee,ne]=S.useState(!1),[de,Ve]=S.useState(!1),[Pe,Q]=S.useState(!1),[ie,ye]=S.useState(0),[Bt,Ge]=S.useState(0),Ce=S.useRef(s.duration||k||Co),ft=S.useRef(null),ce=S.useRef(null),at=v===0,Rn=v+1<=b,K=s.type,We=s.dismissible!==!1,Ht=s.className||"",kt=s.descriptionClassName||"",lt=S.useMemo(()=>x.findIndex($=>$.toastId===s.id)||0,[x,s.id]),$t=S.useMemo(()=>{var $;return($=s.closeButton)!=null?$:N},[s.closeButton,N]),it=S.useMemo(()=>s.duration||k||Co,[s.duration,k]),st=S.useRef(0),Ue=S.useRef(0),Gt=S.useRef(0),ze=S.useRef(null),[En,jn]=q.split("-"),Wt=S.useMemo(()=>x.reduce(($,re,se)=>se>=lt?$:$+re.height,0),[x,lt]),qe=bg(),dt=s.invert||u,pt=K==="loading";Ue.current=S.useMemo(()=>lt*V+Wt,[lt,Wt]),S.useEffect(()=>{Ce.current=it},[it]),S.useEffect(()=>{Y(!0)},[]),S.useEffect(()=>{const $=ce.current;if($){const re=$.getBoundingClientRect().height;return Ge(re),h(se=>[{toastId:s.id,height:re,position:s.position},...se]),()=>h(se=>se.filter(ge=>ge.toastId!==s.id))}},[h,s.id]),S.useLayoutEffect(()=>{if(!z)return;const $=ce.current,re=$.style.height;$.style.height="auto";const se=$.getBoundingClientRect().height;$.style.height=re,Ge(se),h(ge=>ge.find(oe=>oe.toastId===s.id)?ge.map(oe=>oe.toastId===s.id?{...oe,height:se}:oe):[{toastId:s.id,height:se,position:s.position},...ge])},[z,s.title,s.description,h,s.id,s.jsx,s.action,s.cancel]);const Se=S.useCallback(()=>{le(!0),ye(Ue.current),h($=>$.filter(re=>re.toastId!==s.id)),setTimeout(()=>{C(s)},kg)},[s,C,h,Ue]);S.useEffect(()=>{if(s.promise&&K==="loading"||s.duration===1/0||s.type==="loading")return;let $;return w||m||qe?(()=>{if(Gt.current<st.current){const re=new Date().getTime()-st.current;Ce.current=Ce.current-re}Gt.current=new Date().getTime()})():Ce.current!==1/0&&(st.current=new Date().getTime(),$=setTimeout(()=>{s.onAutoClose==null||s.onAutoClose.call(s,s),Se()},Ce.current)),()=>clearTimeout($)},[w,m,s,K,qe,Se]),S.useEffect(()=>{s.delete&&(Se(),s.onDismiss==null||s.onDismiss.call(s,s))},[Se,s.delete]);function Ut(){var $;if(I!=null&&I.loading){var re;return S.createElement("div",{className:Ae(M==null?void 0:M.loader,s==null||(re=s.classNames)==null?void 0:re.loader,"sonner-loader"),"data-visible":K==="loading"},I.loading)}return S.createElement(cg,{className:Ae(M==null?void 0:M.loader,s==null||($=s.classNames)==null?void 0:$.loader),visible:K==="loading"})}const qt=s.icon||(I==null?void 0:I[K])||sg(K);var Mt,Xt;return S.createElement("li",{tabIndex:0,ref:ce,className:Ae(P,Ht,M==null?void 0:M.toast,s==null||(n=s.classNames)==null?void 0:n.toast,M==null?void 0:M.default,M==null?void 0:M[K],s==null||(t=s.classNames)==null?void 0:t[K]),"data-sonner-toast":"","data-rich-colors":(Mt=s.richColors)!=null?Mt:E,"data-styled":!(s.jsx||s.unstyled||f),"data-mounted":z,"data-promise":!!s.promise,"data-swiped":Pe,"data-removed":U,"data-visible":Rn,"data-y-position":En,"data-x-position":jn,"data-index":v,"data-front":at,"data-swiping":ee,"data-dismissible":We,"data-type":K,"data-invert":dt,"data-swipe-out":de,"data-swipe-direction":j,"data-expanded":!!(w||X&&z),style:{"--index":v,"--toasts-before":v,"--z-index":y.length-v,"--offset":`${U?ie:Ue.current}px`,"--initial-height":X?"auto":`${Bt}px`,..._,...s.style},onDragEnd:()=>{ne(!1),A(null),ze.current=null},onPointerDown:$=>{$.button!==2&&(pt||!We||(ft.current=new Date,ye(Ue.current),$.target.setPointerCapture($.pointerId),$.target.tagName!=="BUTTON"&&(ne(!0),ze.current={x:$.clientX,y:$.clientY})))},onPointerUp:()=>{var $,re,se;if(de||!We)return;ze.current=null;const ge=Number((($=ce.current)==null?void 0:$.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),oe=Number(((re=ce.current)==null?void 0:re.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),Z=new Date().getTime()-((se=ft.current)==null?void 0:se.getTime()),me=T==="x"?ge:oe,Fe=Math.abs(me)/Z;if(Math.abs(me)>=Ng||Fe>.11){ye(Ue.current),s.onDismiss==null||s.onDismiss.call(s,s),F(T==="x"?ge>0?"right":"left":oe>0?"down":"up"),Se(),Ve(!0);return}else{var _e,Pt;(_e=ce.current)==null||_e.style.setProperty("--swipe-amount-x","0px"),(Pt=ce.current)==null||Pt.style.setProperty("--swipe-amount-y","0px")}Q(!1),ne(!1),A(null)},onPointerMove:$=>{var re,se,ge;if(!ze.current||!We||((re=window.getSelection())==null?void 0:re.toString().length)>0)return;const oe=$.clientY-ze.current.y,Z=$.clientX-ze.current.x;var me;const Fe=(me=e.swipeDirections)!=null?me:Mg(q);!T&&(Math.abs(Z)>1||Math.abs(oe)>1)&&A(Math.abs(Z)>Math.abs(oe)?"x":"y");let _e={x:0,y:0};const Pt=ct=>1/(1.5+Math.abs(ct)/20);if(T==="y"){if(Fe.includes("top")||Fe.includes("bottom"))if(Fe.includes("top")&&oe<0||Fe.includes("bottom")&&oe>0)_e.y=oe;else{const ct=oe*Pt(oe);_e.y=Math.abs(ct)<Math.abs(oe)?ct:oe}}else if(T==="x"&&(Fe.includes("left")||Fe.includes("right")))if(Fe.includes("left")&&Z<0||Fe.includes("right")&&Z>0)_e.x=Z;else{const ct=Z*Pt(Z);_e.x=Math.abs(ct)<Math.abs(Z)?ct:Z}(Math.abs(_e.x)>0||Math.abs(_e.y)>0)&&Q(!0),(se=ce.current)==null||se.style.setProperty("--swipe-amount-x",`${_e.x}px`),(ge=ce.current)==null||ge.style.setProperty("--swipe-amount-y",`${_e.y}px`)}},$t&&!s.jsx&&K!=="loading"?S.createElement("button",{"aria-label":L,"data-disabled":pt,"data-close-button":!0,onClick:pt||!We?()=>{}:()=>{Se(),s.onDismiss==null||s.onDismiss.call(s,s)},className:Ae(M==null?void 0:M.closeButton,s==null||(r=s.classNames)==null?void 0:r.closeButton)},(Xt=I==null?void 0:I.close)!=null?Xt:mg):null,(K||s.icon||s.promise)&&s.icon!==null&&((I==null?void 0:I[K])!==null||s.icon)?S.createElement("div",{"data-icon":"",className:Ae(M==null?void 0:M.icon,s==null||(o=s.classNames)==null?void 0:o.icon)},s.promise||s.type==="loading"&&!s.icon?s.icon||Ut():null,s.type!=="loading"?qt:null):null,S.createElement("div",{"data-content":"",className:Ae(M==null?void 0:M.content,s==null||(a=s.classNames)==null?void 0:a.content)},S.createElement("div",{"data-title":"",className:Ae(M==null?void 0:M.title,s==null||(l=s.classNames)==null?void 0:l.title)},s.jsx?s.jsx:typeof s.title=="function"?s.title():s.title),s.description?S.createElement("div",{"data-description":"",className:Ae(G,kt,M==null?void 0:M.description,s==null||(i=s.classNames)==null?void 0:i.description)},typeof s.description=="function"?s.description():s.description):null),S.isValidElement(s.cancel)?s.cancel:s.cancel&&ln(s.cancel)?S.createElement("button",{"data-button":!0,"data-cancel":!0,style:s.cancelButtonStyle||R,onClick:$=>{ln(s.cancel)&&We&&(s.cancel.onClick==null||s.cancel.onClick.call(s.cancel,$),Se())},className:Ae(M==null?void 0:M.cancelButton,s==null||(c=s.classNames)==null?void 0:c.cancelButton)},s.cancel.label):null,S.isValidElement(s.action)?s.action:s.action&&ln(s.action)?S.createElement("button",{"data-button":!0,"data-action":!0,style:s.actionButtonStyle||D,onClick:$=>{ln(s.action)&&(s.action.onClick==null||s.action.onClick.call(s.action,$),!$.defaultPrevented&&Se())},className:Ae(M==null?void 0:M.actionButton,s==null||(d=s.classNames)==null?void 0:d.actionButton)},s.action.label):null)};function So(){if(typeof window>"u"||typeof document>"u")return"ltr";const e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}function Fg(e,n){const t={};return[e,n].forEach((r,o)=>{const a=o===1,l=a?"--mobile-offset":"--offset",i=a?Rg:_g;function c(d){["top","right","bottom","left"].forEach(u=>{t[`${l}-${u}`]=typeof d=="number"?`${d}px`:d})}typeof r=="number"||typeof r=="string"?c(r):typeof r=="object"?["top","right","bottom","left"].forEach(d=>{r[d]===void 0?t[`${l}-${d}`]=i:t[`${l}-${d}`]=typeof r[d]=="number"?`${r[d]}px`:r[d]}):c(i)}),t}const Ag=S.forwardRef(function(e,n){const{invert:t,position:r="bottom-right",hotkey:o=["altKey","KeyT"],expand:a,closeButton:l,className:i,offset:c,mobileOffset:d,theme:u="light",richColors:s,duration:f,style:m,visibleToasts:h=Sg,toastOptions:b,dir:x=So(),gap:v=jg,icons:y,containerAriaLabel:w="Notifications"}=e,[C,E]=S.useState([]),N=S.useMemo(()=>Array.from(new Set([r].concat(C.filter(A=>A.position).map(A=>A.position)))),[C,r]),[_,R]=S.useState([]),[D,P]=S.useState(!1),[G,k]=S.useState(!1),[q,V]=S.useState(u!=="system"?u:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),X=S.useRef(null),M=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),I=S.useRef(null),L=S.useRef(!1),T=S.useCallback(A=>{E(j=>{var F;return(F=j.find(z=>z.id===A.id))!=null&&F.delete||fe.dismiss(A.id),j.filter(({id:z})=>z!==A.id)})},[]);return S.useEffect(()=>fe.subscribe(A=>{if(A.dismiss){requestAnimationFrame(()=>{E(j=>j.map(F=>F.id===A.id?{...F,delete:!0}:F))});return}setTimeout(()=>{Ro.flushSync(()=>{E(j=>{const F=j.findIndex(z=>z.id===A.id);return F!==-1?[...j.slice(0,F),{...j[F],...A},...j.slice(F+1)]:[A,...j]})})})}),[C]),S.useEffect(()=>{if(u!=="system"){V(u);return}if(u==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?V("dark"):V("light")),typeof window>"u")return;const A=window.matchMedia("(prefers-color-scheme: dark)");try{A.addEventListener("change",({matches:j})=>{V(j?"dark":"light")})}catch{A.addListener(({matches:j})=>{try{V(j?"dark":"light")}catch(F){console.error(F)}})}},[u]),S.useEffect(()=>{C.length<=1&&P(!1)},[C]),S.useEffect(()=>{const A=j=>{var F;if(o.every(Y=>j[Y]||j.code===Y)){var z;P(!0),(z=X.current)==null||z.focus()}j.code==="Escape"&&(document.activeElement===X.current||(F=X.current)!=null&&F.contains(document.activeElement))&&P(!1)};return document.addEventListener("keydown",A),()=>document.removeEventListener("keydown",A)},[o]),S.useEffect(()=>{if(X.current)return()=>{I.current&&(I.current.focus({preventScroll:!0}),I.current=null,L.current=!1)}},[X.current]),S.createElement("section",{ref:n,"aria-label":`${w} ${M}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},N.map((A,j)=>{var F;const[z,Y]=A.split("-");return C.length?S.createElement("ol",{key:A,dir:x==="auto"?So():x,tabIndex:-1,ref:X,className:i,"data-sonner-toaster":!0,"data-sonner-theme":q,"data-y-position":z,"data-x-position":Y,style:{"--front-toast-height":`${((F=_[0])==null?void 0:F.height)||0}px`,"--width":`${Eg}px`,"--gap":`${v}px`,...m,...Fg(c,d)},onBlur:U=>{L.current&&!U.currentTarget.contains(U.relatedTarget)&&(L.current=!1,I.current&&(I.current.focus({preventScroll:!0}),I.current=null))},onFocus:U=>{U.target instanceof HTMLElement&&U.target.dataset.dismissible==="false"||L.current||(L.current=!0,I.current=U.relatedTarget)},onMouseEnter:()=>P(!0),onMouseMove:()=>P(!0),onMouseLeave:()=>{G||P(!1)},onDragEnd:()=>P(!1),onPointerDown:U=>{U.target instanceof HTMLElement&&U.target.dataset.dismissible==="false"||k(!0)},onPointerUp:()=>k(!1)},C.filter(U=>!U.position&&j===0||U.position===A).map((U,le)=>{var ee,ne;return S.createElement(Pg,{key:U.id,icons:y,index:le,toast:U,defaultRichColors:s,duration:(ee=b==null?void 0:b.duration)!=null?ee:f,className:b==null?void 0:b.className,descriptionClassName:b==null?void 0:b.descriptionClassName,invert:t,visibleToasts:h,closeButton:(ne=b==null?void 0:b.closeButton)!=null?ne:l,interacting:G,position:A,style:b==null?void 0:b.style,unstyled:b==null?void 0:b.unstyled,classNames:b==null?void 0:b.classNames,cancelButtonStyle:b==null?void 0:b.cancelButtonStyle,actionButtonStyle:b==null?void 0:b.actionButtonStyle,closeButtonAriaLabel:b==null?void 0:b.closeButtonAriaLabel,removeToast:T,toasts:C.filter(de=>de.position==U.position),heights:_.filter(de=>de.position==U.position),setHeights:R,expandByDefault:a,gap:v,expanded:D,swipeDirections:e.swipeDirections})})):null}))}),Ig=J("font-inter text-sm font-medium rounded-lg border shadow-lg !w-fit !max-w-md",{variants:{variant:{default:"bg-[#0d1213] border-gray-700 text-white",success:"bg-[#0d1213] border-green-600 text-green-200",error:"bg-[#0d1213] border-red-600 text-red-200",warning:"bg-[#0d1213] border-yellow-600 text-yellow-200",info:"bg-[#0d1213] border-blue-600 text-blue-200"}},defaultVariants:{variant:"default"}}),Og=p.forwardRef(({className:e,theme:n="light",position:t="bottom-center",visibleToasts:r=3,closeButton:o=!0,richColors:a=!1,expand:l=!1,gap:i=14,offset:c="20px",...d},u)=>g.jsx(Ag,{ref:u,className:O("toaster group",e),theme:n,position:t,visibleToasts:r,closeButton:o,richColors:a,expand:l,gap:i,offset:c,toastOptions:{classNames:{toast:O(Ig({variant:"default"}),"group toast flex items-start justify-between gap-4 p-4"),description:"group-[.toast]:text-gray-300",actionButton:"group-[.toast]:bg-white group-[.toast]:text-[#0d1213] group-[.toast]:hover:bg-gray-200 group-[.toast]:rounded group-[.toast]:px-3 group-[.toast]:py-1 group-[.toast]:text-sm group-[.toast]:font-medium",cancelButton:"group-[.toast]:bg-gray-700 group-[.toast]:text-white group-[.toast]:hover:bg-gray-600 group-[.toast]:rounded group-[.toast]:px-3 group-[.toast]:py-1 group-[.toast]:text-sm group-[.toast]:font-medium",closeButton:"group-[.toast]:ml-auto group-[.toast]:bg-transparent group-[.toast]:border-0 group-[.toast]:text-white group-[.toast]:hover:bg-gray-700 group-[.toast]:hover:text-white group-[.toast]:p-1"}},...d}));Og.displayName="Toaster";export{Ec as I,hi as S,we as _,nu as a,ae as i,Go as q,au as u};
