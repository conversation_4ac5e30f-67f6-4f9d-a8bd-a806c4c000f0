import{w as f,v as y,a as n,o as s}from"./chunk-C37GKA54-BAI9agUh.js";import{S,I as b,_ as m,q as u,i as j}from"./index-C9EGKWKr.js";import"./index-BCQUowBv.js";function R({}){return[{title:"StoreSEO Agency - Register"},{name:"description",content:"Create your StoreSEO Agency account"}]}const k=f(function(){const p=y(),[a,g]=n.useState({email:"",password:""}),[i,l]=n.useState({}),[r,d]=n.useState(!1),h=/^[^\s@]+@[^\s@]+\.[^\s@]+$/,w=()=>{const e={};return a.email?h.test(a.email)||(e.email="Please enter a valid email address"):e.email="Email is required",a.password?a.password.length<6&&(e.password="Password must be at least 6 characters long"):e.password="Password is required",l(e),Object.keys(e).length===0},c=e=>t=>{g(o=>({...o,[e]:t})),i[e]&&l(o=>({...o,[e]:void 0}))},x=async e=>{if(e.preventDefault(),!!w()){d(!0);try{await new Promise(t=>setTimeout(t,1e3)),p("/dashboard")}catch(t){console.error("Registration failed:",t)}finally{d(!1)}}};return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"max-w-md w-full space-y-8",children:s.jsx(S,{children:s.jsx(b,{children:s.jsxs("form",{onSubmit:x,children:[s.jsxs(BlockStack,{gap:"200",children:[s.jsx(m,{as:"h1",variant:"headingLg",alignment:"center",children:"Create Your Account"}),s.jsx(m,{as:"p",variant:"bodyMd",tone:"subdued",alignment:"center",children:"Join StoreSEO Agency to get started"})]}),s.jsxs(BlockStack,{gap:"400",children:[s.jsx(u,{label:"Email",type:"email",value:a.email,onChange:c("email"),error:i.email,autoComplete:"email",placeholder:"Enter your email address",disabled:r}),s.jsx(u,{label:"Password",type:"password",value:a.password,onChange:c("password"),error:i.password,autoComplete:"new-password",placeholder:"Create a secure password",disabled:r}),s.jsx(j,{variant:"primary",size:"large",fullWidth:!0,submit:!0,loading:r,disabled:r,children:r?"Creating Account...":"Register"})]})]})})})})})});export{k as default,R as meta};
