{"name": "agency", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc", "server:dev": "nodemon server/index.js", "server:start": "node server/index.js"}, "dependencies": {"@react-router/node": "^7.7.1", "@react-router/serve": "^7.7.1", "@storeware/polaris": "github:Storeware-Apps/storeware-polaris#v0.1.0", "isbot": "^5.1.27", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.1", "express": "^4.18.2", "helmet": "^7.1.0", "cors": "^2.8.5", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "http-status": "^1.7.4", "xss-filters": "^1.2.7", "morgan": "^1.10.0", "express-fileupload": "^1.4.3", "yup": "^1.4.0", "dotenv": "^16.4.5"}, "devDependencies": {"@react-router/dev": "^7.7.1", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4", "nodemon": "^3.0.2"}}