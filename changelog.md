# Changelog

## [v3.9.1](https://github.com/Storeware-Apps/storeseo/releases/tag/v3.9.1)
📆 22nd July, 2025

**🚀 Features**

* Blog auto-write: AI blog generator by @rabby<PERSON>irul in #851, #846

**✨ Improvements**

* Feature/show-trial-stats-in-admin-and-slack by @j<PERSON>ian in #853
* Turn off image generation steps using feature flag by @rabby<PERSON>ir<PERSON> in #850
* Feature flag structure to turn off features or parts of a feature by @rabbykhairul in #848

**🐞 Bug fixes**

* Delete 'not synced' products from DB after product sync is complete by @rabbykhairul in #856
* Exclude EasyFlow app product options from product sync process & create/update webhooks by @rabbykhairul in #857

**🚑 Hot fixes**

* Hotfix by @jnahian in #859

## [v3.9.0](https://github.com/Storeware-Apps/storeseo/releases/tag/v3.9.0)
📆 13th July, 2025

**🚀 Features**

* Trial System Implementation for PRO Plans with 7-day trials by @jnahian in #836
* Weekly Email Send based on AUTO_AI_CONTENT_OPTIMIZATION_REPORT by @mdhossain07 in #801
* Email notification after bulk AI content optimization by @maruffahmed in #815
* Email notification after bulk image optimization batch complete by @maruffahmed in #820
* Implement gift 100 AI credits feature by @mdhossain07 in #843
* Subscription Cancelled Warning Banner by @jnahian in #847

**✨ Improvements**

* Update modals to App Bridge v4 modal by @maruffahmed in #810
* Fetch plan features from backend config file by @mdhossain07 in #842
* Update default email setting script by @mdhossain07 in #798
* Query optimization and dynamically handle email template with Resource auto optimization Status by @mdhossain07 in #817
* Update image alt text generator path by @jnahian in #819
* Add GitHub Copilot as a reviewer on pull requests by @jnahian in #840

**🐞 Bug fixes**

* Add on reset issue by @rabbykhairul in #821
* Ignore deleted products while restoring backups by @maruffahmed in #789
* Not updating optimization_settings for single image optimization by @maruffahmed in #825

**🚑 Hot fixes**

* Installed tag not registered in FluentCRM by @jnahian in #824
* Move StoreSEO branding removal to top by @jnahian in #826

**❄️ Code refactorings**

* Trial-end-queue implementation by @jnahian in #839
* Sync blogs separately after article sync by @rabbykhairul in #845

## [v3.8.5](https://github.com/Storeware-Apps/storeseo/releases/tag/v3.8.5)
📆 7th June, 2025

**🚀 Features**

* Add LLMs.txt Generator feature by @jnahian in #788, #809
* Initial documentation setup by @rabbykhairul in #802

**🐞 Bug fixes**

* Article image optimization bug fix by @rabbykhairul in #797
* Auto image optimization settings should open as modal by @maruffahmed in #813

## [v3.8.4](https://github.com/Storeware-Apps/storeseo/releases/tag/v3.8.4)
📆 4th June, 2025

**🚀 Features**

* Show image optimizer settings inside modal by @rabbykhairul in #791
* Bulk image optimizer - new queue system by @rabbykhairul in #792
* Auto image optimization for collections and blog posts by @rabbykhairul in #794
* Contextual save bar with discard functionality in Settings by @maruffahmed in #780

**✨ Improvements**

* Simplify queue process by @jnahian in #747
* Image optimizer page UI refactor by @rabbykhairul in #790
* Aggregate image optimizer stats from all resource types in Slack image optimization stats report by @rabbykhairul in #800
* Update product limit messaging in onboarding content by @jnahian in #811

**🐞 Bug fixes**

* New image optimizer queue system by @rabbykhairul in #793
* Image optimization by @rabbykhairul in #795
* Resolve image optimizer empty state persistence across tab navigation by @rabbykhairul in #808
* Truncate newlines in single line text metafields for Shopify API compatibility by @rabbykhairul in #812

**🚑 Hot fixes**

* Respect subscription plan product limits during new shop registration by @rabbykhairul in #799
* Upgrade Google Ads API from deprecated v17 to v20 by @rabbykhairul in #807

## [v3.8.3](https://github.com/Storeware-Apps/storeseo/releases/tag/v3.8.3)
📆 1st June, 2025

**🚀 Features**

* Blog post backup and restore settings by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/763
* OpenAI node service for article AI content generation by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/764
* Blog post bulk AI content API service by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/772
* UI implementation for article bulk optimization by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/774
* Restore blog posts original content by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/775
* Auto AI optimization for blog posts by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/777
* Auto AI Optimization settings as modal components by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/779
* Integrate LAI review by @rabbykhairul in https://github.com/Storeware-Apps/storeseo/pull/778

**✨ Improvements**

* Enhance AI tag management by adding functionality to keep individual tags by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/773
* Email logo updated by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/776

**🐞 Bug fixes**

* Restoring backup is getting stuck by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/785

**🚑 Hot fixes**

* Backup restore incorrectly restores AI optimization status by @rabbykhairul in #769

**🧰 Tooling**

* Add script to restore product manually by @maruffahmed in #758
* Enhance product data restoration tool with GraphQL integration by @maruffahmed in #761
* Command to remove duplicate webhooks by @jnahian in #770

**👥 Admin**

* Feature: Implement change shop status API by @mdhossain07 in #729
* Feature: Implement activity logs for app settings options & Bulk Sync Options by @mdhossain07 in #725
* Feature: Adding log origin, domain in the activity log for ADMIN and Delete logs API implemented by @mdhossain07 in #720
* Feature: Remove Deleted Shopify Products from DB and add shop name & domain in the Activity Log Subject by @mdhossain07 in #714
  

## [v3.8.2](https://github.com/Storeware-Apps/storeseo/releases/tag/v3.8.2)
📆 15th May, 2025

**🚀 Features**

* Implement AI content generation for collections by @maruffahmed in #746
* Collection bulk restore original content by @maruffahmed in #748
* Collection auto AI optimization by @maruffahmed in #749
* Introduce collection Backup & Restore settings by @maruffahmed in #755
* Node service for collections AI content generation by @maruffahmed in #745
* Collection bulk optimization API service by @maruffahmed in #744

**✨ Improvements**

* Plan validation and plan rules logic updated [CU-86et9a6j4] by @mdhossain07 in #760
* Change subscription plan layout to introduce starter plan by @jnahian in #781
* Optimize webhook registration process by @rabbykhairul in #765
* Optimize DB performance by @rabbykhairul in #767

**🐞 Bug fixes**

* Update blog posts tags from APP by @maruffahmed in #762
* Prevent multiple popover for index table resource reference by @maruffahmed in #757
* Reload image optimizer resource table after sync by @maruffahmed in #756
* HTML Sitemap menu config URL by @maruffahmed in #766
* Session not found in session storage by @jnahian in #771

**🚑 Hot fixes**

* Backup restore incorrectly restores AI optimization status by @rabbykhairul in #769

**🧰 Tooling**

* Move to pnpm by @jnahian in #740
* Update dependencies and improve UI components by @maruffahmed in #739
* Setup git town by @maruffahmed in #742

**👥 Admin**

* Feature: Implement change shop status API by @mdhossain07 in #729
* Feature: Implement activity logs for app settings options & Bulk Sync Options by @mdhossain07 in #725
* Feature: adding log origin, domain in the activity log for ADMIN and Delete logs API implemented by @mdhossain07 in #720
* Feature:Remove Deleted Shopify Products from DB and add shop name & domain in the Activity Log Subject by @mdhossain07 in #714
  

## [v3.8.1](https://github.com/Storeware-Apps/storeseo/releases/tag/v3.8.1)
📆 24th Apr, 2025

**🚀 Features** 

* Collections image optimization by @maruffahmed in #712 & #717
* Articles image optimizer by @maruffahmed in #721 & #722
* Add "support extension" to open chat window with app bridge by @maruffahmed in #738

**✨ Improvements** 

* Update OpenAiService to use new model enum and improve keyword extrac… by @jnahian in #741
* Command to restore original products backup from jsonl file by @rabbykhairul in #743
* Reusable image compare modal for image optimizer by @maruffahmed in #718
* AI optimize status filter for products by @maruffahmed in #730
* Addon usage count & usage limit reset logic refactor by @mdhossain07 in #753

**🚑 Hot fixes**

* Remove unnecessary webhooks by @jnahian in #737

**🧰 Tooling**

* N/A

**💡 RnD**

* N/A

**❄️ Code refactorings** 

* N/A

**🗒️ Documentation**

* N/A

**👥 Admin**

* N/A

**🔔 Slack Report**

* N/A

## [v3.8.0](https://github.com/Storeware-Apps/storeseo/releases/tag/v3.8.0)
📆 26th Mar, 2025

🚀 Features

- Onboarding Redesign Phage 3: Redesign subscription & checkout by @jnahian in #713
- Combine subscription with addons by @jnahian in #728

✨ Improvements

- Update default model to gpt-4o-mini in OpenAiService - CU-86ervhuf8 by @jnahian in #723
- Leagecy plan support by @jnahian in #733
- Enhance subscription data retrieval and improve addon handling by @jnahian in #735

🐞 Bug fixes

- Restore status in image optimizer ui by @maruffahmed in #731

🚑 Hot fixes

- Fix product bulk sync not syncing metafields by @rabbykhairul in #732

❄️ Code refactorings

- Update confirmation modal to App Bridge v4 by @jnahian in #724


## [v3.7.5](https://github.com/Storeware-Apps/storeseo/releases/tag/v3.7.5)
📆 9th Mar, 2025

**🚀 Features** 

* Feature: AI Content Optimizer usage email notification by @mdhossain07 in https://github.com/Storeware-Apps/storeseo/pull/711
* Feature: Image Optimizer Usage Notification email trigger  by @mdhossain07 in https://github.com/Storeware-Apps/storeseo/pull/707


**✨ Improvements** 

* Translation generation process updated by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/699
* Special char in URL - SEO score calculation for all types of entity by @rabbykhairul in https://github.com/Storeware-Apps/storeseo/pull/708
* Performance improvement - reduce DB scan in cronjobs by @rabbykhairul in https://github.com/Storeware-Apps/storeseo/pull/710
* Resuable method to check shop session validity & refactor old references by @rabbykhairul in https://github.com/Storeware-Apps/storeseo/pull/709
* Image optimizer percent based email integration Config & Addon Usage Queue implemeted by @mdhossain07 in https://github.com/Storeware-Apps/storeseo/pull/702


**🐞 Bug fixes**

* Enable weekly store report queue & sent emails from generate email queue by @mdhossain07 in https://github.com/Storeware-Apps/storeseo/pull/697
* Delete weekly sent email queue by @mdhossain07 in https://github.com/Storeware-Apps/storeseo/pull/698
* Email Notification Default Settings Issue by @mdhossain07 in https://github.com/Storeware-Apps/storeseo/pull/704
* update sitemaps page skeleton by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/696


**🚑 Hot fixes**

* Re-calculate approximate credit usage for Alt text generation by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/701
* Fix restore image file optimization loop by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/703

**❄️ Code refactorings** 

* Cleanup old onboarding by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/700
* Remove defaultProps from jsx components by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/705
* Convert "storeseo-enums" package to typescript by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/686
* Load & implement session using Shopify session storage driver by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/706

## [v3.7.4](https://github.com/Storeware-Apps/storeseo/releases/tag/v3.7.4)
📆 19th Feb, 2025

**🚀 Features** 

* Redesign onboarding flow by @jnahian in #684
* Clarity integration by @jnahian in #684
* Tracking system for background operations and bulk auto trigger by @maruffahmed in #685 & #680
* Send emails to user for by @mdhossain07 in #670, #676, #677, #681, #682
  * Google analytics not integrated
  * Google search console not integrated
  * Html Sitemap not generated
  * Product sync completed
  * Auto Image Optimizer turned off

**✨ Improvements** 

* Update Email Config Settings & display email card conditionally by @mdhossain07 in #665
* Send event based emails from Generate Email Queue by @mdhossain07 in #683
* Extend the email feature to support event based and regular interval emails by @rabbykhairul in #664

**🚑 Hot fixes**

* Don't bulk process queued items for inactive shops by @rabbykhairul in #693

## [v3.7.3](https://github.com/Storeware-Apps/storeseo/releases/tag/v3.7.3)
📆 13rd Feb, 2025

**🚀 Features** 

* Added weight information to local SEO product schema by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/666
* Auto Alt-text optimizer trigger, merging again due to code missing by @rabbykhairul in https://github.com/Storeware-Apps/storeseo/pull/687
* Ai generate shop industries by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/678
* Polish language support in translation files by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/690

**✨ Improvements** 

* Extract focus keyword using openai by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/658
* Onboarding/Subscription improvements by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/675
* Custom AI Content Optimizer plan support by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/672
* AI Credit usage warning banner in settings by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/691

**🐞 Bug fixes**

* Optimize index table filters and page navigation by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/689


**🧰 Tooling**

* Upgrade shopify api version to 2025-01 by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/668
* Migrate Shopify REST API calls to GraphQL endpoint by @rabbykhairul in https://github.com/Storeware-Apps/storeseo/pull/688

**❄️ Code refactorings** 

* Convert "storeseo-logger" package to typescript by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/662

## [v3.7.2](https://github.com/Storeware-Apps/storeseo/releases/tag/v3.7.2)
📆 3rd Feb, 2025

**🚀 Features** 

* Custom Ai Content Optimizer plan purchase support by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/674

**✨ Improvements** 

* Auto alt-text optimizer trigger by @rabbykhairul in https://github.com/Storeware-Apps/storeseo/pull/669
* Remove `Store Information` and `SEO Settings` Steps from onboarding by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/679
* Move free plan to the top of the subscription page by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/679

**🐞 Bug fixes**

* N/A

**🚑 Hot fixes**

* Fix alt-text auto generation pending if data is same after rewrite by @rabbykhairul in https://github.com/Storeware-Apps/storeseo/pull/671

**🧰 Tooling**

* N/A

**💡 RnD**

* N/A

**❄️ Code refactorings** 

* N/A

**🗒️ Documentation**

* N/A

**👥 Admin**

* N/A

**🔔 Slack Report**

* N/A

## [v3.7.1](https://github.com/Storeware-Apps/storeseo/releases/tag/v3.7.1)
📆 20th Jan, 2025

**🚀 Features** 

* Backup and Restore by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/644
* Revamp dashboard statistics UI by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/655
* Need Help Card in fix pages to trigger Crisp chat by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/657

**✨ Improvements** 

* Config managed webhooks for mandatory webhooks by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/660
* Generate ai content on stores default language by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/653
* Remove local seo Site links feature by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/656

**🐞 Bug fixes**

* Fix and simplify report statistics calculations using helper function by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/659

**🚑 Hot fixes**

* Onboard product analysis by @maruffahmed in https://github.com/Storeware-Apps/storeseo/pull/661

**🧰 Tooling**

* N/A

**💡 RnD**

* N/A

**❄️ Code refactorings** 

* Convert graphql queries by @jnahian in https://github.com/Storeware-Apps/storeseo/pull/643

**🗒️ Documentation**

* N/A

**👥 Admin**

* N/A

**🔔 Slack Report**

* N/A

## [v3.7.0](https://github.com/Storeware-Apps/storeseo/releases/tag/v3.7.0)
📆 1st Jan, 2025

**🚀 Features** 

* Multi Language SEO Feature by [@rabbykhairul](https://github.com/rabbykhairul) in [#650](https://github.com/Storeware-Apps/storeseo/pull/650)
* UX improvement for alt-text generation modal form by [@maruffahmed](https://github.com/maruffahmed) in [#642](https://github.com/Storeware-Apps/storeseo/pull/642)
* Add fileSize field to product image by [@maruffahmed](https://github.com/maruffahmed) in [#632](https://github.com/Storeware-Apps/storeseo/pull/632)

**✨ Improvements** 

* Delete delivered emails from DB using cron by [@mdhossain07](https://github.com/mdhossain07) in [#633](https://github.com/Storeware-Apps/storeseo/pull/633)
* Filter image by file size for image optimizer table by [@maruffahmed](https://github.com/maruffahmed) in [#639](https://github.com/Storeware-Apps/storeseo/pull/639)
* Add short by file size option for image optimizer table by [@maruffahmed](https://github.com/maruffahmed) in [#640](https://github.com/Storeware-Apps/storeseo/pull/640)
* Improve accuracy of approximate credit use for bulk ai optimization by [@maruffahmed](https://github.com/maruffahmed) in [#636](https://github.com/Storeware-Apps/storeseo/pull/636)

**🐞 Bug fixes**

* Image table and doc translations by [@maruffahmed](https://github.com/maruffahmed) in [#637](https://github.com/Storeware-Apps/storeseo/pull/637)

**🚑 Hot fixes**

* Auto ai optimization credit use sync logics by [@maruffahmed](https://github.com/maruffahmed) in [#635](https://github.com/Storeware-Apps/storeseo/pull/635)

**🧰 Tooling**

* Upgrade vite tool to v6.0.1 by [@rabbykhairul](https://github.com/rabbykhairul) in [#638](https://github.com/Storeware-Apps/storeseo/pull/638)
* Upgrade rabbitmq by [@jnahian](https://github.com/jnahian) in [#645](https://github.com/Storeware-Apps/storeseo/pull/645)

**💡 RnD**

* N/A

**❄️ Code refactorings** 

* N/A

**🗒️ Documentation**

* N/A

**👥 Admin**

* N/A

**🔔 Slack Report**

* AI Content Optimizer stats add to daily image optimizer report by [@jnahian](https://github.com/jnahian) in [#641](https://github.com/Storeware-Apps/storeseo/pull/641)

# [v3.5.3]()

# [v3.4.5]()

# [v3.4.2]()

📅 27 May, 2024

### 🚀🚀 Features 🚀🚀

- Keep track 🛤 of add-on usage by each purchase 💵 and reset interval by @rabbykhairul in https://github.com/WPDevelopers/storeseo/pull/427
- Sitemaps for collections, pages and blog posts 📍🗺️ by @maruffahmed in https://github.com/WPDevelopers/storeseo/pull/426
- StoreSEO Report for Collections 📊 by @maruffahmed in https://github.com/WPDevelopers/storeseo/pull/415
- Bring back **AI Credit Purchase** in `onboading` by @jnahian

### ✨✨ Improvements ✨✨

- For single blog post sync, if doesn't sync due to not found in Shopify notify user and delete entry 🗑️ by @maruffahmed in https://github.com/WPDevelopers/storeseo/pull/428
- For single page sync, if doesn't sync due to not found in Shopify notify user and delete entry 🗑️ by @maruffahmed in https://github.com/WPDevelopers/storeseo/pull/429
- Update pagination for Optimize-SEO resources (Product, collection, page, blog post) 📜 by @maruffahmed in https://github.com/WPDevelopers/storeseo/pull/437
- Tables are now more clean 🧼 by @maruffahmed in https://github.com/WPDevelopers/storeseo/pull/431
- Ensure that all tables are optimized for mobile devices 📱 by @maruffahmed in https://github.com/WPDevelopers/storeseo/pull/434
- Replaced `Current plan` badge with Icon in **Subscriptions** page by @jnahian
- Added `(Y)` to the subscription name to identify Annual plan purchase from partner dashboartd by @jnahian

### 🐞🐞 Bug fixes 🐞🐞

- Fix migration errors and add transaction handling by @maruffahmed in https://github.com/WPDevelopers/storeseo/pull/410

### 🚒 🚑 Hot fixes 🚒 🚑

- Update local seo data on shop update webhook by @rabbykhairul in https://github.com/WPDevelopers/storeseo/pull/433
- Fixed GDPR Webhook urls by @jnahian

### ❄️❄️ Code refactorings

- Bump protobufjs and @google-cloud/translate in /web by @dependabot in https://github.com/WPDevelopers/storeseo/pull/432

### 📜 Documentation

- N/A

# [v3.4.1](https://github.com/WPDevelopers/storeseo/releases/tag/v3.4.1)

📅 08 May, 2024

### 🚀🚀 Features 🚀🚀

- N/A

### ✨✨ Improvements ✨✨

- Polaris migration - upgrade to v13 by @maruffahmed
- Improve Slack daily status report by @jnahian
- Keep track of optimization meta stats by @rabbykhairul
- Unregister webhooks when there are no `plan_id` or `plan_rules` by @jnahian
- Created a command to check subscription status by @jnahian
- Store & update the shop's Redis data to the database every 6 hours by @jnahian
- Created a command for storing Redis data in the database by @jnahian
- Created a command to restore Redis data from the database by @jnahian

### 🐞🐞 Bug fixes 🐞🐞

- Fixed: Homepage broken due to `null` value by @rabbykhairul
- Fixed: Notice Showing for all pages @maruffahmed

### 🚒 🚑 Hot fixes 🚒 🚑

- Fixed: Homepage broken due to `null` value by @rabbykhairul
- Fixed: Unverified issue after image optimizer purchase with free plan by @jnahian

### ❄️❄️ Code refactorings

- Reorganize and refactor the image optimizer webhook processing logic to match the image count and trace all images by @rabbykhairul

### 📜 Documentation

- Create a doc explaining how the pages scoring works for each SEO criteria by @maruffahmed

# [v3.4.0](https://github.com/WPDevelopers/storeseo/releases/tag/v3.4.0)

📅 29 Apr, 2024

### 🚀🚀 Features 🚀🚀

- **AI Content Optimizer** for products by @jnahian, PR: https://github.com/WPDevelopers/storeseo/pull/376
- **Credit bundle** purchase system by @jnahian, PR: https://github.com/WPDevelopers/storeseo/pull/376

### ✨✨ Improvements ✨✨

- Bring back app's **Multi Language** support by @maruffahmed, PR: https://github.com/WPDevelopers/storeseo/pull/404
- Changed **SEO Settings** menu text to **Bulk SEO Update** by @maruffahmed, PR: https://github.com/WPDevelopers/storeseo/pull/402
- Added a card to the dashboard to promote the **AI Content Optimizer** by @jnahian, PR: https://github.com/WPDevelopers/storeseo/pull/376
- Track **Image Optimizer** webhook calls by @rabbykhairul, PR: https://github.com/WPDevelopers/storeseo/pull/401
- Added ⚠️ icon beside **SEO Analysis** items and enabled tooltip for scores above 75 & below 100 by @jnahian, PR: https://github.com/WPDevelopers/storeseo/pull/403

### 🐞🐞 Bug fixes 🐞🐞

- Fixed database seeding issue by @maruffahmed, PR: https://github.com/WPDevelopers/storeseo/pull/399

### 🚒 🚑 Hot fixes 🚒 🚑

- Prevent `svg` image processing in the **Image Optimizer** by @rabbykhairul, PR: https://github.com/WPDevelopers/storeseo/pull/407
- Show ⚠️ instead of ✅ for more than 50 & below 75 in **SEO Analysis** by @rabbykhairul, PR: https://github.com/WPDevelopers/storeseo/pull/400

### ❄️❄️ Code refactorings

- Subscription add-on seeder update for credit bundles by @jnahian

# [v3.3.1]()

📅 23 Apr, 2024

### 🚀🚀 Features 🚀🚀

- **AI Content Optimizer** for products by @jnahian, PR: https://github.com/WPDevelopers/storeseo/pull/376
- **Credit bundle** purchase system by @jnahian, PR: https://github.com/WPDevelopers/storeseo/pull/376

### ✨✨ Improvements ✨✨

- Bring back app's **Multi Language** support by @maruffahmed, PR: https://github.com/WPDevelopers/storeseo/pull/404
- Changed **SEO Settings** menu text to **Bulk SEO Update** by @maruffahmed, PR: https://github.com/WPDevelopers/storeseo/pull/402
- Added a card to the dashboard to promote the **AI Content Optimizer** by @jnahian, PR: https://github.com/WPDevelopers/storeseo/pull/376
- Track **Image Optimizer** webhook calls by @rabbykhairul, PR: https://github.com/WPDevelopers/storeseo/pull/401
- Added ⚠️ icon beside **SEO Analysis** items and enabled tooltip for scores above 75 & below 100 by @jnahian, PR: https://github.com/WPDevelopers/storeseo/pull/403

### 🐞🐞 Bug fixes 🐞🐞

- Fixed database seeding issue by @maruffahmed, PR: https://github.com/WPDevelopers/storeseo/pull/399
-

### 🚒 🚑 Hot fixes 🚒 🚑

- Prevent `svg` image processing in the **Image Optimizer** by @rabbykhairul, PR: https://github.com/WPDevelopers/storeseo/pull/407
- Show ⚠️ instead of ✅ for more than 50 & below 75 in **SEO Analysis** by @rabbykhairul, PR: https://github.com/WPDevelopers/storeseo/pull/400

### ❄️❄️ Code refactorings

- Subscription add-on seeder update for credit bundles by @jnahian

# [v3.3.0](https://github.com/WPDevelopers/storeseo/releases/tag/v3.3.0)

📅 31st Mar, 2024

### 🚀🚀 Features 🚀🚀

- **Collection pages** SEO analysis

### ✨✨ Improvements ✨✨

- Combine navigation menu of Products, Pages & Blog Posts under one menu.
- Keep track of invalid images found while doing image optimization
- Don't send invalid URL images to image processing server
- Use authentication for all requests to image optimizer server

### 🐞🐞 Bug fixes 🐞🐞

- Last optimization settings are not showing in the image compare modal for the related image

### 🚒 🚑 Hot fixes 🚒 🚑

- Product score jump due to no meta description when syncing
- URL handler doesn't work with the words with number at a time. It creates both words and numbers individually using "-"

### ❄️❄️ Code refactorings

- N/A

---

# [v2.7.0]()

📅 28th Aug, 2023

### 🚀🚀 Features

- **Google Analytics** data **report** for the store.
- **Schedule call** button to connect with support team.
- **Sidebar app menu** inside the Shopify admin panel.

### ✨✨ Improvements

- **Upgrade** Shopify API version to **`2023-04`**
- **Upgrade** Shopify app bridge version to latest
- **Update _BetterDocs_ logo** in the promotion section.
- Enable **partial marking** for `focus keyword density` SEO score in **product** analysis.
- **Move** JSON columns from the _shops_ table to _shop settings_ table
- **Remove** unused fields from the _shops_ table

### 🐛🐛 Bug fixes

- _Products_ **meta title, description** gets `deleted` when applying empty SEO template.
- _SEO setting_ page shows old data after successful update.
- _Page SEO_ score **jumps** after clicking _save update_ button.
- **_Webhooks_** are auto unregistered after user logs in via full **OAuth** process.

### 🚒 🚒 Hot fixes

- N/A

### ❄️❄️ Code refactorings

- N/A

---

# [v2.6.3]()

📅 13th July, 2023

### 🚀🚀 Features

- **`Restore data`** from **backup** files on the event when user uninstalls the app & returns back
  - `Backup process`
    - **Store backup** upon receiving _APP_UNINSTALLED_ event from Shopify
    - **Zip** the backup files & store it in _Google Bucket_
  - `Restore process`
    - **Run** the restore command manually once requested by client

### ✨✨ Improvements

- **Handle webhooks** via **`Google PubSub`**
  - Send all webhooks for _selected shops_ to PubSub
  - **Deploy** cloud function to _consume_ messages from PubSub
  - **Dispatch queues** from _PubSub_ based on the event topic and details

### 🐛🐛 Bug fixes

- _Maichimp_ data update in Shop table
- _Shop status_ change to `deleted` on permanent deletion event

### 🚒 🚒 Hot fixes

- `Google Integration` - search console step not working for the client *https://the-great-british-charcuterie-co-6452-2.myshopify.com*
- `Redirect out of stock` logic not deleted from theme's source code for shop *https://durslade-farmshop.myshopify.com*

### ❄️❄️ Code refactorings

- **Blog Articles,** use seperate tables to save
  - `metafields` data
  - `images` data
  - `SEO analysis` data
- **Code refactor** related to `Shopify articles` table breakdown

---

# [v2.6.2](https://github.com/WPDevelopers/storeseo/releases/tag/v2.6.2)

📅 20th June, 2023

### 🚀🚀 Features

- **`No index`** & **`No follow`** meta tag support for Shopify online store's _pages_ and _blog posts_.

### ✨✨ Improvements

- Show same number of blog posts & pages in tables (`20 per page`).
- Blog posts & pages table, sort by `created at: asc` by default.

### 🐛🐛 Bug fixes

- Google indexing toggler `not changing state` after all integration steps are complete.
- Updating img alt text `deletes` existing tags in Shopify for blog posts.
- Not all shops are pushed to `FluentCRM` after installation.

### ❄️❄️ Code refactorings

- Seperate table to save `metafields` of Shopify pages.
- Seperate table to save `SEO analysis` of Shopify pages.
- Code changes related to `Shopify pages` table breakdown.

---

### [V2.6.1](https://github.com/WPDevelopers/storeseo/releases/tag/v2.6.1) [11 June, 2023]

## What's Changed

> `Feature:` Sorting in **Products, Pages & Blogs**. \
> `Improvements:` Close the notification dropdown when click outside/link. \
> `Improvements:` Added "Edit Product" link where the criteria is related to edit product in **SEO Analysis** report. \
> `Improvements:` Showed warning for unsaved changes while tab switching in **Products**. \
> `Improvements:` Sync button disabled after synchronization complete. \
> `Improvements:` Removed related flags from cache when store uninstalls the app. \
> `Marketting:` Integrated FluentCRM and send user data to FluentCRM. \
> `Bug:` Fixed **BetterDocs-Shopify** App link.

### [V2.6.0](https://github.com/WPDevelopers/storeseo/releases/tag/v2.6.0) [17 May, 2023]

## What's Changed

- Created new project with latest Shopify CLI

  - Frontend (Moved from next.js to react.js)
  - Migrated each & every pages & components to react module structure.
  - Re-write each & every api calls with react-query.
  - Migrated all service methods to react hooks.
  - Moved all api calls to react hooks .
  - Moved some major features like analysis, breadcrumbs, featured images, label colors, score colors, publish status
    to react hooks .
  - Implemented lazy loading to improve loading time.
  - Migrated all router operations, calls, declarations to react-router style.
  - Implemented translations using react-18next
  - Re-write helper methods related to shopify app bridge v3 .
  - Replace next.js related dependencies with react.js packages.
  - Migrated Pusher notification system.

- Backend

  - Implemented latest @shopify/shopify-api
  - Updated app auth, auth callback structure.
  - Moved shop registration functionality to middleware.
  - Migrated Shopify Session structure.
  - Updated Shopify related API structure with new Session.
  - Shopify API Clients rewrite.
  - Re-write all Shopify GraphQL queries.
  - Re-write all Shopify HTTP queries.
  - Updated parameters & response for Shopify Queries.
  - Updated error handling for Shopify Queries .
  - Updated Webhooks registration and process structure.
  - Moved Shopify GraphQL query, mutations to relevant folder.
  - Migrated api, webhooks Routing.
  - Modified some queues & changed all the queue dispatch.
  - Removed deprecated api fields and include alternatives.
  - Changed some database structure to adopt deprecation .

- Also created common enum package for sharing configs to frontend & backend.
- Update both Frontend & Backend several times with the Live Application cause we've simultaneously updated & hot-fixed
  the Live Application during the migration process.

### [V2.5.4](https://github.com/WPDevelopers/storeseo/releases/tag/v2.5.4) [16 Mar, 2023]

## What's Changed

> `Feature:` Using **Bulk Operation** to optimize products in batch. \
> `Feature:` Subscription & checkout system instantly after installation. \
> `Improvements:` Sync products button's disabled state UI improved. \
> `Improvements:` Trimmed meta title & meta description for `pages` & `blogs`. \
> `Improvements:` Added missing translations for toast messages. \
> `Improvements:` Re-queueing failed messages for upto 3 times. \
> `Improvements:` Tracking auto optimization run. \
> `Improvements:` Migrate non-queueable webhooks to queue. \
> `Bug:` Fixed meta title, meta description & image alt text saving issue in `blogs`.

### [V2.5.3](https://github.com/WPDevelopers/storeseo/releases/tag/v2.5.3) [14 Feb, 2023]

## What's Changed

> `Features:` Implemented Real time notification in `pages` & `blogs`. \
> `Improvements:` Translation For Notification Feature. \
> `Improvements:` Handle random string in checkout route. \
> `Improvements:` Breadcrumb UI update in checkout page. \
> `Improvements:` Product image sequence changing after updating image alt-text. \
> `Improvements:` Send daily mail to admin about installation & subscription stats. \
> `Improvements:` Product update, create queue optimize. \
> `Improvements:` Set http response codes for API response and modify API calls accordingly. \
> `Improvements:` Use `pusher` instead of `socket.io` for real time notification. \
> `Improvements:` Re-write the queue dispatch mechanism to avoid too many channel/high CPU usage and connection error. \

### [V2.5.2](https://github.com/WPDevelopers/storeseo/releases/tag/v2.5.2) [02 Jan, 2023]

## What's Changed

> `Features:` Implemented Real time notification system. \
> `Features:` Implemented socket to display real time sync progress. \
> `Improvements:` Removed `knex.js` & `bookshelf.js` related codes & packages. \
> `Improvements:` Queue dequeue functionality with `redis`. \
> `Improvements:` Unused tables removed with migration. \
> `Improvements:` Comparing webhook product before processing. \
> `Improvements:` Subscription system re-write to handle deal with new UI. \
> `Improvements:` Coupon apply system update. \
> `Bug:` Disabled sync button when all products are synced. \
> `Bug:` Blog posts & Pages alignments correction. \
> `Bug:` Blog posts focus keyword related issues fixed. \
> `Bug:` Blog posts sync issues fixed. \
> `Bug:` Bulk archive issue fixed for products.

### [V2.5.1](https://github.com/WPDevelopers/storeseo/releases/tag/v2.5.1) [7 Dec, 2022]

## What's Changed

- Migrate from `knex` to `sequelize`.

**Full Changelog**: https://github.com/WPDevelopers/storeseo/compare/v2.5.0...v2.5.1

### [V2.5.0](https://github.com/WPDevelopers/storeseo/releases/tag/v2.5.0) [02 Nov, 2022]

## What's Changed

- `Added:` StoreSEO App Migrator for **SEO King**.
- `Added:` StoreSEO Migrator for existing users **Settings => SEO Migrator**.
- `Improvement:` Removed unused api calls for **Local SEO** & **Google Integration** in `FREE` plan.
- `Improvement:` Product Sync cursor moved to **Redis** for better sync results.
- `Improvement:` Sitemap page UI Improvement.
- `Improvement:` Package upgrade notice removed from onboarding step.
- `Improvement:` Migrate Products related Models from `knex` to `sequelize` and re-write queries.
- `Improvement:` Re-factor some components to improve speed and enhance performance.
- `Improvement:` Moved server stored data to redis for better manageability and improve performance.
- `Fix:` Sitemap search & update issue fix.

### [V2.4.4](https://github.com/WPDevelopers/storeseo/releases/tag/v2.4.4) [02 Nov, 2022]

## What's Changed

- `Added:` StoreSEO App Migrator for **SEO Manager**
- `Improvement:` Bulk product sync functionality improved.
- `Improvement:` Update features list in PREMIUM onboard modal.
- `Improvement:` Migrate Shop Model from `knex` to `sequelize`.
- `Improvement:` Re-factor some components to improve speed and enhance performance.
- `Fix:` Typo fix in PREMIUM onboard modal.

### [V2.4.3](https://github.com/WPDevelopers/storeseo/releases/tag/v2.4.3) [23 Oct, 2022]

## What's Changed

- `Added:` Bulk product sync functionality for unlimited products.
- `Improvement:` Re-factor product meta column to separate table to improve performance.
- `Improvement:` Re-factor product images column to separate table to improve performance.
- `Improvement:` Change the downloaded invoice filename with the transaction id.
- `Fix:` Blog posts tags related issue fixed.
- `Fix:` Yearly subscription validity related issue fix.

### [V2.4.2](https://github.com/WPDevelopers/storeseo/releases/tag/v2.4.2) [11 Oct, 2022]

## What's Changed

- `Added:` StoreSEO App Migrator for **Smart SEO**.
- `Improvement:` Meta title found count update.
- `Improvement:` Invoice download restricted.
- `Improvement:` Comma(,) added as tags separator added.

### [V2.4.1](https://github.com/WPDevelopers/storeseo/releases/tag/v2.4.1) [26 Sept, 2022]

## What's Changed

- `Improvement:` Show added tags in **Blogs** post view page.
- `Improvement:` Moved shop data permanently delete functionality to shop/reduct webhook.
- `Improvement:` Optimize **Report** page query to increase app performance.
- `Improvement:` Allowed special characters(-,&) in product **Focus Keyword**.
- `Improvement:` All statistics report numerical formatting added.
- `Improvement:` Show shop main domain instead of shop's shopify domain in **General Settings**.
- `Improvement:` Show only first focus keyword suggestion by default and show more button to reveal more.
- `Fix:` Title length trimmed to 70 char while auto-fix.
- `Fix:` Image alt-text population fixed after auto-fix.
- `Fix:` Multiple occurrence handled on **SEO Settings**.
- `Fix:` In **Page** focus keyword and image alt-text related issue fixed.
- `Fix:` Store name updating issue fixed in **General Settings**.
- `Structural Improvement:` Move product analysis data to separate table to optimize app performance.

### [V2.4.0](https://github.com/WPDevelopers/storeseo/releases/tag/v2.4.0) [29 Aug, 2022]

## What's Changed

- `Added:` PREMIUM teaser for every premium features in FREE version.
- `Added:` Google Indexing Re-submit functionality added.
- `Improvement:` Canonical URL input moved to a modal for better user experience.
- `Improvement:` Keyword Analysis button changed for better user experience.
- `Improvement:` Removed tags limitation from free plan.
- `Improvement:` Change of footer copyright text style.
- `Improvement:` Removed static placeholder from Image Alt Text input fields.
- `Improvement:` Change the Store URL to shop's actual url.
- `Fix:` Transaction history page crash and download pdf invoice.
- `Structural Improvement:` Re-write header menu codes.
- `Structural Improvement:` Moved lang configs to separate config file.
- `Structural Improvement:` Moved long validation methods to separate file.
- `Structural Improvement:` Removed unnecessary logs from queues.

### [V2.3.2](https://github.com/WPDevelopers/storeseo/releases/tag/v2.3.2) [23 Jun, 2022]

## What's Changed

- `Feature:` **Google Keyword Planner** integration with keyword metrics.
- `Feature:` Check **Canonical URL** for duplicate content detection.
- `Feature:` App Multi-language support for `en, in, fr, es, nl, it, de, hk`.
- `Improvement:` Save and display shop category data from shopify API.
- `Improvement:` Save shopify plan related data for better reporting.
- `Improvement:` Remove uninstalled shop data.
- `Fix:` Remove theme modification button design fix.
- `Check:` App Uninstalled webhook check for reason (Not Found).

### [V2.3.1](https://github.com/WPDevelopers/storeseo/releases/tag/v2.3.1) [23 Jun, 2022]

## What's Changed

- `Feature:` Home page SEO optimization.
- `Feature:` Fixed amount or percentage discount implementation in **Subscription** checkout.
- `Feature:` Remove theme modification & re-apply theme modification for products, pages, articles SEO.
- `Improvement:` Install StoreSEO via link.

### [V2.3.0](https://github.com/WPDevelopers/storeseo/releases/tag/v2.3.0) [01 Jun, 2022]

## What's Changedgc

- `Feature:` Multiple location manage for **Local SEO**.
- `Feature:` Reset google credentials for **Google Integration**.
- `Improvement:` Remove existing preview image while uploading new image.
- `Improvement:` Upgrade **Shopify CLI** project to **Express**.
- `Improvement:` Remove data from `pages`, `blogs` & `sitemaps` after uninstalling app.

### [V2.2.4](https://github.com/WPDevelopers/storeseo/releases/tag/v2.2.4) [19 May, 2022]

## What's Changed

- `Improvement:` Changed Shopify API Version to **January 2021**.
- `Improvement:` Remove deprecated api calls for metafields.
- `Improvement:` Using **Google Cloud Storage** for uploading shop logo images.
- `Improvement:` Using **Google Cloud Storage** for uploading product preview images.
- `Improvement:` Using **Google Cloud Storage** for uploading blog preview images.
- `Improvement:` Using **Google Cloud Storage** for uploading page preview images.
- `Improvement:` Migrate all uploaded images to **Google Cloud Storage**.
- `Fix:` Queue improvement to fix auto dispatch.

### [V2.2.3](https://github.com/WPDevelopers/storeseo/releases/tag/v2.2.3) [17 Apr, 2022]

## What's Changed

- `Feature:` **Blogs** sync from shopify.
- `Feature:` **Blogs** SEO analysis, SEO fix feature implemented.
- `Feature:` **Pages** & **Blogs** analysis statistics show on **Report**.
- `Feature:` Social media preview customization functionality for **Pages**.
- `Feature:` **Optimize with StoreSEO** action button added in shopify admin(products, pages, blogs).
- `Improvement:` UI improvement - Google preview text & image alignment when text length is small.
- `Improvement:` Product sync queue improvement.

### [V2.2.2](https://github.com/WPDevelopers/storeseo/releases/tag/v2.2.2) [26 Mar, 2022]

## What's Changed

- `Feature:` Unique Product details **SEO Parameter** added for **Products**.
- `Feature:` Unique Page content **SEO Parameter** added for **Pages**.
- `Feature:` Social media preview modify functionality for **Products**.
- `Feature:` Added Image Cropper to **Shop Logo** upload.
- `Improvement:` Product fix & show page layout update for better UX.
- `Improvement:` Product analysis improvement.
- `Improvement:` Google Integration structure update.
- `Improvement:` No page found image change.
- `Improvement:` Products menu icon change.
- `Improvement:` Dashboard & Report Statistics code refactor.

### [V2.2.1](https://github.com/WPDevelopers/storeseo/releases/tag/v2.2.1) [08 Mar, 2022]

## What's Changed

- `Feature:` **Pages** sync from shopify.
- `Feature:` **Pages** SEO analysis, SEO fix feature.
- `Bug:` Search with pagination fixed.
- `Bug:` Subscription page responsive issue on firefox browser solved.

### [V2.2.0](https://github.com/WPDevelopers/storeseo/releases/tag/v2.2.0) [08 Mar, 2022]

## What's Changed

- `Feature:` Published **StoreSEO** app on Google.
- `Feature:` `Site Verification`, `Search Console`, `Submit Sitemap`, `Instant Indexing` process using newly published \*
  \*StoreSEO\*\* app.
- `Feature:` Redirect on "Out of Stock".
- `Feature:` Verified StoreSEO domain on Google search console.
- `Feature:` Yearly subscription system.
- `Improvement:` Add Filter section in sitemap.
- `Improvement:` Updated backdated webhooks.
- `Improvement:` Responsiveness Issue fix: Subscription Page Layout Broken on Android Device.
- `Improvement:` Sanitize Text before product rich data submission.
- `Improvement:` Product list filter update.
- `Improvement:` Product sync button re-position and confirmation added for better UX.
- `Bug:` Product search issue with spaces fixed.
- `Bug:` Product sync issue when disabled fixed.

### [V2.1.4](https://github.com/WPDevelopers/storeseo/releases/tag/v2.1.4) [22 Feb, 2022]

- `Feature:` Added option to toggle `noindex` & `nofollow` meta tags for Google.
- `Improvement:` Select input UI changed for better UX.
- `Improvement:` Close mobile menus on clicking outside the menu added.
- `Improvement:` Responsive design Hamburger menu position and UI changed for better UX.
- `Improvement:` Responsive design menu design update for better UX.
- `Improvement:` Product list sl no. shown.
- `Improvement:` Sitemap list sl no. shown.
- `Improvement:` Remove deprecated API calls for Shopify GraphQL.
- `Improvement:` Update APP_UNINSTALLED webhooks API Version.
- `Improvement:` Upgrade `koa-shopify-auth` & `shopofy-api` package version to meet shopify requirement.

### [V2.1.3](https://github.com/WPDevelopers/storeseo/releases/tag/v2.1.3) [07 Feb, 2022]

- `Feature:` Product rich snippet for Google.
- `Feature:` Subscription upgrade onboard modal to guide user about the unlocked features.
- `Feature:` Real time score change when SEO content changed for better user experience.
- `Feature:` Address suggestion for **Local SEO** using Google Places API for better user experience.
- `Feature:` Ensure that the shop address stays same in both end using Webhook.
- `Feature:` Product **Keyword Suggestion** for better control over keywords.
- `Improvement:` Single product analysis when updating a product.
- `Improvement:` Automatic re-generate LAT, LONG data when address is changed.
- `Improvement:` Removed settings top bar to a minimalistic navigation breadcrumb.
- `Improvement:` Active/Inactive filter in sitemaps.
- `Bug:` Removed the `charge_id` from url after subscription successful to prevent duplicate subscription success
  notification.
- `Structural Improvement:` Removed user dependency.

### [V2.1.2](https://github.com/WPDevelopers/storeseo/releases/tag/v2.1.2) [24 Jan, 2022]

- `Feature:` Focus Keyword parsing with AI & NLTK.
- `Improvement:` Logo upload max size 850 KB.
- `Improvement:` Add placeholder when uploading logo.
- `Improvement:` Improved URL validation for social media in Local SEO.
- `Improvement:` Changed Subscription Plan UI.
- `Improvement:` Improved Subscription Expiry Conditions.
- `Bug:` Fixed multiple toast notification show issue.
- `Bug:` Fixed issue of product syncing after plan downgrade from one pro plan to another pro plan.

### [V2.1.1](https://github.com/WPDevelopers/storeseo/releases/tag/v2.1.1) [10 Jan, 2022]

- `Feature:` Google Instant Indexing.
- `Feature:` Generate auto lat,long based on shop address in Local SEO.
- `Feature:` Generate auto image placeholder from Shop Name for Shop Logo in Local SEO.
- `Feature:` General Settings Added.
- `Improvement:` Injecting JSON-LD schema directly to page source.
- `Improvement:` Changed deprecated API calls.
- `Improvement:` Product sync queue improvement.
- `Improvement:` Warning message shown for products which has more than allowed tags.
- `Improvement:` Settings text added on Settings Icon.
- `Improvement:` Minor UI improvements.
- `Improvement:` Custom error page for 404 and 500 errors.
- `Structural Improvement:` Product meta information structure update.
- `Structural Improvement:` Redis remove & queue restructure.
- `Bug:` Fixed special character in product meta.

### [V2.1.0](https://github.com/WPDevelopers/storeseo/releases/tag/v2.1.0) [27 Dec 2021]

- `Feature:` Subscription downgrade.
- `Feature:` Remove shop's product according to the subscription plan after **subscription downgrade**.
- `Feature:` Subscription auto downgrade when plan canceled.
- `Feature:` JSON-LD Schema add and modify.
- `Feature:` Show transaction history.
- `Feature:` Show and download invoice.
- `Feature:` Separate checkout page.
- `Feature:` Coupon apply system.
- `Improvement:` Added pagination in dashboard (In product list section).
- `Improvement:` Automatic redirect to dashboard from onboard success page after 7 sec.
- `Improvement:` Changed menu name "Home" to "Dashboard".
- `Structural Improvement:` Added transaction type in db.
- `Structural Improvement:` Completely delete store data and products after **APP UNINSTALLED**.
- `Structural Improvement:` Crate log for **APP UNINSTALLED** count.

### [V2.0.0 - PRO version Released](https://github.com/WPDevelopers/storeseo/releases/tag/v2.0.0) [2 Dec, 2021]

- Google console integration.
- Permission manage.
- Product sync with queue.
- Remove bootstrap css.
- Mailchimp integration.
- PRO version merge with FREE.
- .env example updated with server codes.
- Mailchimp update.
- Webhook trigger update.
- Queue improvement.
- Commands created.

### [V1.0.0 - FREE version Released](https://github.com/WPDevelopers/storeseo/releases/tag/v1.0.0) [2 Nov, 2021]
