# Blog Auto-Write API Reference

## Document Information

| Field | Value |
|-------|-------|
| **Feature Name** | Blog Auto-Write API with Blog Selection |
| **API Version** | 2.3.0 |
| **Created Date** | 2024-12-19 |
| **Last Updated** | 2025-07-15 |
| **Status** | Production Ready with AI Blog Generator Page |

## API Overview

The Blog Auto-Write API provides endpoints for AI-powered blog content generation with SEO optimization and blog selection. All endpoints require authentication and follow RESTful conventions.

### Base URL
```
Production: https://app.storeseo.com/api
Staging: https://staging.storeseo.com/api
Development: http://localhost:3000/api
```

### Authentication
All API endpoints require JWT authentication with shop and user context.

### Security & Validation

#### Input Validation
All endpoints implement comprehensive input validation with:
- **DRY Validation**: Shared schema package (`storeseo-schema/blog/autoWrite.js`) ensures frontend/backend sync
- **XSS Protection**: HTML content sanitization using xss-filters
- **International Support**: Unicode regex for keywords supporting all languages
- **Field Length Limits**: Enforced character limits for all text fields
- **Enum Validation**: Strict validation against predefined blog types, tones, and word counts
- **Data Sanitization**: Automatic trimming and null conversion for optional fields
- **Featured Image Logic**: Proper validation of `generateFeaturedImage` and `featuredImageDescription` fields
- **Blog Ownership Validation**: Ensures `targetBlog` belongs to authenticated shop and is synced
- **Feature Flag Integration**: Runtime feature control with input data override when features disabled

#### Concurrency Handling
The API supports multiple concurrent blog generation jobs per shop through:

- **Time-Based Unique Article IDs**: Uses YYYYMMDDHHMMSS format for dummy articles (negative IDs)
- **Unique Handle Generation**: Timestamp-based handles prevent conflicts
- **Independent Job Processing**: Each job processes independently without conflicts
- **Credit Reservation**: Per-job credit locking prevents double-charging

#### Future Enhancements
Rate limiting and concurrent generation limits are planned for future versions and will require business team decisions for proper implementation:

- **Rate Limiting**: API request limits per time window (requires business policy)
- **Concurrent Limits**: Maximum simultaneous generations per shop (requires capacity planning)
- **Advanced Monitoring**: Real-time abuse detection and prevention

#### Credit Pre-validation
- **Automatic credit checking** before job creation
- **Detailed error responses** with credit information
- **Prevention of insufficient credit jobs**

```http
Authorization: Bearer <jwt_token>
```

### Content Type
All requests and responses use JSON format.

```http
Content-Type: application/json
```

## Endpoints

### 1. Create Blog Generation Job

Creates a new blog generation job and queues it for processing.

**Endpoint:** `POST /blog-auto-write`

**Request Body:**
```typescript
interface BlogGenerationRequest {
  topic: string;                    // Required: Blog topic/subject
  targetBlog: string;              // Required: Target blog ID for article creation
  keyword?: string;                 // Optional: Primary keyword
  tone?: string;                   // Optional: Content tone
  wordCount: string;               // Required: Word count range
  blogType: string;                // Required: Blog format type
  customInstructions?: string;     // Optional: Additional instructions
  generateFeaturedImage?: boolean; // Optional: Whether to generate featured image (default: false)
  featuredImageDescription?: string; // Optional: Image description for DALL-E
  autoPublish?: boolean;           // Optional: Auto-publish flag (default: false, saves as draft)
}

// Note: targetBlog field added in v2.1.0 - system uses selected blog instead of default blog

enum ContentTone {
  FORMAL = 'Formal',
  INFORMAL = 'Informal',
  CONVERSATIONAL = 'Conversational',
  PERSUASIVE = 'Persuasive',
  FRIENDLY = 'Friendly',
  ENCOURAGING = 'Encouraging'
}

enum BlogType {
  PRODUCT_BASED = 'Product-based',
  GUIDE = 'Guide',
  LISTICLE = 'Listicle',
  INFORMATIONAL = 'Informational',
  NEWS = 'News',
  SEASONAL = 'Seasonal'
}

enum WordCountRange {
  UP_TO_500 = 'Up to 500',
  RANGE_500_800 = '500-800',
  RANGE_800_1200 = '800-1200',
  RANGE_1200_1500 = '1200-1500',
  RANGE_1500_2000 = '1500-2000',
  OVER_2000 = '2000+'
}
```

**Example Request:**
```json
{
  "topic": "Best SEO Practices for E-commerce Stores",
  "targetBlog": "593",
  "keyword": "SEO optimization",
  "tone": "Professional",
  "wordCount": "800-1200",
  "blogType": "Guide",
  "customInstructions": "Include actionable tips and real examples",
  "generateFeaturedImage": true,
  "featuredImageDescription": "Modern e-commerce dashboard showing SEO metrics",
  "autoPublish": false
}
```

**Response:**
```typescript
interface BlogGenerationResponse {
  success: boolean;
  job: {
    id: string;
    status: string;
    progress: number;
    article_id: number;
  };
  article: {
    id: number;
    title: string;
    handle: string;
    generation_status: string;
  };
  creditEstimation: {
    totalCredits: number;
    feature: string;
    featureName: string;
    addonGroup: string;
    estimatedCost: number;
    breakdown: object;
  };
  availableCredits: number;
}
```

**Example Response:**
```json
{
  "success": true,
  "job": {
    "id": "job_abc123def456",
    "status": "pending",
    "progress": 0,
    "article_id": 987654321
  },
  "article": {
    "id": 987654321,
    "title": "[Generating] Best SEO Practices for E-commerce Stores",
    "handle": "best-seo-practices-for-e-commerce-stores",
    "generation_status": "pending"
  },
  "creditEstimation": {
    "totalCredits": 112,
    "feature": "blog_auto_write",
    "featureName": "Blog Auto-Write",
    "addonGroup": "AI_OPTIMIZER",
    "estimatedCost": 0.224,
    "breakdown": {
      "content": { "credits": 12 },
      "images": { "credits": 100 }
    }
  },
  "availableCredits": 4888
}
```

**Status Codes:**
- `201 Created` - Job created successfully
- `400 Bad Request` - Invalid input parameters
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Insufficient credits or permissions
- `429 Too Many Requests` - Rate limit exceeded

---

### 2. Get Job Details

Retrieves comprehensive details of a blog generation job including status, progress, steps, and credit usage.

**Endpoint:** `GET /blog-auto-write/:jobId`

**Path Parameters:**
- `jobId` (string, required) - The job identifier

**Response:**
```typescript
interface JobDetailsResponse {
  success: boolean;
  data: {
    jobId: string;
    status: JobStatus;
    progress: number;              // 0-100
    steps: JobStep[];              // Detailed step tracking
    articleId: number;             // Database article ID
    article?: Article;             // Article details if available
    inputData: BlogGenerationInput; // Original request data
    estimatedCreditUsage?: CreditUsage; // Estimated costs
    creditUsage?: CreditUsage;     // Actual usage (when available)
    errorMessage?: string;
    createdAt: string;            // ISO 8601
    updatedAt: string;            // ISO 8601
    processingStartedAt?: string; // ISO 8601
    processingCompletedAt?: string; // ISO 8601
  };
}

interface JobStep {
  step: string;                   // Step name (e.g., "CONTENT_GENERATION")
  completed: boolean;
  error?: string;
  startedAt?: string;            // ISO 8601
  completedAt?: string;          // ISO 8601
}

interface CreditUsage {
  totalCredits: number;
  content?: {
    credits: number;
    details: object;
  };
  images?: {
    credits: number;
    details: object;
  };
}

enum JobStatus {
  PENDING = 'pending',
  GENERATING_CONTENT = 'generating_content',     // Step 1: Content generation
  CREATING_DRAFT = 'creating_draft',             // Step 2: Shopify draft creation
  LINKING_ARTICLE = 'linking_article',           // Step 3: Article linking
  ANALYZING_SEO = 'analyzing_seo',               // Step 4: First SEO analysis
  GENERATING_IMAGE = 'generating_image',         // Step 5: Featured image generation
  UPLOADING_IMAGE = 'uploading_image',           // Step 6: Shopify image upload
  UPDATING_ARTICLE = 'updating_article',        // Step 7: Article update with image
  FINALIZING_SEO = 'finalizing_seo',            // Step 8: Final SEO analysis
  PUBLISHING = 'publishing',                     // Step 9: Shopify article publish
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// Article Generation Status (automatically synchronized with JobStatus)
enum BlogGenerationStatus {
  MANUAL = 'manual',                    // Regular manually created articles
  PENDING = 'pending',                  // AI generation job created, not started
  GENERATING = 'generating',            // AI actively creating content
  CONTENT_READY = 'content_ready',      // Content generated, ready for publication
  DRAFT_CREATED = 'draft_created',      // Draft article created in Shopify
  PUBLISHING = 'publishing',            // Publishing to Shopify in progress
  PUBLISHED = 'published',              // Successfully published to Shopify (autoPublish: true)
  DRAFT_PUBLISHED = 'draft_published',  // Completed as draft (autoPublish: false)
  FAILED = 'failed'                     // Generation or publication failed
}
```

**Automatic Status Synchronization:**

The system automatically synchronizes job status changes with the corresponding article's `generation_status` field to maintain data consistency. This synchronization happens transparently during all job status updates.

**Status Mapping:**
```typescript
// Job Status → Article Generation Status (Automatic Mapping)
PENDING → PENDING                    // Job queued, waiting to start
GENERATING_CONTENT → GENERATING      // AI creating blog content
CREATING_DRAFT → GENERATING          // Still in content creation phase
LINKING_ARTICLE → DRAFT_CREATED      // Article linked to Shopify draft
ANALYZING_SEO → CONTENT_READY        // Content ready, being optimized
GENERATING_IMAGE → CONTENT_READY     // Content ready, adding images
UPLOADING_IMAGE → CONTENT_READY      // Content ready, finalizing
UPDATING_ARTICLE → CONTENT_READY     // Content ready, updating
FINALIZING_SEO → CONTENT_READY       // Content ready, final touches
PUBLISHING → PUBLISHING              // Publishing to Shopify
COMPLETED → DRAFT_PUBLISHED/PUBLISHED // Based on autoPublish setting
FAILED → FAILED                      // Job failed
CANCELLED → FAILED                   // Cancelled jobs marked as failed
```

**Synchronization Features:**
- **Automatic**: Triggered on every job status update
- **Smart Progression**: Prevents status regression (e.g., PUBLISHED → GENERATING)
- **Special Cases**: Handles completion (autoPublish), failure, and regeneration
- **Error Resilient**: Sync failures don't affect job processing
- **Real-time**: Status changes reflect immediately in both tables

```

interface GeneratedBlogContent {
  title: string;
  content: string;               // HTML content
  metaTitle: string;
  metaDescription: string;
  tags: string[];
  seoScore: number;             // 0-100
  readabilityScore: number;     // 0-100
  wordCount: number;
  keywordDensity: Record<string, number>;
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "jobId": "job_abc123def456",
    "status": "generating_content",
    "progress": 30,
    "steps": [
      {
        "step": "CONTENT_GENERATION",
        "completed": false,
        "startedAt": "2024-12-19T10:00:05Z"
      },
      {
        "step": "SHOPIFY_DRAFT_CREATION",
        "completed": false
      }
    ],
    "articleId": 12345,
    "inputData": {
      "topic": "Best SEO Practices for E-commerce",
      "wordCount": "800-1200",
      "blogType": "Guide"
    },
    "estimatedCreditUsage": {
      "totalCredits": 112,
      "content": {
        "credits": 12,
        "details": {}
      },
      "images": {
        "credits": 100,
        "details": {}
      }
    },
    "createdAt": "2024-12-19T10:00:00Z",
    "updatedAt": "2024-12-19T10:00:05Z",
    "processingStartedAt": "2024-12-19T10:00:05Z"
  }
}
```

**Status Codes:**
- `200 OK` - Job status retrieved successfully
- `404 Not Found` - Job not found
- `401 Unauthorized` - Authentication required

---

### 3. Publish Generated Blog

Publishes the generated blog content to Shopify.

**Endpoint:** `PUT /blog-auto-write/:jobId/publish`

**Path Parameters:**
- `jobId` (string, required) - The job identifier

**Request Body:**
```typescript
interface PublishRequest {
  publishImmediately: boolean;    // true = publish, false = save as draft
  customTitle?: string;          // Optional: override generated title
  customMetaDescription?: string; // Optional: override generated meta description
  additionalTags?: string[];     // Optional: additional tags to include
}

// Note: Articles are published to the same default blog where they were created
```

**Example Request:**
```json
{
  "publishImmediately": true,
  "customTitle": "Ultimate Guide to E-commerce SEO Success",
  "additionalTags": ["guide", "marketing"]
}
```

**Response:**
```typescript
interface PublishResponse {
  success: boolean;
  message: string;
  shopifyArticleId?: string;
  shopifyArticleUrl?: string;
  publishedAt?: string;          // ISO 8601
}
```

**Example Response:**
```json
{
  "success": true,
  "message": "Blog published successfully",
  "shopifyArticleId": "gid://shopify/Article/123456789",
  "shopifyArticleUrl": "https://mystore.myshopify.com/blogs/news/ultimate-guide-to-ecommerce-seo",
  "publishedAt": "2024-12-19T10:05:30Z"
}
```

**Status Codes:**
- `200 OK` - Blog published successfully
- `400 Bad Request` - Job not ready for publishing or invalid parameters
- `404 Not Found` - Job not found
- `500 Internal Server Error` - Shopify publishing failed

---

### 4. List User Jobs

Retrieves a paginated list of blog generation jobs for the authenticated user.

**Endpoint:** `GET /blog-auto-write/jobs`

**Query Parameters:**
- `page` (number, optional) - Page number (default: 1)
- `limit` (number, optional) - Items per page (default: 20, max: 100)
- `status` (JobStatus, optional) - Filter by job status
- `sortBy` (string, optional) - Sort field (default: 'createdAt')
- `sortOrder` (string, optional) - Sort order: 'asc' or 'desc' (default: 'desc')

**Example Request:**
```
GET /blog-auto-write/jobs?page=1&limit=10&status=completed&sortBy=createdAt&sortOrder=desc
```

**Response:**
```typescript
interface JobListResponse {
  success: boolean;
  jobs: JobSummary[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

interface JobSummary {
  id: string;
  status: JobStatus;
  progress: number;
  inputData: {
    topic: string;
    wordCount: number;
    blogType: BlogType;
  };
  createdAt: string;
  completedAt?: string;
  shopifyArticleId?: string;
  creditUsage: number;
}
```

**Status Codes:**
- `200 OK` - Jobs retrieved successfully
- `400 Bad Request` - Invalid query parameters
- `401 Unauthorized` - Authentication required

---

### 5. Cancel Job

Cancels a pending or in-progress blog generation job.

**Endpoint:** `DELETE /blog-auto-write/:jobId`

**Path Parameters:**
- `jobId` (string, required) - The job identifier

**Response:**
```typescript
interface CancelJobResponse {
  success: boolean;
  message: string;
  refundedCredits?: number;
}
```

**Example Response:**
```json
{
  "success": true,
  "message": "Job cancelled successfully",
  "refundedCredits": 10
}
```

**Status Codes:**
- `200 OK` - Job cancelled successfully
- `400 Bad Request` - Job cannot be cancelled (already completed)
- `404 Not Found` - Job not found
- `401 Unauthorized` - Authentication required

---

### 6. Regenerate Job

Cancels the current job and creates a new one with the same input data, maintaining audit trail while allowing content refinement. **Regeneration is available for all job statuses including completed jobs**, providing users with maximum flexibility to refine their AI-generated content.

**Endpoint:** `POST /blog-auto-write/:jobId/regenerate`

**Path Parameters:**
- `jobId` (string, required) - The job identifier to regenerate

**Request Body:**
```typescript
interface RegenerateJobRequest {
  // Optional: Override specific input parameters for regeneration
  topic?: string;                    // Override original topic
  keyword?: string;                  // Override original keyword
  tone?: string;                     // Override original tone
  wordCount?: string;                // Override original word count
  blogType?: string;                 // Override original blog type
  customInstructions?: string;       // Override original instructions
  generateFeaturedImage?: boolean;   // Override image generation setting
  featuredImageDescription?: string; // Override original image description
  autoPublish?: boolean;             // Override auto-publish setting
  // Note: Unspecified parameters use original values from cancelled job
}
```

**Response:**
```typescript
interface RegenerateJobResponse {
  success: boolean;
  message: string;
  data: {
    // Core job fields (same structure as create job)
    jobId: string;                 // New job identifier
    articleId: number;             // Target article ID (reused)
    status: string;                // New job status (PENDING)
    progress: number;              // Job progress (0)
    estimatedCredits: number;      // Credit estimate for new job
    availableCredits: number;      // Remaining credits after reservation
    feature: string;               // "blog_auto_write"
    featureName: string;           // "Blog Auto-Write"
    addonGroup: string;            // "AI_OPTIMIZER"

    // Regeneration-specific metadata
    regeneration: {
      cancelledJobId: string;      // Original job identifier (now cancelled)
      refundedCredits?: number;    // Credits refunded from cancelled job (if any)
    };
  };
}
```

**Example Request:**
```json
{
  "topic": "Advanced SEO Techniques for 2024",
  "tone": "Expert",
  "wordCount": "1500-2000",
  "customInstructions": "Focus more on technical SEO and include recent algorithm updates",
  "generateFeaturedImage": true,
  "featuredImageDescription": "Modern SEO dashboard with analytics charts",
  "autoPublish": false
}
```

**Example Response:**
```json
{
  "success": true,
  "message": "Job regenerated successfully",
  "data": {
    "jobId": "550e8400-e29b-41d4-a716-446655440001",
    "articleId": 12345,
    "status": "PENDING",
    "progress": 0,
    "estimatedCredits": 125,
    "availableCredits": 4775,
    "feature": "blog_auto_write",
    "featureName": "Blog Auto-Write",
    "addonGroup": "AI_OPTIMIZER",
    "regeneration": {
      "cancelledJobId": "550e8400-e29b-41d4-a716-446655440000",
      "refundedCredits": 45
    }
  }
}
```

**Regeneration Availability:**
- ✅ **Completed Jobs**: Users can regenerate successfully completed blogs to try different parameters or improve content
- ✅ **Failed Jobs**: Users can regenerate failed jobs to retry with same or modified settings
- ✅ **In-Progress Jobs**: Users can cancel and regenerate jobs that are currently processing
- ✅ **Pending Jobs**: Users can regenerate jobs that haven't started processing yet

**Regeneration Logic:**
1. **Validate Current Job**: Ensure job exists and belongs to authenticated user
2. **Get Target Article**: Retrieve the article associated with the current job
3. **Cancel Current Job**: Set status to CANCELLED and perform credit settlement
4. **Calculate Credit Refund**: Determine credits to refund based on completed steps
5. **Merge Input Data**: Combine original job input with override parameters
6. **Create New Job**: Generate new job with merged input data targeting same article
7. **Reserve Credits**: Reserve estimated credits for the new job
8. **Queue Processing**: Dispatch new job to background queue for processing
9. **Return Response**: Provide new job details and regeneration metadata

**Credit Settlement Behavior:**
- **Cancelled Job**: Automatic credit settlement based on completed steps
- **Refund Calculation**: Estimated credits minus actual usage from completed steps
- **New Job Reservation**: Fresh credit reservation for the new job estimation
- **Net Credit Impact**: Users pay the difference between new estimation and refunded amount
- **Transparent Billing**: Both refund and new reservation amounts are tracked separately

**Status Codes:**
- `201 Created` - Job regenerated successfully
- `400 Bad Request` - Invalid input or job cannot be regenerated
- `402 Payment Required` - Insufficient credits for regeneration
- `404 Not Found` - Original job not found
- `401 Unauthorized` - Authentication required

---

### 7. Estimate Credit Usage

Estimates the credit cost for a blog generation request without creating a job.

**Endpoint:** `POST /blog-auto-write/credit-estimate`

**Request Body:**
```typescript
interface CreditEstimateRequest {
  topic: string;                    // Required: Blog topic/subject
  keyword?: string;                 // Optional: Primary keyword
  wordCount: string;               // Required: Word count range
  blogType: string;                // Required: Blog format type
  tone?: string;                   // Optional: Content tone
  customInstructions?: string;     // Optional: Additional instructions
  featuredImageDescription?: string; // Optional: Image description for DALL-E
}
```

**Example Request:**
```json
{
  "topic": "SEO Best Practices for E-commerce",
  "keyword": "SEO optimization",
  "wordCount": "800-1200",
  "blogType": "Guide",
  "tone": "Professional",
  "customInstructions": "Include practical examples",
  "featuredImageDescription": "Modern e-commerce dashboard"
}
```

**Response:**
```typescript
interface CreditEstimateResponse {
  success: boolean;
  data: {
    estimatedCredits: number;
    availableCredits: number;
    currentUsage: number;
    usageLimit: number;
    canProceed: boolean;
    feature: string;
    featureName: string;
    addonGroup: string;
    estimatedCost: number;
    breakdown: {
      content: {
        credits: number;
        details: object;
      };
      images?: {
        credits: number;
        details: object;
      };
    };
  };
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "estimatedCredits": 112,
    "availableCredits": 4888,
    "currentUsage": 112,
    "usageLimit": 5000,
    "canProceed": true,
    "feature": "blog_auto_write",
    "featureName": "Blog Auto-Write",
    "addonGroup": "AI_OPTIMIZER",
    "estimatedCost": 0.224,
    "breakdown": {
      "content": {
        "credits": 12,
        "details": {
          "tokenEstimation": {
            "promptTokens": 850,
            "completionTokens": 1350,
            "totalTokens": 2200
          },
          "userCredits": 12
        }
      },
      "images": {
        "credits": 100,
        "details": {
          "dalleActualCost": 0.08,
          "userCredits": 100
        }
      }
    }
  }
}
```

**Status Codes:**
- `200 OK` - Estimation calculated successfully
- `400 Bad Request` - Invalid parameters
- `401 Unauthorized` - Authentication required

---

### 8. Credit Settlement System

The Blog Auto-Write feature implements a comprehensive credit settlement system that ensures accurate billing and transparent credit management.

#### Credit Flow Process

1. **Credit Reservation**: When a job is created, estimated credits are immediately reserved
2. **Step-by-Step Tracking**: Actual credit usage is tracked for each completed step
3. **Final Settlement**: At job completion/cancellation/failure, actual vs estimated usage is calculated
4. **Automatic Adjustment**: Credits are refunded or additional charges applied based on the difference

#### Settlement Scenarios

**Job Completion:**
- Calculates actual credit usage from all completed steps
- Compares against estimated credits reserved at job creation
- Applies refund if actual usage is less than estimated
- Applies additional charge if actual usage exceeds estimated

**Job Cancellation:**
- Calculates credit usage from steps completed before cancellation
- Refunds unused credits from the original estimation
- Preserves credits used in current step if cancellation occurs mid-execution

**Job Failure:**
- Only charges for successfully completed operations
- Refunds credits for failed or incomplete steps
- Maintains transparent billing for partial work completed

#### Credit Settlement API Response

When jobs complete, cancel, or fail, the system automatically performs settlement. The settlement details are stored in the job's `credit_usage` field:

```typescript
interface CreditSettlement {
  totalCredits: number;           // Actual credits used
  breakdown: {
    content?: {
      credits: number;
      usage: object;
    };
    images?: {
      credits: number;
      usage: object;
    };
  };
  feature: "blog_auto_write";
  featureName: "Blog Auto-Write";
  addonGroup: "AI_OPTIMIZER";
  settlementType: "completion" | "cancellation" | "failure";
  settledAt: string;              // ISO 8601 timestamp
  auditTrail: {
    estimatedCredits: number;
    actualCredits: number;
    settlementAmount: number;     // Positive = refund, Negative = additional charge
    completedSteps: number;
    totalSteps: number;
    stepBreakdown: Array<{
      step: string;
      completedAt: string;
      creditsUsed: number;
      usage: object;
    }>;
  };
}
```

---

### 9. Get Credit Usage Status

Retrieves current credit usage information for the authenticated user.

**Endpoint:** `GET /blog-auto-write/credit-usage`

**Response:**
```typescript
interface CreditUsageResponse {
  success: boolean;
  data: {
    currentUsage: number;
    usageLimit: number;
    availableCredits: number;
    totalUsageThisMonth: number;
    addonGroup: 'AI_OPTIMIZER';
    lastUpdated: string;
  };
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "currentUsage": 112,
    "usageLimit": 5000,
    "availableCredits": 4888,
    "totalUsageThisMonth": 1250,
    "addonGroup": "AI_OPTIMIZER",
    "lastUpdated": "2025-01-23T10:00:00Z"
  }
}
```

**Status Codes:**
- `200 OK` - Usage information retrieved successfully
- `401 Unauthorized` - Authentication required

---

## Blog Selection Endpoints (v2.1.0)

### 10. Get Available Blogs

Retrieves a paginated list of blogs available for article creation with filtering and search capabilities.

**Endpoint:** `GET /blogs`

**Query Parameters:**
- `synced_only` (boolean, optional) - Only return synced blogs (default: true)
- `search` (string, optional) - Search term for blog titles
- `sort_by` (string, optional) - Sort field: 'title' or 'created_at' (default: 'title')
- `sort_order` (string, optional) - Sort order: 'asc' or 'desc' (default: 'asc')
- `page` (number, optional) - Page number (default: 1)
- `limit` (number, optional) - Items per page (default: 50, max: 100)

**Example Request:**
```
GET /blogs?synced_only=true&search=category&sort_by=title&sort_order=asc&page=1&limit=20
```

**Response:**
```typescript
interface BlogListResponse {
  success: boolean;
  data: {
    blogs: Blog[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
}

interface Blog {
  id: number;                    // Database blog ID
  blog_id: string;              // Shopify blog ID
  title: string;                // Blog title
  handle: string;               // Blog URL handle
  is_synced: boolean;           // Sync status
  template_suffix?: string;     // Template suffix
  tags: string[];               // Blog tags
  article_count: number;        // Number of articles in blog
  created_at: string;           // ISO timestamp
  updated_at: string;           // ISO timestamp
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "blogs": [
      {
        "id": 593,
        "blog_id": "123456789",
        "title": "Category Khairul",
        "handle": "category-khairul",
        "is_synced": true,
        "template_suffix": null,
        "tags": ["category", "products"],
        "article_count": 14,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2025-07-10T08:45:00Z"
      },
      {
        "id": 594,
        "blog_id": "987654321",
        "title": "News & Updates",
        "handle": "news-updates",
        "is_synced": true,
        "template_suffix": "news",
        "tags": ["news", "updates"],
        "article_count": 8,
        "created_at": "2024-02-20T14:15:00Z",
        "updated_at": "2025-07-09T16:20:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 2,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

**Status Codes:**
- `200 OK` - Blogs retrieved successfully
- `401 Unauthorized` - Authentication required
- `500 Internal Server Error` - Server error

### 11. Get Blog Statistics

Retrieves statistics about blogs for the authenticated shop.

**Endpoint:** `GET /blogs/stats`

**Response:**
```typescript
interface BlogStatsResponse {
  success: boolean;
  data: {
    total: number;      // Total number of blogs
    synced: number;     // Number of synced blogs
    unsynced: number;   // Number of unsynced blogs
  };
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "total": 5,
    "synced": 3,
    "unsynced": 2
  }
}
```

**Status Codes:**
- `200 OK` - Statistics retrieved successfully
- `401 Unauthorized` - Authentication required
- `500 Internal Server Error` - Server error

---

## Feature Flag Endpoints (v2.2.0)

### 12. Get Feature Flags

Retrieves current feature flag configuration for the authenticated user's context.

**Endpoint:** `GET /feature-flags`

**Request Parameters:** None

**Response:**
```typescript
interface FeatureFlagsResponse {
  success: true;
  data: {
    flags: {
      BLOG_AUTO_WRITE: {
        IMAGE_GENERATION: boolean;
        BULK_GENERATION: boolean;
        SCHEDULING: boolean;
        TEMPLATES: boolean;
      };
      AI: {
        SUGGESTIONS: boolean;
        AUTO_OPTIMIZATION: boolean;
        CONTENT_ANALYSIS: boolean;
      };
      UI: {
        NEW_DASHBOARD: boolean;
        DARK_MODE: boolean;
        ADVANCED_FILTERS: boolean;
      };
      INTEGRATIONS: {
        SOCIAL_MEDIA: boolean;
        ANALYTICS: boolean;
        WEBHOOKS: boolean;
      };
    };
  };
  timestamp: string;
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "flags": {
      "BLOG_AUTO_WRITE": {
        "IMAGE_GENERATION": true,
        "BULK_GENERATION": false,
        "SCHEDULING": false,
        "TEMPLATES": false
      },
      "AI": {
        "SUGGESTIONS": false,
        "AUTO_OPTIMIZATION": true,
        "CONTENT_ANALYSIS": true
      },
      "UI": {
        "NEW_DASHBOARD": false,
        "DARK_MODE": false,
        "ADVANCED_FILTERS": true
      },
      "INTEGRATIONS": {
        "SOCIAL_MEDIA": false,
        "ANALYTICS": true,
        "WEBHOOKS": false
      }
    }
  },
  "timestamp": "2025-07-10T10:30:00.000Z"
}
```

**Feature Flag Behavior:**

1. **Image Generation Control:**
   - When `BLOG_AUTO_WRITE.IMAGE_GENERATION` is `false`:
     - Frontend hides image generation UI fields
     - Backend overrides `generateFeaturedImage: false` in validation
     - Jobs created with 6 steps (skips image-related steps)
     - Progress calculated with dynamic weights for enabled steps

2. **Conditional Processing:**
   - Feature flags are checked at multiple layers (frontend, middleware, service, queue)
   - System gracefully handles feature disabling without breaking existing functionality
   - Jobs adapt to enabled features at creation time

3. **Caching:**
   - Feature flags are cached for 5 minutes on frontend
   - Backend reads from environment variables (no caching needed)
   - Changes require environment variable updates and service restart

**Status Codes:**
- `200 OK` - Feature flags retrieved successfully
- `401 Unauthorized` - Authentication required
- `500 Internal Server Error` - Server error

**Usage Example:**
```javascript
// Frontend usage
const featureFlags = useFeatureFlags();
const isImageEnabled = featureFlags.blogs.autoWrite.isImageGenerationEnabled();

// Backend usage
const isEnabled = FeatureFlagService.blogs.autoWrite.isImageGenerationEnabled();
```

---

## Error Responses

All error responses follow a consistent format:

```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  requestId: string;
}
```

### Common Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `INVALID_INPUT` | Request validation failed | 400 |
| `INVALID_BLOG_OWNERSHIP` | Blog does not belong to authenticated shop | 400 |
| `BLOG_NOT_SYNCED` | Selected blog is not synced | 400 |
| `BLOG_VALIDATION_ERROR` | Blog ownership validation failed | 500 |
| `INSUFFICIENT_TOKENS` | Not enough AI Optimizer tokens | 403 |
| `TOKEN_RESERVATION_FAILED` | Unable to reserve tokens | 500 |
| `TOKEN_ESTIMATION_ERROR` | Token estimation calculation failed | 500 |
| `JOB_NOT_FOUND` | Job ID does not exist | 404 |
| `RATE_LIMIT_EXCEEDED` | Too many requests | 429 |
| `OPENAI_API_ERROR` | External AI service error | 502 |
| `SHOPIFY_API_ERROR` | Shopify publishing error | 502 |
| `INTERNAL_ERROR` | Unexpected server error | 500 |

### Example Error Response:
```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_TOKENS",
    "message": "Not enough AI Optimizer tokens to generate blog. Required: 1800, Available: 1200",
    "details": {
      "required": 1800,
      "available": 1200,
      "currentUsage": 8800,
      "usageLimit": 10000,
      "addonGroup": "AI_OPTIMIZER",
      "upgradeUrl": "/settings/billing"
    }
  },
  "timestamp": "2024-12-19T10:00:00Z",
  "requestId": "req_xyz789abc123"
}
```

## Rate Limiting (Future Enhancement)

Rate limiting is planned for future versions and will require business team decisions for proper implementation:

- **Request Limits**: API request limits per time window (requires business policy)
- **Concurrent Limits**: Maximum simultaneous generations per shop (requires capacity planning)
- **Monitoring**: Real-time abuse detection and prevention
- **Tiered Limits**: Different limits based on subscription plans

Rate limiting will be implemented in a future version once business policies are defined.

## WebSocket Events

Real-time job progress updates are sent via Socket.IO:

### Event: `blog-auto-write-progress`
```typescript
interface ProgressEvent {
  jobId: string;
  status: JobStatus;
  progress: number;
  currentStep: string;
  estimatedTimeRemaining: number;
  tokenUsage?: {
    tokensUsedSoFar: number;
    estimatedTotal: number;
  };
}
```

### Event: `blog-auto-write-completed`
```typescript
interface CompletionEvent {
  jobId: string;
  status: 'completed' | 'failed';
  generatedContent?: GeneratedBlogContent;
  errorMessage?: string;
  finalTokenUsage?: {
    actualTokensUsed: number;
    tokensReturned: number;
    finalCost: number;
  };
}
```

## Error Handling

The API uses standard HTTP status codes and returns detailed error information:

```json
{
  "success": false,
  "message": "Error description",
  "error": {
    "code": "ERROR_CODE",
    "details": "Detailed error information"
  }
}
```

### Validation Error Codes

| Error Code | Description | HTTP Status |
|------------|-------------|-------------|
| `FIELD_REQUIRED` | Required field is missing | 400 |
| `FIELD_TOO_SHORT` | Field value is below minimum length | 400 |
| `FIELD_TOO_LONG` | Field value exceeds maximum length | 400 |
| `INVALID_ENUM_VALUE` | Value not in allowed enum options | 400 |
| `INVALID_FORMAT` | Field format doesn't match requirements | 400 |
| `INVALID_INTEGER` | Value is not a valid integer | 400 |
| `INVALID_POSITIVE_NUMBER` | Value is not a positive number | 400 |

### Business Logic Error Codes

| Error Code | Description | HTTP Status |
|------------|-------------|-------------|
| `INSUFFICIENT_CREDITS` | Not enough credits for operation | 402 |
| `JOB_CREATION_FAILED` | Failed to create blog generation job | 500 |
| `CREDIT_ESTIMATION_FAILED` | Failed to estimate credit usage | 500 |
| `CREDIT_VALIDATION_ERROR` | Error validating credit availability | 500 |

### Validation Error Response Example

```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "topic",
      "message": "Topic must be at least 5 characters",
      "code": "FIELD_TOO_SHORT"
    },
    {
      "field": "blogType",
      "message": "Blog type must be one of: Product-based, Guide, Listicle, Informational, News, Seasonal",
      "code": "INVALID_ENUM_VALUE"
    }
  ]
}
```



### Insufficient Credits Error Response Example

```json
{
  "success": false,
  "message": "Insufficient credits",
  "error": {
    "code": "INSUFFICIENT_CREDITS",
    "required": 150,
    "available": 75,
    "shortfall": 75
  },
  "creditInfo": {
    "currentUsage": 925,
    "usageLimit": 1000,
    "availableCredits": 75,
    "estimatedCost": 150
  }
}
```

## SDK Examples

### JavaScript/Node.js
```javascript
const response = await fetch('/api/blog-auto-write', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    topic: 'SEO Best Practices',
    keywords: ['SEO', 'optimization'],
    tone: 'professional',
    wordCount: 1000,
    blogType: 'how_to'
  })
});

const result = await response.json();
console.log('Job ID:', result.jobId);
```

### React Hook Example
```javascript
const useBlogGeneration = () => {
  const [job, setJob] = useState(null);
  
  const generateBlog = async (params) => {
    const response = await blogApi.createJob(params);
    setJob(response.job);
    return response.jobId;
  };
  
  const checkStatus = async (jobId) => {
    const response = await blogApi.getJobStatus(jobId);
    setJob(response.job);
    return response.job;
  };
  
  return { job, generateBlog, checkStatus };
};
```

---

## Changelog

| Version | Date | Changes |
|---------|------|---------|
| 1.0.0 | 2024-12-19 | Initial API specification |

## Support

For API support and questions:
- Documentation: [Link to full documentation]
- Support Email: <EMAIL>
- Developer Portal: [Link to developer resources]
