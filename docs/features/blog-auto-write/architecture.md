# Blog Auto-Write Feature Architecture

## Document Information

| Field | Value |
|-------|-------|
| **Feature Name** | Blog Auto-Write with SEO Optimization |
| **Architecture Version** | 1.6.0 |
| **Created Date** | 2024-12-19 |
| **Last Updated** | 2025-07-10 |
| **Status** | Complete with Feature Flag Support - Runtime Feature Control |
| **Architect** | Development Team |
| **Reviewers** | Senior Architecture Team |

## Architecture Overview

### System Context
The Blog Auto-Write feature integrates into the existing StoreSEO application as a new capability within the Articles optimization module. It uses a "Dummy Article First" approach where draft articles are created immediately in the existing articles table, then populated with AI-generated content. This leverages the existing infrastructure including the articles table, queue system, OpenAI integration, Shopify API connections, and real-time notification system.

### Design Principles
- **Asynchronous Processing**: Long-running AI generation tasks are handled via background queues to maintain responsive UI
- **Progressive Enhancement**: Feature builds upon existing patterns and services without disrupting current functionality
- **Scalable Architecture**: Queue-based processing allows horizontal scaling of content generation workload
- **User Experience First**: Real-time progress updates and clear error handling prioritize user experience
- **Security by Design**: All user inputs are validated, sanitized, and processed securely
- **Concurrency Safe**: Time-based unique identifiers handle multiple concurrent jobs per shop without conflicts
- **Maximum Transparency**: Full 9-status approach provides detailed progress tracking for users
- **Step-by-Step Processing**: Each step is a separate queue message for better cancellation and retry granularity
- **Credit Settlement**: Pre-book credits, track actual usage, settle difference at completion for optimal UX
- **Atomic Operations**: Database updates combine step completion, status updates, and progress in single transactions
- **Runtime Feature Control**: Feature flags provide runtime control over functionality without code deployment
- **Conditional Processing**: System adapts behavior based on feature flag state with easy cleanup path

### Architecture Patterns Used
| Pattern | Application | Rationale |
|---------|-------------|-----------|
| Queue-based Processing | Background blog generation | Handles long-running AI tasks without blocking UI |
| Event-Driven Architecture | Progress notifications | Real-time updates via socket events |
| Repository Pattern | Data access layer | Consistent data access patterns across the application |
| Service Layer Pattern | Business logic encapsulation | Separates business logic from controllers and data access |
| Command Pattern | Job processing | Encapsulates blog generation requests as executable commands |
| Feature Flag Pattern | Runtime feature control | Enables/disables features without code deployment |

## Component Architecture

### Component Diagram
```mermaid
graph TB
    A[Blog Auto-Write Modal] --> B[Blog Auto-Write API]
    B --> C[Blog Auto-Write Service]
    C --> D[Blog Auto-Write Queue]
    D --> E[OpenAI Service]
    D --> F[Article Service]
    D --> G[Shopify Service]
    C --> H[Socket Event Service]
    C --> I[Usage Tracking Service]
    C --> J[Blog Auto-Write Repository]
    J --> K[PostgreSQL Database]
    H --> L[Socket.IO Client]
    L --> A
```

### Component Descriptions

#### Frontend Layer
- **BlogAutoWriteModal**: Main UI component for blog generation interface
- **ProgressTracker**: Real-time progress visualization component
- **ContentPreview**: Generated content preview and basic editing interface
- **BlogGenerationForm**: Input form for blog parameters and preferences

#### API Layer
- **BlogAutoWriteController**: RESTful endpoints for job management
- **BlogAutoWriteRoutes**: Route definitions and middleware configuration
- **ValidationMiddleware**: Input validation and sanitization
- **AuthenticationMiddleware**: User and shop-level authorization

#### Business Logic Layer
- **BlogAutoWriteService**: Core business logic orchestration
- **ContentGenerationService**: AI content generation coordination
- **SEOOptimizationService**: SEO metadata generation and optimization
- **PublishingService**: Shopify blog publishing coordination
- **FeatureFlagService**: Runtime feature control and conditional processing logic

#### Data Layer
- **BlogAutoWriteRepository**: Data access patterns for job management
- **JobStatusTracker**: Job state management and persistence
- **ContentStorage**: Temporary storage for generated content

#### Integration Layer
- **OpenAI Integration**: Extended AI service for blog-specific prompts
- **Shopify Integration**: Article creation and publishing via Admin API
- **Queue Integration**: RabbitMQ message processing
- **Socket Integration**: Real-time progress updates

## Queue Processing Architecture

### Step-by-Step Processing Model

The Blog Auto-Write feature uses a **step-by-step queue processing model** where each step is processed as a separate queue message. This architecture provides superior cancellation detection, retry granularity, and fault tolerance.

#### Processing Flow
```mermaid
graph TD
    A[Job Created] --> B[Queue Message 1]
    B --> C[Execute Step 1: Content Generation]
    C --> D[Complete Step 1 & Update Status]
    D --> E{More Steps?}
    E -->|Yes| F[Queue Message 2]
    E -->|No| G[Complete Job]
    F --> H[Execute Step 2: Shopify Draft]
    H --> I[Complete Step 2 & Update Status]
    I --> J{More Steps?}
    J -->|Yes| K[Queue Message 3...]
    J -->|No| G
```

#### Key Architectural Decisions

**1. Step-by-Step vs Iterative Processing**
- ✅ **Chosen**: Step-by-Step Queue Messages
- ❌ **Rejected**: Iterative Processing in Single Message
- **Rationale**: Better cancellation detection, retry granularity, and fault tolerance

**2. Message Structure**
```javascript
// Simple message structure (no isInitial flag needed)
{
  jobId: 'abc123',
  shopDomain: 'shop.myshopify.com'
}
```

**3. Step Determination**
- Queue processor automatically determines next step using `BlogAutoWriteJobSteps.getNextStep(job.steps)`
- No external flags or step tracking needed in messages
- Self-healing and robust against any job state

#### Benefits of This Architecture

| Benefit | Description | Impact |
|---------|-------------|---------|
| **Real-time Cancellation** | User cancellation detected between steps | < 5 seconds cancellation response |
| **Granular Retry** | Each step can be retried independently | No re-work of expensive completed steps |
| **Fault Tolerance** | Worker crashes don't lose completed work | High reliability and data integrity |
| **Monitoring** | Each step is a separate measurable unit | Better performance analytics |
| **Scalability** | Different workers can handle different step types | Optimized resource utilization |

### Credit Management Architecture

#### Credit Settlement Strategy
```mermaid
graph LR
    A[Job Creation] --> B[Pre-book Estimated Credits]
    B --> C[Process Steps]
    C --> D[Track Actual Usage]
    D --> E[Job Completion]
    E --> F[Calculate Difference]
    F --> G[Apply Settlement]
```

**Key Principles:**
- **Pre-booking**: Credits reserved during job creation for immediate user feedback
- **Usage Tracking**: Actual OpenAI usage tracked per step in job data (no cache updates)
- **Final Settlement**: Single atomic operation at job completion/cancellation/failure
- **Performance**: Eliminates per-step cache updates for better queue throughput

#### Credit Calculation Integration
- Uses `CreditEstimationService.calculateCreditUsage()` for consistency
- Proper CONTENT/IMAGE enum constants for accurate pricing
- Tracks both estimated and actual usage for settlement accuracy
- Comprehensive audit trail for billing transparency

### Database Operations Architecture

#### Atomic Step Completion
```javascript
// Single atomic operation combines:
{
  steps: updatedStepsArray,           // Step completion with timestamps
  status: nextJobStatus,              // Job status for next step
  progress: calculateProgress(steps), // Accurate progress based on completed work
  processing_completed_at: timestamp  // Completion tracking
}
```

**Service Layer Design:**
- **Generic `updateJob(jobId, updateData)`**: Single method for all database updates
- **Dual `getJobById` methods**: Secure (API) vs performant (internal) access patterns
- **Clean separation**: Queue handles business logic, service handles database operations

### Status Synchronization Architecture

**Overview:**
The system implements automatic synchronization between job processing status and article generation status to maintain data consistency across the `blog_auto_write_jobs` and `articles` tables.

**Synchronization Implementation:**

```javascript
// BlogAutoWriteService.updateJob() - Enhanced with automatic synchronization
async updateJob(jobId, updateData) {
  // 1. Update job in database
  const [updatedRowsCount] = await BlogAutoWriteJob.update(updateData, {
    where: { id: jobId },
  });

  if (updatedRowsCount > 0) {
    const updatedJob = await BlogAutoWriteJob.findByPk(jobId);

    // 2. Automatic status synchronization when job status changes
    if (updateData.status && updatedJob?.article_id) {
      await this.syncArticleGenerationStatus(
        updatedJob.article_id,
        updateData.status,
        updatedJob
      );
    }

    return updatedJob?.toJSON() || null;
  }
  return null;
}
```

**Status Mapping Logic:**

```javascript
// Intelligent mapping between job and article statuses
static mapJobStatusToGenerationStatus(jobStatus) {
  const statusMapping = {
    [BlogAutoWriteJobStatus.PENDING]: BlogGenerationStatus.PENDING,
    [BlogAutoWriteJobStatus.GENERATING_CONTENT]: BlogGenerationStatus.GENERATING,
    [BlogAutoWriteJobStatus.CREATING_DRAFT]: BlogGenerationStatus.GENERATING,
    [BlogAutoWriteJobStatus.LINKING_ARTICLE]: BlogGenerationStatus.DRAFT_CREATED,
    [BlogAutoWriteJobStatus.ANALYZING_SEO]: BlogGenerationStatus.CONTENT_READY,
    [BlogAutoWriteJobStatus.GENERATING_IMAGE]: BlogGenerationStatus.CONTENT_READY,
    [BlogAutoWriteJobStatus.UPLOADING_IMAGE]: BlogGenerationStatus.CONTENT_READY,
    [BlogAutoWriteJobStatus.UPDATING_ARTICLE]: BlogGenerationStatus.CONTENT_READY,
    [BlogAutoWriteJobStatus.FINALIZING_SEO]: BlogGenerationStatus.CONTENT_READY,
    [BlogAutoWriteJobStatus.PUBLISHING]: BlogGenerationStatus.PUBLISHING,
    [BlogAutoWriteJobStatus.COMPLETED]: BlogGenerationStatus.DRAFT_PUBLISHED,
    [BlogAutoWriteJobStatus.FAILED]: BlogGenerationStatus.FAILED,
    [BlogAutoWriteJobStatus.CANCELLED]: BlogGenerationStatus.FAILED,
  };
  return statusMapping[jobStatus] || BlogGenerationStatus.PENDING;
}
```

**Smart Progression Logic:**

```javascript
// Prevents status regression while allowing valid progressions
shouldSkipStatusUpdate(currentStatus, newStatus) {
  const statusOrder = {
    [BlogGenerationStatus.MANUAL]: 0,
    [BlogGenerationStatus.PENDING]: 1,
    [BlogGenerationStatus.GENERATING]: 2,
    [BlogGenerationStatus.CONTENT_READY]: 3,
    [BlogGenerationStatus.DRAFT_CREATED]: 4,
    [BlogGenerationStatus.PUBLISHING]: 5,
    [BlogGenerationStatus.PUBLISHED]: 6,
    [BlogGenerationStatus.DRAFT_PUBLISHED]: 6,
    [BlogGenerationStatus.FAILED]: -1, // Always allowed
  };

  const currentOrder = statusOrder[currentStatus] || 0;
  const newOrder = statusOrder[newStatus] || 0;

  // Allow failure status updates and prevent regression
  return newStatus !== BlogGenerationStatus.FAILED && newOrder < currentOrder;
}
```

**Synchronization Features:**

- **Automatic Triggering**: Every job status update triggers article status sync
- **Regression Prevention**: Smart logic prevents status downgrades
- **Special Cases**: Handles completion (autoPublish), failure, and regeneration scenarios
- **Error Resilience**: Sync failures don't block job processing
- **Performance Optimized**: Minimal database queries with efficient status checks

## Data Architecture

### Data Flow Diagram
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant S as Service
    participant Q as Queue
    participant AI as OpenAI
    participant SP as Shopify
    participant D as Database
    participant SO as Socket

    U->>F: Submit Blog Request
    F->>A: POST /blog-auto-write
    A->>S: Create Draft Article
    S->>D: Insert Draft Article
    A->>S: Create Job
    S->>D: Store Job Record (with article_id)
    S->>Q: Dispatch Queue Message
    S->>A: Return Article ID
    A->>F: Article Created Response
    F->>F: Show Article in List

    Q->>AI: Generate Content
    AI-->>Q: Generated Content
    Q->>D: Update Article Content
    Q->>SO: Emit Progress Event
    SO-->>F: Real-time Article Update

    Q->>SP: Publish to Shopify
    SP-->>Q: Shopify Article ID
    Q->>D: Update Article with Shopify ID
    Q->>SO: Emit Completion Event
    SO-->>F: Article Published Notification
```

### Real-time Cancellation Architecture

The system implements intelligent real-time cancellation detection to prevent resource waste and provide immediate user feedback.

#### Cancellation Detection Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant D as Database
    participant Q as Queue Worker
    participant AI as OpenAI API

    Note over U,AI: Normal Processing Flow
    U->>F: Cancel Job Request
    F->>A: DELETE /blog-auto-write/:jobId
    A->>D: Update Job Status to CANCELLED
    A->>F: Cancellation Confirmed
    F->>U: Show Cancelled Status

    Note over Q,AI: Queue Worker Cancellation Detection
    Q->>D: Check Job Status (before expensive operation)
    D-->>Q: Status: CANCELLED
    Q->>Q: Throw JobCancelledException
    Q->>D: Update Credit Usage (partial refund)
    Q->>Q: Clean Exit (no error logging)
```

#### Strategic Cancellation Checkpoints

The queue processor checks for cancellation at strategic points to minimize wasted resources:

```javascript
// Cancellation checkpoints in queue processing:
async processJob(jobId, shopDomain) {
  // ✅ Checkpoint 1: Before starting
  await this.checkJobCancellation(jobId);

  // ✅ Checkpoint 2: Before expensive OpenAI call (30% weight)
  await this.checkJobCancellation(jobId);
  const content = await OpenAiService.generateBlogContent(inputData);

  // ✅ Checkpoint 3: Before Shopify operations
  await this.checkJobCancellation(jobId);
  const draft = await ShopifyService.createDraft(content);

  // ✅ Checkpoint 4: Before image generation (20% weight)
  await this.checkJobCancellation(jobId);
  const image = await OpenAiService.generateImage(description);

  // ... additional checkpoints before each major operation
}
```

#### Performance Optimization

```javascript
// Lightweight cancellation check (only fetches status field)
async checkJobCancellation(jobId) {
  const job = await BlogAutoWriteJob.findByPk(jobId, {
    attributes: ['id', 'status'] // Minimal data fetch
  });

  if (job?.status === BlogAutoWriteJobStatus.CANCELLED) {
    throw new JobCancelledException(`Job ${jobId} cancelled`);
  }
}
```

### Regeneration Architecture

The regeneration feature allows users to restart blog generation while preserving audit trails and maintaining data integrity.

#### Regeneration Workflow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant D as Database
    participant Q as Queue

    U->>F: Click Regenerate
    F->>A: POST /blog-auto-write/:jobId/regenerate

    Note over A,D: Step 1: Cancel Current Job
    A->>D: Update Job Status to CANCELLED
    A->>D: Set article_id to NULL (disconnect)

    Note over A,D: Step 2: Create New Job
    A->>D: Create New Job with Previous Input Data
    A->>D: Link New Job to Target Article

    Note over A,Q: Step 3: Queue New Job
    A->>Q: Dispatch New Job to Queue
    A->>F: Return New Job ID
    F->>U: Show Regeneration Started
```

#### Data Integrity During Regeneration

```sql
-- Before regeneration: Current job linked to article
BlogAutoWriteJob: { id: 'job-1', article_id: 123, status: 'generating_content' }
Article: { id: 123, title: '[Generating] SEO Tips' }

-- After regeneration: Clean separation
BlogAutoWriteJob: { id: 'job-1', article_id: NULL, status: 'cancelled' }  -- Audit trail
BlogAutoWriteJob: { id: 'job-2', article_id: 123, status: 'pending' }     -- New job
Article: { id: 123, title: '[Generating] SEO Tips' }                      -- Same article
```

## Feature Flag Architecture

### Overview
The feature flag system provides runtime control over blog auto-write functionality, enabling temporary disabling of specific features without code deployment. This architecture supports gradual feature rollouts, A/B testing, and emergency feature disabling.

### Feature Flag Service Architecture

```mermaid
graph TB
    A[Environment Variables] --> B[FeatureFlagService]
    B --> C[Backend Services]
    B --> D[Queue Processing]
    B --> E[API Endpoints]
    F[Frontend Hook] --> G[API /feature-flags]
    G --> B
    F --> H[UI Components]

    subgraph "Conditional Processing"
        C --> I{Feature Enabled?}
        I -->|Yes| J[Original Logic]
        I -->|No| K[Filtered Logic]
    end
```

### Implementation Layers

#### 1. Environment Configuration Layer
```bash
# Feature flag environment variables
FEATURE_BLOG_AUTO_WRITE_IMAGE_GENERATION=true|false
```

#### 2. Backend Service Layer
```javascript
class FeatureFlagService {
  static blogs = {
    autoWrite: {
      isImageGenerationEnabled: () => process.env.FEATURE_BLOG_AUTO_WRITE_IMAGE_GENERATION !== 'false',
      getEnabledSteps: () => [...],     // Dynamic step filtering
      getStepWeights: () => ({...})     // Dynamic weight calculation
    }
  }
}
```

#### 3. Frontend Integration Layer
```javascript
// API-driven feature flags
const featureFlags = useFeatureFlags();
const isImageEnabled = featureFlags.blogs.autoWrite.isImageGenerationEnabled();

// Conditional UI rendering
{!flagsLoading && isImageEnabled && (
  <ImageGenerationFields />
)}
```

### Conditional Processing Architecture

The system uses a **conditional-first approach** where feature flags are checked first, then either original or filtered logic is executed:

```javascript
// Job Creation - Conditional Step Selection
steps: FeatureFlagService.blogs.autoWrite.isImageGenerationEnabled()
  ? BlogAutoWriteJobSteps.getDefaultSteps()  // All 9 steps
  : this.getEnabledStepsForJob(),            // Filtered 6 steps

// Queue Processing - Conditional Validation
if (FeatureFlagService.blogs.autoWrite.isImageGenerationEnabled()) {
  // Use original validation (expects all steps)
  const validation = validateStepCompletion(job.steps);
} else {
  // Skip validation (job has filtered steps)
  console.info('Skipping validation - filtered steps due to feature flag');
}
```

### Defense-in-Depth Implementation

```mermaid
graph TD
    A[User Input] --> B[Frontend Layer]
    B --> C{Feature Flag Check}
    C -->|Disabled| D[Hide UI Fields]
    C -->|Enabled| E[Show UI Fields]

    E --> F[API Request]
    D --> F
    F --> G[Middleware Layer]
    G --> H{Feature Flag Check}
    H -->|Disabled| I[Override Input Data]
    H -->|Enabled| J[Pass Through Input]

    I --> K[Service Layer]
    J --> K
    K --> L{Feature Flag Check}
    L -->|Disabled| M[Create Filtered Job]
    L -->|Enabled| N[Create Full Job]

    M --> O[Queue Processing]
    N --> O
    O --> P[Process Job Steps]
```

### Feature Flag Lifecycle Management

#### Phase 1: Feature Flag Introduction
- Add conditional logic with feature flag checks
- Implement both enabled and disabled code paths
- Add helper methods marked for cleanup

#### Phase 2: Feature Stabilization
- Monitor feature performance and user feedback
- Gradually enable for more users
- Collect metrics on feature usage

#### Phase 3: Feature Flag Removal
```javascript
// Remove conditional logic
// From:
steps: FeatureFlagService.blogs.autoWrite.isImageGenerationEnabled()
  ? BlogAutoWriteJobSteps.getDefaultSteps()
  : this.getEnabledStepsForJob(),

// To:
steps: BlogAutoWriteJobSteps.getDefaultSteps(),
```

- Delete helper methods with cleanup notes
- Remove environment variables
- Clean up frontend conditional rendering

### Benefits
- **Runtime Control**: Toggle features without deployment
- **Risk Mitigation**: Quick feature disabling during issues
- **Easy Cleanup**: Conditional logic enables straightforward removal
- **Performance**: Enabled features use original fast paths
- **User Experience**: Seamless regardless of flag state

### Data Models

#### Primary Entities
```typescript
interface BlogAutoWriteJob {
  id: string;
  shopId: number;
  status: JobStatus;
  progress: number;
  inputData: BlogGenerationInput;
  articleId: number | null; // References dummy article (NULL if article deleted)
  errorMessage: string | null;
  estimatedCreditUsage: CreditUsageDetails;
  creditUsage: CreditUsageDetails;
  processingStartedAt: Date | null;
  processingCompletedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

// Enhanced Article model to support AI generation
interface Article {
  // Existing fields
  id: number;
  shopId: number;
  articleId: number | null; // Shopify article ID (null until published)
  blogId: number;
  title: string;
  bodyHtml: string;
  focusKeyword: string;
  score: number;
  issues: number;
  publishedAt: Date | null;

  // New AI generation fields
  generationStatus: GenerationStatus;
  isAiGenerated: boolean;
  generationProgress: number;
  aiOptimizationStatus: string;
}

interface BlogGenerationInput {
  topic: string;
  keywords: string[];
  tone: ContentTone;
  wordCount: number;
  blogType: BlogType;
  targetAudience?: string;
  customInstructions?: string;
}

interface GeneratedBlogContent {
  title: string;
  content: string;
  metaTitle: string;
  metaDescription: string;
  tags: string[];
  seoScore: number;
  readabilityScore: number;
  wordCount: number;
  keywordDensity: Record<string, number>;
}
```

#### Relationships
```mermaid
erDiagram
    SHOP ||--o{ BLOG_AUTO_WRITE_JOB : has
    BLOG ||--o{ ARTICLE : contains
    ARTICLE ||--|| BLOG_AUTO_WRITE_JOB : "has_generation_job"

    SHOP {
        int id PK
        int shop_id UK
    }

    BLOG {
        int id PK
        int shop_id FK
        bigint blog_id UK
    }

    ARTICLE {
        int id PK
        int shop_id FK
        bigint article_id UK "NULL until published to Shopify"
        bigint blog_id FK
        string generation_status "NEW: tracks AI generation state"
        boolean is_ai_generated "NEW: flags AI-generated articles"
        int generation_progress "NEW: 0-100 progress percentage"
    }

    BLOG_AUTO_WRITE_JOB {
        string id PK
        int shop_id FK
        int article_id FK "NULL if article deleted (preserves audit trail)"
        string status
        json token_usage
        json input_data
    }
```

### Data Storage Strategy
| Data Type | Storage Solution | Rationale | Backup Strategy |
|-----------|------------------|-----------|-----------------|
| Job Records | PostgreSQL | ACID compliance, complex queries | Daily automated backups |
| Generated Content | PostgreSQL (JSON) | Structured storage, searchability | Included in DB backups |
| Progress Logs | PostgreSQL | Audit trail, debugging | Included in DB backups |
| Temporary Data | Redis | Fast access, auto-expiration | Not backed up (temporary) |

## Integration Architecture

### System Integration Map
```mermaid
graph LR
    A[Blog Auto-Write] --> B[OpenAI Service]
    A --> C[Article Service]
    A --> D[Shopify Service]
    A --> E[Usage Tracking]
    A --> F[Socket Events]
    A --> G[Queue System]
    
    C --> H[Shopify Admin API]
    D --> H
    E --> I[Redis Cache]
    F --> J[Socket.IO]
    G --> K[RabbitMQ]
```

### Integration Points

#### Internal Integrations
| System | Integration Type | Data Exchange | Error Handling |
|--------|------------------|---------------|----------------|
| OpenAI Service | Direct Method Call | JSON prompts/responses | Retry with exponential backoff |
| Article Service | Direct Method Call | Article data objects | Transaction rollback |
| Queue System | Message Queue | JSON job messages | Dead letter queue |
| Socket Events | Event Emission | Progress update objects | Fire-and-forget with logging |
| Usage Tracking | Direct Method Call | Credit usage numbers | Compensating transactions |

#### External Integrations
| Service | Protocol | Authentication | Rate Limits | SLA |
|---------|----------|----------------|-------------|-----|
| OpenAI API | HTTPS/REST | API Key | 3000 RPM | 99.9% uptime |
| Shopify Admin API | HTTPS/GraphQL | OAuth 2.0 | 2 RPS per shop | 99.95% uptime |

### Message Flow Architecture
```mermaid
graph TD
    A[User Request] --> B[API Validation]
    B --> C[Job Creation]
    C --> D[Queue Dispatch]
    D --> E[Background Processor]
    E --> F[Content Generation]
    F --> G[SEO Optimization]
    G --> H[Shopify Publishing]
    H --> I[Job Completion]
    I --> J[User Notification]
    
    E --> K[Progress Updates]
    K --> L[Socket Events]
    L --> M[Real-time UI Updates]
```

## Progress Tracking Architecture

### Real-Time Progress System
The blog auto-write feature implements a comprehensive progress tracking system that provides users with real-time feedback during the generation process.

#### Frontend Progress Components
```mermaid
graph TB
    A[Articles Table] --> B[ArticleRowItem]
    B --> C[useBlogAutoWriteStatus Hook]
    C --> D[Status Detection Logic]
    D --> E[BlogAutoWriteProgressPopover]
    E --> F[Real-time Data Fetching]
    F --> G[Progress Display]

    H[Backend API] --> I[GET /blog-auto-write/:jobId]
    I --> J[Job Details Response]
    J --> F

    K[5-Second Polling] --> F
    L[Error Handling] --> F
```

#### Progress Tracking Components
| Component | Purpose | Key Features |
|-----------|---------|--------------|
| `BlogAutoWriteProgressPopover` | Main progress display | Real-time polling, step tracking, credit usage |
| `useBlogAutoWriteStatus` | Status detection hook | AI article detection, status formatting, job ID extraction |
| `ArticleRowItem` | Table integration | Conditional rendering, event handling, responsive design |

#### Data Flow for Progress Tracking
```mermaid
sequenceDiagram
    participant U as User
    participant T as Articles Table
    participant P as Progress Popover
    participant A as API
    participant D as Database

    U->>T: Views articles list
    T->>T: Detect AI-generated articles
    T->>T: Show status indicator
    U->>P: Click status indicator
    P->>A: GET /blog-auto-write/:jobId
    A->>D: Query job details
    D-->>A: Job data with steps
    A-->>P: Complete job details
    P->>P: Display progress & steps

    loop Every 5 seconds (if in progress)
        P->>A: Poll for updates
        A->>D: Get latest job state
        D-->>A: Updated job data
        A-->>P: Fresh progress data
        P->>P: Update UI
    end
```

#### Progress Display Features
- **Real-time Updates**: 5-second polling for active jobs
- **Step Tracking**: Detailed progress with completion timestamps
- **Credit Usage**: Estimated vs actual credit consumption
- **Error Handling**: Graceful fallbacks with retry functionality
- **Performance**: Lazy loading and cleanup on close
- **Responsive**: Works on desktop and mobile layouts

#### Status Integration
```typescript
// Status detection and formatting
const autoWriteStatus = useBlogAutoWriteStatus(article);
// Returns: {
//   isAiGenerated: boolean,
//   isInProgress: boolean,
//   status: string,
//   displayStatus: string,
//   statusTone: string,
//   jobId: string,
//   progress: number
// }

// Conditional rendering in articles table
{autoWriteStatus.isAiGenerated ? (
  // Vertical layout: Title + Status with popover
  <BlockStack>
    <Link>{title}</Link>
    <ProgressPopover jobId={autoWriteStatus.jobId} />
  </BlockStack>
) : (
  // Original horizontal layout
  <InlineStack>
    <TitleColumn />
    <StatusBadge />
  </InlineStack>
)}
```

#### Backend Integration
- **Enhanced API Response**: `GET /blog-auto-write/:jobId` returns complete job details
- **Database Relationships**: Articles include `autoWriteJob` data in list queries
- **Enum Synchronization**: Frontend uses actual backend enum labels
- **Single Source of Truth**: All status and step labels from backend enums

## Security Architecture

### Security Layers
| Layer | Security Measures | Implementation |
|-------|-------------------|----------------|
| Frontend | Input validation, XSS prevention | React sanitization, form validation |
| API | Authentication, authorization, rate limiting | JWT validation, role-based access |
| Business Logic | Input sanitization, business rule validation | Service-layer validation |
| Data | Encryption at rest, access controls | PostgreSQL encryption, row-level security |
| Integration | API key management, secure communication | Environment variables, HTTPS only |

### Authentication & Authorization
- **Authentication Method**: JWT tokens with shop and user context
- **Authorization Model**: Shop-level permissions with user role validation
- **Permission Levels**: 
  - Shop Owner: Full access to blog generation
  - Staff: Limited access based on Shopify permissions
  - API Access: Service-to-service authentication

### Data Protection
- **Encryption at Rest**: PostgreSQL transparent data encryption
- **Encryption in Transit**: TLS 1.3 for all API communications
- **Sensitive Data Handling**: 
  - User inputs sanitized and validated
  - Generated content temporarily stored
  - API keys stored in secure environment variables
  - No PII stored beyond necessary retention period

## Performance Architecture

### Performance Requirements
| Metric | Target | Measurement Method |
|--------|--------|--------------------|
| API Response Time | <500ms | Application monitoring |
| Blog Generation Time | 30-120 seconds | Queue processing metrics |
| Concurrent Jobs | 100 simultaneous | Load testing |
| Database Query Time | <100ms | Query performance monitoring |

### Scalability Strategy
- **Horizontal Scaling**: Multiple queue workers for parallel processing
- **Vertical Scaling**: Increased worker memory for complex content generation
- **Caching Strategy**: 
  - Redis for temporary job data
  - Application-level caching for common prompts
  - CDN for static assets
- **Database Optimization**: 
  - Indexed queries on shop_id and status
  - Partitioning by creation date
  - Read replicas for reporting queries

### Performance Monitoring
```mermaid
graph TB
    A[Application Metrics] --> D[Monitoring Dashboard]
    B[Queue Metrics] --> D
    C[Database Metrics] --> D
    D --> E[Alert Manager]
    E --> F[On-call Engineer]
    
    A --> G[Response Times]
    A --> H[Error Rates]
    B --> I[Queue Depth]
    B --> J[Processing Times]
    C --> K[Query Performance]
    C --> L[Connection Pool]
```

## Deployment Architecture

### Environment Strategy
| Environment | Purpose | Configuration | Data |
|-------------|---------|---------------|------|
| Development | Feature development | Local DB, mock APIs | Synthetic test data |
| Staging | Integration testing | Production-like setup | Anonymized production data |
| Production | Live system | Full redundancy | Real user data |

### Deployment Pipeline
```mermaid
graph LR
    A[Code Commit] --> B[Build & Test]
    B --> C[Security Scan]
    C --> D[Deploy to Staging]
    D --> E[Integration Tests]
    E --> F[Performance Tests]
    F --> G[Deploy to Production]
    G --> H[Health Checks]
    H --> I[Monitoring]
```

### Infrastructure Requirements
- **Compute Resources**: 
  - API servers: 2 CPU cores, 4GB RAM per instance
  - Queue workers: 4 CPU cores, 8GB RAM per worker
- **Storage Requirements**: 
  - Database: 100GB initial, 10GB/month growth
  - Redis: 4GB for caching and sessions
- **Network Requirements**: 
  - Outbound HTTPS for OpenAI and Shopify APIs
  - WebSocket connections for real-time updates
- **External Dependencies**: 
  - OpenAI API access
  - Shopify Partner API access
  - RabbitMQ cluster
  - PostgreSQL cluster

## Monitoring & Observability

### Observability Stack
| Component | Tool/Service | Purpose |
|-----------|--------------|---------|
| Logging | Winston + ELK Stack | Centralized log aggregation |
| Metrics | Prometheus + Grafana | Performance and business metrics |
| Tracing | Jaeger | Distributed request tracing |
| Alerting | AlertManager | Incident response |

### Key Metrics
- **Business Metrics**: 
  - Blog generation success rate
  - User adoption and retention
  - Credit usage and revenue impact
  - Content quality scores
- **Technical Metrics**: 
  - API response times and error rates
  - Queue processing times and backlog
  - Database performance and connection health
  - External API success rates and latencies
- **Operational Metrics**: 
  - System resource utilization
  - Error rates and types
  - User session metrics
  - Feature usage patterns

## Evolution Strategy

### Planned Enhancements
| Enhancement | Timeline | Impact | Dependencies |
|-------------|----------|--------|--------------|
| Multi-language Support | Q2 2025 | Medium | OpenAI multilingual models |
| Bulk Generation | Q3 2025 | High | Enhanced queue system |
| Custom AI Training | Q4 2025 | High | ML infrastructure |
| Advanced Editing | Q1 2026 | Medium | Rich text editor integration |

### Architecture Evolution
- **Phase 1**: Current implementation with basic functionality
- **Phase 2**: Enhanced AI capabilities and bulk operations
- **Phase 3**: Custom model training and advanced personalization
- **Phase 4**: Full content management suite integration

### Technical Debt Management
| Debt Item | Priority | Remediation Plan | Timeline |
|-----------|----------|------------------|----------|
| Basic error handling | Medium | Implement comprehensive error taxonomy | Q1 2025 |
| Limited content editing | Low | Integrate rich text editor | Q2 2025 |
| Simple progress tracking | Low | Granular progress indicators | Q1 2025 |

## Decision Records

### Architecture Decision Records (ADRs)

#### ADR-001: Queue-based Processing Architecture
- **Status**: Accepted
- **Context**: Need to handle long-running AI content generation without blocking UI
- **Decision**: Implement asynchronous processing using existing RabbitMQ infrastructure
- **Consequences**: Better user experience, increased system complexity, need for progress tracking

#### ADR-002: Socket.IO for Real-time Updates
- **Status**: Accepted
- **Context**: Users need real-time feedback during blog generation process
- **Decision**: Use existing Socket.IO infrastructure for progress updates
- **Consequences**: Excellent UX, leverages existing patterns, minimal additional complexity

#### ADR-003: PostgreSQL JSON Storage for Generated Content
- **Status**: Accepted
- **Context**: Need flexible storage for varying blog content structures
- **Decision**: Store generated content as JSON in PostgreSQL
- **Consequences**: Flexible schema, good query capabilities, single database system

## Risk Assessment

### Architecture Risks
| Risk | Probability | Impact | Mitigation |
|------|-------------|--------|------------|
| OpenAI API rate limits | High | High | Implement queue throttling, multiple API keys |
| Queue system overload | Medium | High | Auto-scaling workers, circuit breakers |
| Database performance | Low | Medium | Proper indexing, query optimization |
| Socket connection issues | Medium | Low | Graceful degradation, polling fallback |

### Failure Modes
| Failure Mode | Detection | Recovery | Prevention |
|--------------|-----------|----------|------------|
| OpenAI API failure | API error responses | Retry with backoff, queue persistence | Health checks, circuit breakers |
| Queue processing failure | Job timeout, error logs | Dead letter queue, manual retry | Input validation, error handling |
| Database connection loss | Connection errors | Connection pooling, reconnection | Connection monitoring, failover |

---

## Appendices

### A. Detailed Component Specifications
[Links to individual component documentation]

### B. API Specifications
[Reference to api-reference.md]

### C. Database Schema
[Detailed schema documentation with migrations]

### D. Deployment Scripts
[Infrastructure as code and deployment automation]
